-- MySQL dump 10.13  Distrib 5.7.32, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: bnlp
-- ------------------------------------------------------
-- Server version	5.7.32

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `l_load_document`
--

DROP TABLE IF EXISTS `l_load_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `l_load_document` (
  `id` varchar(100) NOT NULL,
  `no` varchar(100) DEFAULT NULL,
  `project_id` bigint(20) NOT NULL,
  `batch_id` bigint(20) NOT NULL,
  `article_id` varchar(30) DEFAULT NULL,
  `document_id` varchar(30) DEFAULT NULL,
  `status` tinyint(2) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `no` (`no`) USING BTREE,
  KEY `batch_id` (`batch_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `l_load_document`
--

LOCK TABLES `l_load_document` WRITE;
/*!40000 ALTER TABLE `l_load_document` DISABLE KEYS */;
/*!40000 ALTER TABLE `l_load_document` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `l_operation`
--

DROP TABLE IF EXISTS `l_operation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `l_operation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `note_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `article_name` varchar(255) DEFAULT NULL COMMENT '文章名',
  `operation` varchar(255) DEFAULT NULL COMMENT '操作模块',
  `type` varchar(255) DEFAULT NULL COMMENT '操作类型',
  `msg` text COMMENT '信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `l_operation`
--

LOCK TABLES `l_operation` WRITE;
/*!40000 ALTER TABLE `l_operation` DISABLE KEYS */;
/*!40000 ALTER TABLE `l_operation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `l_statistics_project`
--

DROP TABLE IF EXISTS `l_statistics_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `l_statistics_project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `invalid` int(11) NOT NULL DEFAULT '0',
  `unmarked` int(11) NOT NULL DEFAULT '0',
  `marked` int(11) NOT NULL DEFAULT '0',
  `reviewed` int(11) NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `l_statistics_project`
--

LOCK TABLES `l_statistics_project` WRITE;
/*!40000 ALTER TABLE `l_statistics_project` DISABLE KEYS */;
/*!40000 ALTER TABLE `l_statistics_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_captcha`
--

DROP TABLE IF EXISTS `sys_captcha`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_captcha` (
  `uuid` char(36) NOT NULL COMMENT 'uuid',
  `code` varchar(6) NOT NULL COMMENT '验证码',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统验证码';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_captcha`
--

LOCK TABLES `sys_captcha` WRITE;
/*!40000 ALTER TABLE `sys_captcha` DISABLE KEYS */;
INSERT INTO `sys_captcha` VALUES ('12f5dd64-08fc-4f49-80f9-be348db46518','c4c2c','2021-03-15 13:45:56'),('151adf82-dbae-4fcf-8902-f410d9307848','e57gm','2021-03-09 19:00:38'),('1f27e0aa-cef5-4cb1-89bb-bef59ec5d5f4','wf4f8','2021-02-25 09:12:43'),('2032f62e-2554-4969-8fa8-974b3ba2c882','pca62','2021-03-05 14:12:03'),('23558fc4-b233-47b6-86c4-75a71a850429','pcay8','2021-02-24 13:45:36'),('273c1352-46a7-4911-8493-093fddae55c6','n3pc5','2021-02-25 09:16:41'),('2bbad14d-ef49-4d72-8082-9b5844fec7f3','68e26','2021-03-03 09:21:55'),('2c6e32bc-8991-4d8b-8af6-ceeddfb4a244','gf3y8','2021-02-24 13:54:45'),('2f1ddcb8-6eef-497e-8cee-0d44f267131b','36wbc','2021-02-25 09:17:51'),('33581f30-3257-486b-88c2-b68f87372ea5','b7nfg','2021-02-25 11:36:00'),('40651e0d-6988-41b1-8f9d-d7931bdc332f','gpww6','2021-02-24 17:25:01'),('49427a57-b796-478a-800c-fe7adaa5f946','x3yn7','2021-02-24 17:24:57'),('4984215d-c2fd-442c-87dd-8165ec49a0a9','6af67','2021-03-07 18:53:56'),('4c62e589-c672-4add-8fae-11cffa86a1e2','223wd','2021-02-25 09:12:35'),('50db585d-1336-4a88-87eb-bc3cfd78f0ba','xd8ne','2021-02-24 17:25:36'),('550e4984-3845-4ffa-8087-91589c187b9e','bmgw6','2021-02-24 15:02:24'),('55a90362-2082-4439-8d0d-99005c7f0c66','7ncfe','2021-02-24 14:30:16'),('5a4ab921-a16e-4c48-8599-c7a717a927e0','n7edp','2021-02-25 09:17:32'),('5e8bab50-5b1a-41e7-8453-71588f635a77','by4wm','2021-02-24 17:16:07'),('5f697efb-81c2-4611-8c86-197c85679eb9','37e4w','2021-02-25 11:09:43'),('65ad176d-6ec3-4e08-878e-60c4f0484fce','ma276','2021-03-15 10:08:53'),('681bf552-c2fc-4288-8250-dc4e888dea8f','p78mg','2021-02-24 14:29:31'),('68a2f4f9-9b25-4242-83c8-b10966223827','wa6pg','2021-03-15 10:19:26'),('6937459c-72a7-4485-8b79-86f52bf6c693','n23fy','2021-02-24 14:48:28'),('7007e33d-86ec-49d4-8e02-2c8161915f9a','nwc3w','2021-02-24 17:18:48'),('711def12-e4fc-4387-8f7c-03c3dd488cf4','d67y5','2021-03-02 09:25:02'),('759e7ae6-7504-42a4-8316-ef06a8f9e42d','e2p48','2021-03-09 09:30:36'),('83310ce4-64e2-4452-81d3-85e0a1b0723e','w4f2m','2021-03-15 10:09:32'),('83c0bb70-359e-4891-8fe1-b6d0c83d8389','bnfba','2021-02-24 14:27:44'),('89eb875e-02f6-44d1-8b85-9c8a727ffe26','pbeb8','2021-03-15 15:04:06'),('8f4c7736-b449-4df7-8be0-6b962a104638','n7p3c','2021-02-24 17:23:23'),('93997863-c476-4ec1-86d2-5e8decc4e568','b4ye8','2021-02-24 14:53:35'),('99d4faea-630a-4cdf-8eb0-f357907a2632','gxbff','2021-02-24 17:14:20'),('af1f3155-be70-4277-8b8c-f916fc3b1b3a','cxwy2','2021-02-25 14:51:44'),('aff8b1ed-39a4-4ce7-8d70-398c6c718c75','wpp6m','2021-03-15 13:44:04'),('b33c9c51-ec7e-4d11-8a0a-aec357ec0959','f4m88','2021-03-02 09:29:11'),('b403d89d-3e6b-40d9-8649-32362edee713','nw72a','2021-03-11 09:27:16'),('b40b8134-2860-4984-8a94-36c48852824f','xd86a','2021-03-15 13:45:20'),('b5dc280f-e440-4b88-86f0-9726f23c001a','agyfw','2021-02-24 17:23:40'),('b9bbbaf3-72e4-4cbc-834c-865927d81b35','7wyyb','2021-03-12 16:17:52'),('ba566caa-c607-4c32-8860-ded00d5b4aca','p2fxd','2021-03-07 18:53:50'),('bbc82124-cdc1-409f-8bc7-b12f97d7f5b1','2df7m','2021-02-24 14:42:01'),('c630b2ba-e130-43bf-842e-74c853b7feec','mdf2c','2021-02-24 13:54:13'),('cb8fae05-4ed7-4afc-8a02-7a281879637b','n2c32','2021-03-05 14:12:55'),('d0c40e12-c6a2-42f6-8c97-55b9cf45f829','y37ee','2021-02-25 09:09:52'),('d7da087d-18ab-4efa-8f69-6b382d7e6d4b','ge5nf','2021-02-25 09:12:32'),('df2dbb9f-d12b-4fde-8002-552d7b35b466','3b86p','2021-03-14 20:05:42'),('e48fff3d-1063-463d-89c2-2c90798f868c','b6cn7','2021-03-15 10:08:34'),('e5f8df0c-8c7e-41d5-82b4-14e19bee0b9f','5cbfe','2021-03-15 13:45:13'),('ee866c11-e83f-48ed-848d-620d08409572','wgfem','2021-02-24 17:23:43'),('f02e179e-48a4-407f-8a35-01eab871844f','3xbpb','2021-02-24 17:18:48'),('f315aaf6-7228-4c19-8cd6-e1bebccfcf77','484ma','2021-02-24 14:31:35'),('f735dc9f-5857-482d-83ca-8a5defb8fef9','37m42','2021-02-24 17:22:46'),('f942bbb7-67a7-4aea-8331-972c445e19c4','8cbgn','2021-03-02 09:26:56');
/*!40000 ALTER TABLE `sys_captcha` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `param_key` varchar(50) DEFAULT NULL COMMENT 'key',
  `param_value` varchar(2000) DEFAULT NULL COMMENT 'value',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态   0：隐藏   1：显示',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `param_key` (`param_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log`
--

DROP TABLE IF EXISTS `sys_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation` varchar(50) DEFAULT NULL COMMENT '用户操作',
  `method` varchar(200) DEFAULT NULL COMMENT '请求方法',
  `params` varchar(5000) DEFAULT NULL COMMENT '请求参数',
  `time` bigint(20) NOT NULL COMMENT '执行时长(毫秒)',
  `ip` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='系统日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log`
--

LOCK TABLES `sys_log` WRITE;
/*!40000 ALTER TABLE `sys_log` DISABLE KEYS */;
INSERT INTO `sys_log` VALUES (1,'admin','删除角色','org.biosino.nlp.modules.sys.controller.SysRoleController.delete()','[[7]]',8,'0:0:0:0:0:0:0:1','2021-01-29 15:22:01'),(2,'admin','保存用户','org.biosino.nlp.modules.sys.controller.SysUserController.save()','[{\"userId\":14,\"username\":\"Luo Ruijin\",\"password\":\"dad11015bffd87fb21b1cc12875833eef22a4f0ae0e65de7ba78fc726d2e0bc6\",\"salt\":\"LYjeJxcC0LnHDYcmxmvg\",\"email\":\"<EMAIL>\",\"mobile\":\"18485520864\",\"status\":1,\"roleIdList\":[2,3,8],\"createUserId\":1,\"createTime\":\"Jan 29, 2021 3:22:44 PM\"}]',119,'0:0:0:0:0:0:0:1','2021-01-29 15:22:44'),(3,'admin','保存用户','org.biosino.nlp.modules.sys.controller.SysUserController.save()','[{\"userId\":15,\"username\":\"lrj\",\"password\":\"39ed1d70c18c04e52e47538702e09b105bf92ded2e2dd1dfab0d4b80f4f749b2\",\"salt\":\"Ds1zXItUEnQ1HaKPdML0\",\"email\":\"<EMAIL>\",\"mobile\":\"18111111111\",\"status\":1,\"roleIdList\":[3],\"createUserId\":1,\"createTime\":\"Jan 29, 2021 3:23:35 PM\"}]',18,'0:0:0:0:0:0:0:1','2021-01-29 15:23:35'),(4,'admin','保存用户','org.biosino.nlp.modules.sys.controller.SysUserController.save()','[{\"userId\":16,\"username\":\"lrj-bz\",\"password\":\"61260c3ce3716830f94e1cd7ca53e7787e3ebe128679d1dcdc0b27d6da67bd1b\",\"salt\":\"rOHzTit0YFpArqArwr4b\",\"email\":\"<EMAIL>\",\"mobile\":\"11111111111\",\"status\":1,\"roleIdList\":[2],\"createUserId\":1,\"createTime\":\"Jan 29, 2021 3:24:01 PM\"}]',15,'0:0:0:0:0:0:0:1','2021-01-29 15:24:01'),(5,'admin','保存用户','org.biosino.nlp.modules.sys.controller.SysUserController.save()','[{\"userId\":2,\"username\":\"lrj\",\"password\":\"6b9f1f64d63420767c5913a5dfe9e89b46e70ad8be413ac2d7f5552744e787a4\",\"salt\":\"kMc2DWZCYpV8TA8CFlhC\",\"email\":\"<EMAIL>\",\"mobile\":\"18485520864\",\"status\":1,\"roleIdList\":[2,3,8],\"createUserId\":1,\"createTime\":\"Feb 23, 2021 11:09:00 AM\"}]',121,'219.138.227.208','2021-02-23 11:09:00'),(6,'lrj','修改密码','org.biosino.nlp.modules.sys.controller.SysUserController.password()','[{\"password\":\"111111\",\"newPassword\":\"123456\"}]',51,'0:0:0:0:0:0:0:1','2021-02-25 09:03:31'),(7,'lrj','修改密码','org.biosino.nlp.modules.sys.controller.SysUserController.password()','[{\"password\":\"123456\",\"newPassword\":\"111111\"}]',46,'0:0:0:0:0:0:0:1','2021-02-25 09:03:59'),(8,'admin','修改菜单','org.biosino.nlp.modules.sys.controller.SysMenuController.update()','[{\"menuId\":58,\"parentId\":0,\"name\":\"chart\",\"url\":\"annotation/components/diagram\",\"type\":1,\"orderNum\":0}]',51,'0:0:0:0:0:0:0:1','2021-03-01 18:51:06'),(9,'admin','修改角色','org.biosino.nlp.modules.sys.controller.SysRoleController.update()','[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"超级管理员，可以管理系统\",\"createUserId\":1,\"menuIdList\":[2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,27,29,41,42,54,61,58,-666666,1]}]',1362,'0:0:0:0:0:0:0:1','2021-03-01 18:51:24'),(10,'admin','修改菜单','org.biosino.nlp.modules.sys.controller.SysMenuController.update()','[{\"menuId\":58,\"parentId\":0,\"name\":\"chart\",\"url\":\"annotation/diagram\",\"type\":1,\"orderNum\":0}]',46,'0:0:0:0:0:0:0:1','2021-03-01 18:52:41'),(11,'admin','修改菜单','org.biosino.nlp.modules.sys.controller.SysMenuController.update()','[{\"menuId\":58,\"parentId\":0,\"name\":\"chart\",\"url\":\"annotation/diagram\",\"type\":1,\"icon\":\"articlelist\",\"orderNum\":0}]',46,'0:0:0:0:0:0:0:1','2021-03-01 18:56:01'),(12,'admin','修改菜单','org.biosino.nlp.modules.sys.controller.SysMenuController.update()','[{\"menuId\":58,\"parentId\":0,\"name\":\"relation-1\",\"url\":\"annotation/diagram\",\"type\":1,\"icon\":\"articlelist\",\"orderNum\":0}]',50,'0:0:0:0:0:0:0:1','2021-03-01 20:54:22'),(13,'admin','保存菜单','org.biosino.nlp.modules.sys.controller.SysMenuController.save()','[{\"menuId\":62,\"parentId\":0,\"name\":\"relation-2\",\"url\":\"annotation/diagram2\",\"perms\":\"\",\"type\":1,\"icon\":\"relation\",\"orderNum\":0}]',55,'0:0:0:0:0:0:0:1','2021-03-01 20:54:37'),(14,'admin','修改角色','org.biosino.nlp.modules.sys.controller.SysRoleController.update()','[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"超级管理员，可以管理系统\",\"createUserId\":1,\"menuIdList\":[2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,27,29,41,42,54,61,58,62,-666666,1]}]',1380,'0:0:0:0:0:0:0:1','2021-03-01 20:54:57'),(15,'admin','修改角色','org.biosino.nlp.modules.sys.controller.SysRoleController.update()','[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"超级管理员，可以管理系统\",\"createUserId\":1,\"menuIdList\":[2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,27,29,41,42,54,61,-666666,1]}]',135,'219.138.227.208','2021-03-14 20:01:30');
/*!40000 ALTER TABLE `sys_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父菜单ID，一级菜单为0',
  `name` varchar(50) DEFAULT NULL COMMENT '菜单名称',
  `url` varchar(200) DEFAULT NULL COMMENT '菜单URL',
  `perms` varchar(500) DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
  `type` int(11) DEFAULT NULL COMMENT '类型   0：目录   1：菜单   2：按钮',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `order_num` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COMMENT='菜单管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,0,'系统管理',NULL,NULL,0,'system',10),(2,1,'用户列表','sys/user',NULL,1,'admin',1),(3,1,'角色管理','sys/role',NULL,1,'role',2),(4,1,'菜单管理','sys/menu',NULL,1,'menu',3),(5,1,'SQL监控','http://localhost:8080/nlp/druid/sql.html',NULL,1,'sql',4),(15,2,'查看',NULL,'sys:user:list,sys:user:info',2,NULL,0),(16,2,'新增',NULL,'sys:user:save,sys:role:select',2,NULL,0),(17,2,'修改',NULL,'sys:user:update,sys:role:select',2,NULL,0),(18,2,'删除',NULL,'sys:user:delete',2,NULL,0),(19,3,'查看',NULL,'sys:role:list,sys:role:info',2,NULL,0),(20,3,'新增',NULL,'sys:role:save,sys:menu:list',2,NULL,0),(21,3,'修改',NULL,'sys:role:update,sys:menu:list',2,NULL,0),(22,3,'删除',NULL,'sys:role:delete',2,NULL,0),(23,4,'查看',NULL,'sys:menu:list,sys:menu:info',2,NULL,0),(24,4,'新增',NULL,'sys:menu:save,sys:menu:select',2,NULL,0),(25,4,'修改',NULL,'sys:menu:update,sys:menu:select',2,NULL,0),(26,4,'删除',NULL,'sys:menu:delete',2,NULL,0),(27,1,'参数管理','sys/config','sys:config:list,sys:config:info,sys:config:save,sys:config:update,sys:config:delete',1,'config',6),(29,1,'系统日志','sys/log','sys:log:list',1,'log',7),(30,1,'文件上传','oss/oss','sys:oss:all',1,'oss',6),(31,0,'文献标注','','',0,'log',1),(39,31,'标注页面','annotation/info','',1,'bianji',1),(41,0,'标签管理','','',0,'labels',2),(42,41,'实体标签','labels/entity','label:delete,label:update,label:save,label:info,label:list',1,'entity-label',0),(44,0,'项目管理','project/list','project:manage',1,'project',1),(47,31,'Meta','annotation/meta','',1,'data',2),(48,31,'参考文献','annotation/pdf','',1,'pdf',3),(49,31,'说明文档','annotation/explain','',1,'log',4),(50,31,'操作记录','annotation/log','',1,'edit-log',5),(51,0,'JSON','','',0,'bianji',0),(52,51,'Playground','je/json-editor','',1,'editor',0),(54,41,'属性标签','labels/attribute','label:list,label:info,label:save,label:update,label:delete',1,'bianji',0),(58,0,'relation-1','annotation/diagram','',1,'articlelist',0),(59,0,'标注记录','annotation/anno-history','',1,'bianji',2),(60,0,'标注说明','instruction/project','annotator',1,'pdf',5),(61,41,'关系标签','labels/relation','label:list,label:info,label:save,label:update,label:delete',1,'relation',0),(62,0,'relation-2','annotation/diagram2','',1,'relation',0);
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(100) DEFAULT NULL COMMENT '角色名称',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='角色';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','超级管理员，可以管理系统',1,'2020-11-15 19:30:43'),(2,'标注员','标注员',1,'2020-12-01 09:10:53'),(3,'审核员','审核员',1,'2020-12-01 09:11:20'),(8,'项目管理员','项目管理员',1,'2020-12-06 14:39:51');
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `menu_id` bigint(20) DEFAULT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1190 DEFAULT CHARSET=utf8mb4 COMMENT='角色与菜单对应关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (1017,8,44),(1019,8,-666666),(1020,2,39),(1021,2,48),(1022,2,50),(1024,2,59),(1025,2,60),(1026,2,-666666),(1027,2,31),(1028,3,39),(1029,3,48),(1030,3,50),(1032,3,59),(1033,3,60),(1034,3,-666666),(1035,3,31),(1167,1,2),(1168,1,15),(1169,1,16),(1170,1,17),(1171,1,18),(1172,1,3),(1173,1,19),(1174,1,20),(1175,1,21),(1176,1,22),(1177,1,4),(1178,1,23),(1179,1,24),(1180,1,25),(1181,1,26),(1182,1,27),(1183,1,29),(1184,1,41),(1185,1,42),(1186,1,54),(1187,1,61),(1188,1,-666666),(1189,1,1);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `salt` varchar(20) DEFAULT NULL COMMENT '盐',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(100) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态  0：禁用   1：正常',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='系统用户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,'admin','9ec9750e709431dad22365cabc5c625482e574c74adaebba7dd02f1129e4ce1d','YzcmCZNvbXocrsz9dm8e','<EMAIL>','13600000000',1,1,'2016-11-11 11:11:11'),(2,'test','6b9f1f64d63420767c5913a5dfe9e89b46e70ad8be413ac2d7f5552744e787a4','kMc2DWZCYpV8TA8CFlhC','<EMAIL>','18485520864',1,1,'2021-02-23 11:09:00');
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='用户与角色对应关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,3,1),(2,7,1),(3,1,1),(4,2,2),(5,2,3),(6,2,8);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_token`
--

DROP TABLE IF EXISTS `sys_user_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user_token` (
  `user_id` bigint(20) NOT NULL,
  `token` varchar(100) NOT NULL COMMENT 'token',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `token` (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户Token';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_token`
--

LOCK TABLES `sys_user_token` WRITE;
/*!40000 ALTER TABLE `sys_user_token` DISABLE KEYS */;
INSERT INTO `sys_user_token` VALUES (1,'6921a15de42bc7b437909916d21be926','2021-03-16 07:42:58','2021-03-15 19:42:58'),(2,'0e8705c0b830443750ba91b922f0ddee','2021-03-16 07:42:47','2021-03-15 19:42:47');
/*!40000 ALTER TABLE `sys_user_token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_attachment`
--

DROP TABLE IF EXISTS `t_attachment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `deleted` tinyint(2) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_attachment`
--

LOCK TABLES `t_attachment` WRITE;
/*!40000 ALTER TABLE `t_attachment` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_attachment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_attribute_label`
--

DROP TABLE IF EXISTS `t_attribute_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_attribute_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父标签id',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '层级',
  `field` varchar(255) NOT NULL COMMENT '标签代码',
  `title` varchar(255) NOT NULL COMMENT '标签名称',
  `order_num` int(11) DEFAULT NULL COMMENT '排序字段',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '停启状态(1是正常，0是禁用)',
  `project_id` int(11) NOT NULL DEFAULT '0' COMMENT '0是公共标签，其他是私有标签',
  `form_data` json DEFAULT NULL COMMENT '表格数据',
  `schema_data` json DEFAULT NULL COMMENT '模板',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `deleted` int(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1代表删除 0代表未删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `pid` (`pid`) USING BTREE,
  KEY `level` (`level`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_attribute_label`
--

LOCK TABLES `t_attribute_label` WRITE;
/*!40000 ALTER TABLE `t_attribute_label` DISABLE KEYS */;
INSERT INTO `t_attribute_label` VALUES (1,0,1,'ref_mb','Ref_MB',1,1,0,'{}','{}','2021-01-08 20:10:11',0),(2,0,1,'phe','Phe',2,1,0,'{}','{}','2021-01-08 20:10:11',0),(3,0,1,'general','General',3,1,0,'{}','{}','2021-01-08 20:10:11',0),(4,0,1,'origin','Origin',4,1,0,'{}','{}','2021-01-08 20:10:11',0),(5,0,1,'mor','Mor',5,1,0,'{}','{}','2021-01-08 20:10:11',0),(6,1,2,'reference','Reference',1,1,0,'{}','{\"type\": \"object\", \"ui:order\": [\"name\", \"taxon_id\", \"lmsg_id\", \"doi\", \"title\"], \"properties\": {\"doi\": {\"type\": \"string\", \"title\": \"DOI\"}, \"name\": {\"type\": \"string\", \"title\": \"name\"}, \"title\": {\"type\": \"string\", \"title\": \"Title\"}, \"lmsg_id\": {\"type\": \"string\", \"title\": \"LMSG_ID\"}, \"taxon_id\": {\"type\": \"string\", \"title\": \"TaxonID\"}}}','2021-01-08 20:10:11',0),(7,1,2,'molecular_biology','Molecular Biology',2,1,0,'{}','{\"type\": \"object\", \"ui:order\": [\"genome_assembly_id\", \"primary_acc\", \"jgi_img_id\", \"gene\"], \"properties\": {\"gene\": {\"type\": \"string\", \"title\": \"Gene\"}, \"jgi_img_id\": {\"type\": \"string\", \"title\": \"JGI IMG ID\"}, \"primary_acc\": {\"type\": \"string\", \"title\": \"16SrRNA Primary_acc\"}, \"genome_assembly_id\": {\"type\": \"string\", \"title\": \"Genome Assembly ID\"}}}','2021-01-08 20:10:11',0),(8,2,2,'phenotype','Phenotype',1,1,0,'{}','{\"type\": \"object\", \"ui:order\": [\"bergey_description\", \"emendation\"], \"properties\": {\"emendation\": {\"type\": \"string\", \"title\": \"Emendation\"}, \"bergey_description\": {\"type\": \"string\", \"title\": \"Bergey Description\"}}}','2021-01-08 20:10:11',0),(9,3,2,'general_information','General information',1,1,0,'{}','{\"type\": \"object\", \"ui:order\": [\"full_name\", \"rank\", \"type\", \"strain\", \"etymology\", \"basonym\", \"synonym\", \"vp\", \"ep\"], \"properties\": {\"ep\": {\"type\": \"string\", \"title\": \"EP\"}, \"vp\": {\"type\": \"string\", \"title\": \"VP\"}, \"rank\": {\"type\": \"string\", \"title\": \"Rank\"}, \"type\": {\"type\": \"string\", \"title\": \"Type\"}, \"strain\": {\"type\": \"string\", \"title\": \"Strain\"}, \"basonym\": {\"type\": \"string\", \"title\": \"Basonym\"}, \"synonym\": {\"type\": \"string\", \"title\": \"Synonym\"}, \"etymology\": {\"type\": \"string\", \"title\": \"Etymology\"}, \"full_name\": {\"type\": \"string\", \"title\": \"Full Name\"}}}','2021-01-08 20:10:11',0),(10,4,2,'isolation_and_sampling','Isolation and sampling',1,1,0,'{\"indole_production\": [{\"en_cul_name\": \"\", \"en_cul_temp\": \"\", \"sample_date\": \"\", \"sample_type\": \"\", \"enri_cul_dur\": \"\", \"sample_source\": \"\", \"host_body_site\": \"\", \"isolation_date\": \"\", \"procedure_origin\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"indole_production\"], \"properties\": {\"indole_production\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"sample_type\", \"label\": \"Sample Type\"}, {\"name\": \"sample_source\", \"label\": \"Sample Source\"}, {\"name\": \"host_body_site\", \"label\": \"Host Body-Site\"}, {\"name\": \"procedure_origin\", \"label\": \"Procedure Origin\"}, {\"name\": \"en_cul_name\", \"label\": \"En_cul_name\"}, {\"name\": \"en_cul_temp\", \"label\": \"En_cul_temp\"}, {\"name\": \"enri_cul_dur\", \"label\": \"Enri_cul_dur\"}, {\"name\": \"sample_date\", \"label\": \"Sample Date\"}, {\"name\": \"isolation_date\", \"label\": \"Isolation Date\"}]}}}}','2021-01-08 20:10:11',0),(11,4,2,'environmental_information','Environmental Information',1,1,0,'{\"environmental_information\": [{\"country\": \"\", \"latitude\": \"\", \"continent\": \"\", \"longitude\": \"\", \"ge_lo_name\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"environmental_information\"], \"properties\": {\"environmental_information\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"ge_lo_name\", \"label\": \"Ge_lo_name\"}, {\"name\": \"longitude\", \"label\": \"Longitude\"}, {\"name\": \"latitude\", \"label\": \"Latitude\"}, {\"name\": \"country\", \"label\": \"Country\"}, {\"name\": \"continent\", \"label\": \"Continent\"}]}}}}','2021-01-08 20:10:11',0),(12,5,2,'cell','Cell',1,1,0,'{\"values\": {\"motility\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"\"}], \"cell_list\": [{}], \"environmental_information\": [{\"width\": \"\", \"length\": \"\", \"diameter\": \"\", \"cell_shape\": \"\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"values\"], \"properties\": {\"values\": {\"type\": \"object\", \"required\": [], \"ui:order\": [\"growth_rate\", \"environmental_information\", \"motility\", \"flagellum\"], \"properties\": {\"motility\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Motility\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"flagellum\": {\"type\": \"string\", \"title\": \"Flagellum\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"growth_rate\": {\"type\": \"string\", \"title\": \"Growth Rate\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"environmental_information\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"cell_shape\", \"label\": \"Cell Shape\"}, {\"name\": \"length\", \"label\": \"Length\"}, {\"name\": \"width\", \"label\": \"Width\"}, {\"name\": \"diameter\", \"label\": \"Diameter\"}]}}}}}}','2021-01-08 20:10:11',0),(13,5,2,'smp','SMP',1,1,0,'{\"murein\": {}, \"pigmentation\": {}, \"spore_formation_ability\": {\"ability\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"spore_formation_ability\", \"murein\", \"pigmentation\"], \"properties\": {\"murein\": {\"type\": \"object\", \"title\": \"Murein\", \"required\": [], \"ui:order\": [\"short_index\", \"types\"], \"properties\": {\"types\": {\"type\": \"string\", \"title\": \"Types\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"short_index\": {\"type\": \"string\", \"title\": \"Short Index\", \"ui:options\": {\"placeholder\": \"请输入\"}}}}, \"pigmentation\": {\"type\": \"object\", \"title\": \"Pigmentation\", \"required\": [], \"ui:order\": [\"name\", \"color\"], \"properties\": {\"name\": {\"type\": \"string\", \"title\": \"Name\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"color\": {\"type\": \"string\", \"title\": \"Color\", \"ui:options\": {\"placeholder\": \"请输入\"}}}}, \"spore_formation_ability\": {\"type\": \"object\", \"title\": \"Spore Formation Ability\", \"required\": [], \"ui:order\": [\"ability\", \"type\", \"description\"], \"properties\": {\"type\": {\"type\": \"string\", \"title\": \"Type\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"ability\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Ability\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"others\"}]}}, \"description\": {\"type\": \"string\", \"title\": \"Description\", \"ui:options\": {\"placeholder\": \"请输入\"}}}}}}','2021-01-08 20:10:11',0),(14,5,2,'multicellular','Multicellular',1,1,0,'{\"indole_production\": [{\"ability\": \"\", \"description\": \"\", \"medium_name\": \"\", \"multicell_size\": \"\", \"multicell_color\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"indole_production\"], \"properties\": {\"indole_production\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"ability\", \"type\": \"mycomponent\", \"label\": \"Sample Type\", \"btnList\": false, \"columns\": [{\"label\": \"true\", \"value\": \"true\"}, {\"elm\": {}, \"label\": \"false\", \"value\": \"false\"}, {\"label\": \"Unknown\", \"value\": \"unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"others\"}]}, {\"name\": \"medium_name\", \"label\": \"Multicell Name\"}, {\"name\": \"multicell_size\", \"label\": \"Multicell Size\"}, {\"name\": \"multicell_color\", \"label\": \"Multicell Color\"}, {\"name\": \"description\", \"label\": \"Description\"}]}}}}','2021-01-19 07:52:04',0),(15,5,2,'colony','Colony',1,1,0,'{\"indole_production\": [{\"medium_name\": \"\", \"colony_color\": \"\", \"colony_shape\": \"\", \"colony_length\": \"\", \"hemolysis_type\": \"\", \"incubation_period\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"indole_production\"], \"properties\": {\"indole_production\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"colony_length\", \"label\": \"Colony Length\"}, {\"name\": \"colony_color\", \"label\": \"Colony Color\"}, {\"name\": \"colony_shape\", \"label\": \"Colony Shape\"}, {\"name\": \"medium_name\", \"label\": \"Medium Name\"}, {\"name\": \"hemolysis_ability\", \"type\": \"mycomponent\", \"label\": \"Hemolysis Ability\", \"columns\": [{\"label\": \"Positive\", \"value\": \"positive\"}, {\"label\": \"Negative\", \"value\": \"negative\"}, {\"label\": \"Unknown\", \"value\": \"unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"others\"}]}, {\"name\": \"hemolysis_type\", \"label\": \"Hemolysis Type\"}, {\"name\": \"incubation_period\", \"label\": \"Incubation Period\"}]}}}}','2021-01-19 07:57:05',0),(16,0,1,'biochem','Biochem',6,1,0,'{}','{}','2021-01-19 08:02:59',0),(17,0,1,'phy','Phy',7,1,0,'{}','{}','2021-01-19 08:03:13',0),(18,0,1,'enzym','Enzym',8,1,0,'{}','{}','2021-01-19 08:03:25',0),(19,16,2,'biochem','Biochem',1,1,0,'{\"spore_formation_ability\": {\"voges_proskauer\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"milk_coagulation\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"indole_production\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"nitrate_reduction\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"gelatin_hydrolysis\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"milk_peptonization\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"citrate_utilization\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}], \"methyl_red_reaction\": [{\"value\": \"1\", \"inputShow\": false, \"inputValue\": \"1\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"spore_formation_ability\"], \"properties\": {\"spore_formation_ability\": {\"type\": \"object\", \"title\": \"Spore Formation Ability\", \"required\": [], \"ui:order\": [\"indole_production\", \"voges_proskauer\", \"citrate_utilization\", \"methyl_red_reaction\", \"nitrate_reduction\", \"milk_peptonization\", \"milk_coagulation\", \"gelatin_hydrolysis\", \"H2S_production\", \"polar_lipids\", \"major_quinones\", \"hydrolysates\", \"cell_wall_sugar\", \"cell_wall_aa\", \"mycolic_acids\", \"pyrolysis_esters\"], \"properties\": {\"cell_wall_aa\": {\"type\": \"string\", \"title\": \"Cell-Wall aa\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"hydrolysates\": {\"type\": \"string\", \"title\": \"Hydrolysates\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"polar_lipids\": {\"type\": \"string\", \"title\": \"PolarLipids\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"mycolic_acids\": {\"type\": \"string\", \"title\": \"Mycolic Acids\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"H2S_production\": {\"type\": \"string\", \"title\": \"H2SProduction\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"major_quinones\": {\"type\": \"string\", \"title\": \"MajorQuinones\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"cell_wall_sugar\": {\"type\": \"string\", \"title\": \"Cell-Wall Sugar\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"voges_proskauer\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"VogesProskauer\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"milk_coagulation\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Milk Coagulation\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"pyrolysis_esters\": {\"type\": \"string\", \"title\": \"PyrolysisEsters\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"indole_production\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"nitrate_reduction\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Nitrate Reduction\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"gelatin_hydrolysis\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Gelatin Hydrolysis\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"milk_peptonization\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Milk Peptonization\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"citrate_utilization\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Citrate Utilization\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"methyl_red_reaction\": {\"type\": \"string\", \"ui:field\": \"my-select\", \"ui:title\": \"Methyl Red Reaction\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"+\", \"value\": \"1\"}, {\"name\": \"-\", \"value\": \"2\"}, {\"name\": \"+/-\", \"value\": \"3\"}, {\"name\": \"Unknown\", \"value\": \"4\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}}}}}','2021-01-19 08:04:47',0),(20,17,2,'nutrition','Nutrition',1,1,0,'{\"value\": {\"cn_source\": {\"cn_source\": [{\"ability\": \"Positive\", \"metabolite\": \"\"}]}, \"gram_staining\": [{\"value\": \"Positive\", \"inputShow\": false, \"inputValue\": \"\"}], \"oxygen_require\": [\"\"]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"value\"], \"properties\": {\"value\": {\"type\": \"object\", \"required\": [], \"ui:order\": [\"nutrition_type\", \"cn_source\", \"lysis\", \"oxygen_require\", \"gram_staining\"], \"properties\": {\"lysis\": {\"type\": \"string\", \"title\": \"Lysis\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"cn_source\": {\"type\": \"object\", \"title\": \"C/N Source\", \"required\": [], \"ui:order\": [\"cn_source\"], \"properties\": {\"cn_source\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"fieldColumn\": [{\"name\": \"metabolite\", \"type\": \"input\", \"label\": \"Metabolite\"}, {\"name\": \"ability\", \"type\": \"select\", \"label\": \"Ability\", \"btnList\": false, \"columns\": [{\"label\": \"Positive\", \"value\": \"Positive\"}, {\"label\": \"Negative\", \"value\": \"Negative\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\"}]}]}}}}, \"gram_staining\": {\"ui:field\": \"my-select\", \"ui:title\": \"Gram Staining\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"Positive\", \"value\": \"Positive\"}, {\"name\": \"Negative\", \"value\": \"Negative\"}, {\"name\": \"Unknown\", \"value\": \"Unknown\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"nutrition_type\": {\"type\": \"string\", \"title\": \"Nutrition Type\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"oxygen_require\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"title\": \"Oxygen Require\", \"ui:widget\": \"input-array\"}}}}}','2021-01-19 08:22:10',0),(21,17,2,'gcpath','GC_Path',1,1,0,'{\"gc_path\": {\"cn_source\": {\"n_source\": [{\"ability\": \"Positive\", \"metabolite\": \"\"}]}, \"nutrition_type\": {\"pathogenicity\": {\"plant\": [{\"value\": \"True\", \"inputShow\": false, \"inputValue\": \"\"}], \"animal\": [{\"value\": \"True\", \"inputShow\": false, \"inputValue\": \"\"}]}}}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"gc_path\"], \"properties\": {\"gc_path\": {\"type\": \"object\", \"required\": [], \"ui:order\": [\"cn_source\", \"nutrition_type\"], \"properties\": {\"cn_source\": {\"type\": \"object\", \"title\": \"GC\", \"required\": [], \"ui:order\": [\"n_source\"], \"properties\": {\"n_source\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"fieldColumn\": [{\"name\": \"metabolite\", \"label\": \"Metabolite\"}, {\"name\": \"ability\", \"type\": \"mycomponent\", \"label\": \"Ability\", \"btnList\": false, \"columns\": [{\"label\": \"Positive\", \"value\": \"Positive\"}, {\"label\": \"Negative\", \"value\": \"Negative\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\"}]}]}}}}, \"nutrition_type\": {\"type\": \"object\", \"required\": [], \"ui:order\": [\"pathogenicity\"], \"properties\": {\"pathogenicity\": {\"type\": \"object\", \"title\": \"Pathogenicity\", \"required\": [], \"ui:order\": [\"level\", \"comment\", \"plant\", \"animal\", \"human\", \"myc_re_disease\", \"non_myc_re_disease\", \"underlying_disease\"], \"properties\": {\"human\": {\"type\": \"string\", \"title\": \"Human\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"level\": {\"type\": \"string\", \"title\": \"Level\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"plant\": {\"ui:field\": \"my-select\", \"ui:title\": \"Plant\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"True\", \"value\": \"True\"}, {\"name\": \"False\", \"value\": \"False\"}, {\"name\": \"Unknown\", \"value\": \"Unknown\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"animal\": {\"ui:field\": \"my-select\", \"ui:title\": \"Animal\", \"ui:fieldProps\": {\"btnList\": false, \"fieldColumn\": [{\"name\": \"True\", \"value\": \"True\"}, {\"name\": \"False\", \"value\": \"False\"}, {\"name\": \"Unknown\", \"value\": \"Unknown\"}, {\"name\": \"Others\", \"type\": \"input\", \"value\": \"Others\"}]}}, \"comment\": {\"type\": \"string\", \"title\": \"Comment\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"myc_re_disease\": {\"type\": \"string\", \"title\": \"Myc_re_disease\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"non_myc_re_disease\": {\"type\": \"string\", \"title\": \"Non_myc_re_disease\", \"ui:options\": {\"placeholder\": \"请输入\"}}, \"underlying_disease\": {\"type\": \"string\", \"title\": \"Underlying Disease\", \"ui:options\": {\"placeholder\": \"请输入\"}}}}}}}}}}','2021-01-19 10:37:34',0),(22,17,2,'tempph','Temp_ph',1,1,0,'{\"PH\": {\"PH_list\": [{\"PH\": \"\", \"range\": \"\", \"ability\": \"\", \"test_type\": \"\"}]}, \"temperature\": {\"temperatures\": [{\"range\": \"\", \"ability\": \"Positive\", \"test_type\": \"\", \"temperature\": \"\"}]}, \"metabolite_production\": {\"metabolite_production_list\": [{\"excreted\": \"\", \"metabolite\": \"\", \"production\": \"\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"temperature\", \"PH\", \"metabolite_production\"], \"properties\": {\"PH\": {\"type\": \"object\", \"title\": \"PH\", \"required\": [], \"ui:order\": [\"PH_list\"], \"properties\": {\"PH_list\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"test_type\", \"label\": \"Test Type\"}, {\"name\": \"PH\", \"label\": \"PH\"}, {\"name\": \"range\", \"label\": \"Range\"}, {\"name\": \"ability\", \"type\": \"mycomponent\", \"label\": \"Ability\", \"columns\": [{\"label\": \"Positive\", \"value\": \"Positive\"}, {\"label\": \"Negative\", \"value\": \"Negative\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\"}]}]}}}}, \"temperature\": {\"type\": \"object\", \"title\": \"Temperature\", \"required\": [], \"ui:order\": [\"temperatures\"], \"properties\": {\"temperatures\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"test_type\", \"label\": \"Test Type\"}, {\"name\": \"temperature\", \"label\": \"Temperature\"}, {\"name\": \"range\", \"label\": \"Range\"}, {\"name\": \"ability\", \"type\": \"mycomponent\", \"label\": \"Ability\", \"columns\": [{\"label\": \"Positive\", \"value\": \"Positive\"}, {\"label\": \"Negative\", \"value\": \"Negative\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\"}]}]}}}}, \"metabolite_production\": {\"type\": \"object\", \"title\": \"Metabolite Production\", \"required\": [], \"ui:order\": [\"metabolite_production_list\"], \"properties\": {\"metabolite_production_list\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"metabolite\", \"label\": \"Metabolite\"}, {\"name\": \"production\", \"type\": \"mycomponent\", \"label\": \"Production\", \"columns\": [{\"label\": \"true\", \"value\": \"true\"}, {\"label\": \"false\", \"value\": \"false\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\"}]}, {\"name\": \"excreted\", \"label\": \"Excreted\"}]}}}}}}','2021-01-19 10:37:59',0),(23,17,2,'haltol','Hal_tol',1,1,0,'{\"halophily\": {\"halophily_list\": [{\"salt\": \"\", \"range\": \"\", \"test_type\": \"\", \"concentration\": \"\"}]}, \"tolerance\": {\"tolerance_list\": [{\"compound_name\": \"\", \"concentration\": \"\", \"level_percent\": \"\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"halophily\", \"tolerance\"], \"properties\": {\"halophily\": {\"type\": \"object\", \"title\": \"Halophily\", \"required\": [], \"ui:order\": [\"halophily_list\"], \"properties\": {\"halophily_list\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:title\": \"Indole Production\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"salt\", \"label\": \"Salt\"}, {\"name\": \"test_type\", \"label\": \"Test Type\"}, {\"name\": \"concentration\", \"label\": \"Concentration\"}, {\"name\": \"ability\", \"type\": \"mycomponent\", \"label\": \"Ability\", \"columns\": [{\"label\": \"Positive\", \"value\": \"Positive\", \"isRootInsert\": false}, {\"label\": \"Negative\", \"value\": \"Negative\", \"isRootInsert\": false}, {\"label\": \"Unknown\", \"value\": \"Unknown\", \"isRootInsert\": false}, {\"label\": \"Inconsistent\", \"value\": \"Inconsistent\", \"isRootInsert\": false}, {\"type\": \"input\", \"label\": \"Others\", \"value\": \"Others\", \"istype\": \"input\"}]}, {\"name\": \"range\", \"label\": \"Range\"}]}}}}, \"tolerance\": {\"type\": \"object\", \"title\": \"Tolerance\", \"required\": [], \"ui:order\": [\"tolerance_list\"], \"properties\": {\"tolerance_list\": {\"type\": \"string\", \"ui:field\": \"my-form\", \"ui:fieldProps\": {\"btnList\": true, \"fieldColumn\": [{\"name\": \"compound_name\", \"label\": \"Compound Name\"}, {\"name\": \"level_percent\", \"label\": \"Level Percent\"}, {\"name\": \"concentration\", \"label\": \"Concentration\"}]}}}}}}','2021-01-19 10:38:24',0),(24,17,2,'antibiotic','Antibiotic',1,1,0,'{\"voges_proskauer\": [{\"mic\": \"\", \"antiboitic\": \"Sensitive\", \"metabolite\": \"\", \"concentration\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"voges_proskauer\"], \"properties\": {\"voges_proskauer\": {\"ui:field\": \"mcp-table\", \"ui:title\": \"Voges Proskauer\", \"ui:fieldProps\": {\"column\": [{\"name\": \"metabolite\", \"type\": \"input\", \"label\": \"Metabolite\", \"width\": 180}, {\"name\": \"antiboitic\", \"type\": \"select\", \"label\": \"Antiboitic\", \"width\": 180, \"options\": [{\"label\": \"Sensitive\", \"value\": \"Sensitive\"}, {\"label\": \"Resistant\", \"value\": \"Resistant\"}, {\"label\": \"Intermediate\", \"value\": \"Intermediate\"}, {\"label\": \"Unknown\", \"value\": \"Unknown\"}, {\"type\": \"input\", \"label\": \"other\", \"value\": \"other\"}]}, {\"name\": \"concentration\", \"type\": \"input\", \"label\": \"Concentration\", \"width\": 180}, {\"name\": \"mic\", \"type\": \"input\", \"label\": \"MIC\", \"width\": 180}]}}}}','2021-01-19 10:38:43',0),(25,17,2,'fatty','Fatty',1,1,0,'{\"fatty_acids\": {}, \"fatty_acids_detail\": {\"fatty_acids_detail\": [{\"name\": \"\", \"units\": \"\", \"amount\": \"\"}]}}','{\"type\": \"object\", \"required\": [], \"ui:order\": [\"fatty_acids\", \"fatty_acids_detail\"], \"properties\": {\"fatty_acids\": {\"type\": \"object\", \"title\": \"Fatty Acids\", \"required\": [], \"ui:order\": [\"fatty_acids_profile\"], \"properties\": {\"fatty_acids_profile\": {\"type\": \"string\", \"title\": \"Fatty Acids Profile\", \"ui:options\": {\"placeholder\": \"请输入\"}}}}, \"fatty_acids_detail\": {\"type\": \"object\", \"title\": \"Fatty Acids Detail\", \"required\": [], \"ui:order\": [\"fatty_acids_detail\"], \"properties\": {\"fatty_acids_detail\": {\"ui:field\": \"mcp-table\", \"ui:title\": \"Voges Proskauer\", \"ui:fieldProps\": {\"column\": [{\"name\": \"name\", \"type\": \"input\", \"label\": \"Name\", \"width\": 180}, {\"name\": \"amount\", \"type\": \"input\", \"label\": \"Amount\", \"width\": 180}, {\"name\": \"units\", \"type\": \"input\", \"label\": \"Units\", \"width\": 180}]}}}}}}','2021-01-19 10:38:59',0),(26,18,2,'enzym','enzym',1,1,0,'{\"voges_proskauer\": [{\"enzyme\": \"\", \"activity\": \"+\", \"ec_number\": \"\"}]}','{\"type\": \"object\", \"ui:order\": [\"voges_proskauer\"], \"properties\": {\"voges_proskauer\": {\"ui:field\": \"mcp-table\", \"ui:title\": \"Voges Proskauer\", \"ui:fieldProps\": {\"column\": [{\"name\": \"enzyme\", \"type\": \"input\", \"label\": \"enzyme\", \"width\": 180}, {\"name\": \"activity\", \"type\": \"select\", \"label\": \"activity\", \"width\": 180, \"options\": [{\"label\": \"+\", \"value\": \"+\"}, {\"label\": \"-\", \"value\": \"-\"}, {\"label\": \"+/-\", \"value\": \"+/-\"}, {\"type\": \"input\", \"label\": \"others\", \"value\": \"others\"}]}, {\"name\": \"ec_number\", \"type\": \"input\", \"label\": \"ec_number\", \"width\": 180}]}}}}','2021-01-21 21:12:21',0);
/*!40000 ALTER TABLE `t_attribute_label` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_batch`
--

DROP TABLE IF EXISTS `t_batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_batch` (
  `batch_id` int(20) NOT NULL AUTO_INCREMENT,
  `project_id` int(20) NOT NULL,
  `name` varchar(30) NOT NULL,
  `material_source` varchar(255) DEFAULT NULL,
  `search_criteria` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  `total_article` int(20) NOT NULL DEFAULT '0',
  `status` int(2) NOT NULL DEFAULT '1',
  `deleted` tinyint(2) NOT NULL DEFAULT '0',
  `import_status` int(2) DEFAULT NULL,
  PRIMARY KEY (`batch_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE,
  KEY `import_status` (`import_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_batch`
--

LOCK TABLES `t_batch` WRITE;
/*!40000 ALTER TABLE `t_batch` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_batch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_entity_label`
--

DROP TABLE IF EXISTS `t_entity_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entity_label` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,
  `pid` bigint(11) unsigned NOT NULL DEFAULT '0' COMMENT '父标签ID',
  `level` int(11) NOT NULL COMMENT '层级',
  `code` varchar(50) NOT NULL COMMENT '标签代码',
  `name` varchar(50) NOT NULL COMMENT '标签名字',
  `description` varchar(255) DEFAULT NULL COMMENT '备注和描述使用方法',
  `status` int(11) DEFAULT '1' COMMENT '停启状态(1是正常，0是禁用)',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1代表删除 0代表未删除',
  `project_id` bigint(11) NOT NULL DEFAULT '0' COMMENT '0是公共标签，其他是私有标签',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `pid` (`pid`) USING BTREE,
  KEY `level` (`level`) USING BTREE,
  KEY `code` (`code`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_entity_label`
--

LOCK TABLES `t_entity_label` WRITE;
/*!40000 ALTER TABLE `t_entity_label` DISABLE KEYS */;
INSERT INTO `t_entity_label` VALUES (1,0,1,'ACTI','Activities & Behaviors',NULL,1,0,0,'2020-12-22 08:56:41'),(2,1,2,'T052','Activity',NULL,1,0,0,'2020-12-22 08:56:41'),(3,1,2,'T053','Behavior',NULL,1,0,0,'2020-12-22 08:56:41'),(4,1,2,'T056','Daily or Recreational Activity',NULL,1,0,0,'2020-12-22 08:56:41'),(5,1,2,'T051','Event',NULL,1,0,0,'2020-12-22 08:56:41'),(6,1,2,'T064','Governmental or Regulatory Activity',NULL,1,0,0,'2020-12-22 08:56:41'),(7,1,2,'T055','Individual Behavior',NULL,1,0,0,'2020-12-22 08:56:41'),(8,1,2,'T066','Machine Activity',NULL,1,0,0,'2020-12-22 08:56:41'),(9,1,2,'T057','Occupational Activity',NULL,1,0,0,'2020-12-22 08:56:41'),(10,1,2,'T054','Social Behavior',NULL,1,0,0,'2020-12-22 08:56:41'),(11,0,1,'ANAT','Anatomy',NULL,1,0,0,'2020-12-22 08:56:41'),(12,11,2,'T017','Anatomical Structure',NULL,1,0,0,'2020-12-22 08:56:41'),(13,11,2,'T029','Body Location or Region',NULL,1,0,0,'2020-12-22 08:56:41'),(14,11,2,'T023','Body Part',NULL,1,0,0,'2020-12-22 08:56:41'),(15,11,2,'T030','Body Space or Junction',NULL,1,0,0,'2020-12-22 08:56:41'),(16,11,2,'T031','Body Substance',NULL,1,0,0,'2020-12-22 08:56:41'),(17,11,2,'T022','Body System',NULL,1,0,0,'2020-12-22 08:56:41'),(18,11,2,'T025','Cell',NULL,1,0,0,'2020-12-22 08:56:41'),(19,11,2,'T026','Cell Component',NULL,1,0,0,'2020-12-22 08:56:41'),(20,11,2,'T018','Embryonic Structure',NULL,1,0,0,'2020-12-22 08:56:41'),(21,11,2,'T021','Fully Formed Anatomical Structure',NULL,1,0,0,'2020-12-22 08:56:41'),(22,11,2,'T024','Tissue',NULL,1,0,0,'2020-12-22 08:56:41'),(23,0,1,'CHEM','Chemicals & Drugs',NULL,1,0,0,'2020-12-22 08:56:41'),(24,23,2,'T116','Amino Acid',NULL,1,0,0,'2020-12-22 08:56:41'),(25,23,2,'T195','Antibiotic',NULL,1,0,0,'2020-12-22 08:56:41'),(26,23,2,'T123','Biologically Active Substance',NULL,1,0,0,'2020-12-22 08:56:41'),(27,23,2,'T122','Biomedical or Dental Material',NULL,1,0,0,'2020-12-22 08:56:41'),(28,23,2,'T103','Chemical',NULL,1,0,0,'2020-12-22 08:56:41'),(29,23,2,'T120','Chemical Viewed Functionally',NULL,1,0,0,'2020-12-22 08:56:41'),(30,23,2,'T104','Chemical Viewed Structurally',NULL,1,0,0,'2020-12-22 08:56:41'),(31,23,2,'T200','Clinical Drug',NULL,1,0,0,'2020-12-22 08:56:41'),(32,23,2,'T196','Element',NULL,1,0,0,'2020-12-22 08:56:41'),(33,23,2,'T126','Enzyme',NULL,1,0,0,'2020-12-22 08:56:41'),(34,23,2,'T131','Hazardous or Poisonous Substance',NULL,1,0,0,'2020-12-22 08:56:41'),(35,23,2,'T125','Hormone',NULL,1,0,0,'2020-12-22 08:56:41'),(36,23,2,'T129','Immunologic Factor',NULL,1,0,0,'2020-12-22 08:56:41'),(37,23,2,'T130','Indicator',NULL,1,0,0,'2020-12-22 08:56:41'),(38,23,2,'T197','Inorganic Chemical',NULL,1,0,0,'2020-12-22 08:56:41'),(39,23,2,'T114','Nucleic Acid',NULL,1,0,0,'2020-12-22 08:56:41'),(40,23,2,'T109','Organic Chemical',NULL,1,0,0,'2020-12-22 08:56:41'),(41,23,2,'T121','Pharmacologic Substance',NULL,1,0,0,'2020-12-22 08:56:41'),(42,23,2,'T192','Receptor',NULL,1,0,0,'2020-12-22 08:56:41'),(43,23,2,'T127','Vitamin',NULL,1,0,0,'2020-12-22 08:56:41'),(44,0,1,'CONC','Concepts & Ideas',NULL,1,0,0,'2020-12-22 08:56:41'),(45,44,2,'T185','Classification',NULL,1,0,0,'2020-12-22 08:56:41'),(46,44,2,'T077','Conceptual Entity',NULL,1,0,0,'2020-12-22 08:56:41'),(47,44,2,'T169','Functional Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(48,44,2,'T102','Group Attribute',NULL,1,0,0,'2020-12-22 08:56:41'),(49,44,2,'T078','Idea or Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(50,44,2,'T170','Intellectual Product',NULL,1,0,0,'2020-12-22 08:56:41'),(51,44,2,'T171','Language',NULL,1,0,0,'2020-12-22 08:56:41'),(52,44,2,'T080','Qualitative Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(53,44,2,'T081','Quantitative Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(54,44,2,'T089','Regulation or Law',NULL,1,0,0,'2020-12-22 08:56:41'),(55,44,2,'T082','Spatial Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(56,44,2,'T079','Temporal Concept',NULL,1,0,0,'2020-12-22 08:56:41'),(57,0,1,'DEVI','Devices',NULL,1,0,0,'2020-12-22 08:56:41'),(58,57,2,'T203','Drug Delivery Device',NULL,1,0,0,'2020-12-22 08:56:41'),(59,57,2,'T074','Medical Device',NULL,1,0,0,'2020-12-22 08:56:41'),(60,57,2,'T075','Research Device',NULL,1,0,0,'2020-12-22 08:56:41'),(61,0,1,'DISO','Disorders',NULL,1,0,0,'2020-12-22 08:56:41'),(62,61,2,'T020','Acquired Abnormality',NULL,1,0,0,'2020-12-22 08:56:41'),(63,61,2,'T190','Anatomical Abnormality',NULL,1,0,0,'2020-12-22 08:56:41'),(64,61,2,'T049','Cell or Molecular Dysfunction',NULL,1,0,0,'2020-12-22 08:56:41'),(65,61,2,'T019','Congenital Abnormality',NULL,1,0,0,'2020-12-22 08:56:41'),(66,61,2,'T047','Disease or Syndrome',NULL,1,0,0,'2020-12-22 08:56:41'),(67,61,2,'T050','Experimental Model of Disease',NULL,1,0,0,'2020-12-22 08:56:41'),(68,61,2,'T033','Finding',NULL,1,0,0,'2020-12-22 08:56:41'),(69,61,2,'T037','Injury or Poisoning',NULL,1,0,0,'2020-12-22 08:56:41'),(70,61,2,'T048','Mental or Behavioral Dysfunction',NULL,1,0,0,'2020-12-22 08:56:41'),(71,61,2,'T191','Neoplastic Process',NULL,1,0,0,'2020-12-22 08:56:41'),(72,61,2,'T046','Pathologic Function',NULL,1,0,0,'2020-12-22 08:56:41'),(73,61,2,'T184','Sign or Symptom',NULL,1,0,0,'2020-12-22 08:56:41'),(74,0,1,'GENE','Genes & Molecular Sequences',NULL,1,0,0,'2020-12-22 08:56:41'),(75,74,2,'T087','Amino Acid Sequence',NULL,1,0,0,'2020-12-22 08:56:41'),(76,74,2,'T088','Carbohydrate Sequence',NULL,1,0,0,'2020-12-22 08:56:41'),(77,74,2,'T028','Gene or Genome',NULL,1,0,0,'2020-12-22 08:56:41'),(78,74,2,'T085','Molecular Sequence',NULL,1,0,0,'2020-12-22 08:56:41'),(79,74,2,'T086','Nucleotide Sequence',NULL,1,0,0,'2020-12-22 08:56:41'),(80,0,1,'GEOG','Geographic Areas',NULL,1,0,0,'2020-12-22 08:56:41'),(81,80,2,'T083','Geographic Area',NULL,1,0,0,'2020-12-22 08:56:41'),(82,0,1,'LIVB','Living Beings',NULL,1,0,0,'2020-12-22 08:56:41'),(83,82,2,'T100','Age Group',NULL,1,0,0,'2020-12-22 08:56:41'),(84,82,2,'T011','Amphibian',NULL,1,0,0,'2020-12-22 08:56:41'),(85,82,2,'T008','Animal',NULL,1,0,0,'2020-12-22 08:56:41'),(86,82,2,'T194','Archaeon',NULL,1,0,0,'2020-12-22 08:56:41'),(87,82,2,'T007','Bacterium',NULL,1,0,0,'2020-12-22 08:56:41'),(88,82,2,'T012','Bird',NULL,1,0,0,'2020-12-22 08:56:41'),(89,82,2,'T204','Eukaryote',NULL,1,0,0,'2020-12-22 08:56:41'),(90,82,2,'T099','Family Group',NULL,1,0,0,'2020-12-22 08:56:41'),(91,82,2,'T013','Fish',NULL,1,0,0,'2020-12-22 08:56:41'),(92,82,2,'T004','Fungus',NULL,1,0,0,'2020-12-22 08:56:41'),(93,82,2,'T096','Group',NULL,1,0,0,'2020-12-22 08:56:41'),(94,82,2,'T016','Human',NULL,1,0,0,'2020-12-22 08:56:41'),(95,82,2,'T015','Mammal',NULL,1,0,0,'2020-12-22 08:56:41'),(96,82,2,'T001','Organism',NULL,1,0,0,'2020-12-22 08:56:41'),(97,82,2,'T101','Patient or Disabled Group',NULL,1,0,0,'2020-12-22 08:56:41'),(98,82,2,'T002','Plant',NULL,1,0,0,'2020-12-22 08:56:41'),(99,82,2,'T098','Population Group',NULL,1,0,0,'2020-12-22 08:56:41'),(100,82,2,'T097','Professional or Occupational Group',NULL,1,0,0,'2020-12-22 08:56:41');
/*!40000 ALTER TABLE `t_entity_label` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_note`
--

DROP TABLE IF EXISTS `t_note`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_note` (
  `note_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `batch_id` bigint(20) DEFAULT NULL COMMENT '批次ID',
  `document_id` varchar(30) DEFAULT NULL COMMENT '文档ID',
  `article_id` varchar(20) DEFAULT NULL COMMENT '文章ID',
  `article_name` text COMMENT '文章名',
  `last_update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `status` int(11) DEFAULT NULL COMMENT '0未标注，1标注中，2已标注，3审核中，4已审核',
  `invalid` tinyint(2) DEFAULT '0' COMMENT '是否废弃',
  `lock_user` bigint(20) DEFAULT NULL COMMENT '（待删）',
  `annotator` bigint(20) DEFAULT NULL COMMENT '标注员ID',
  `anno_start_time` datetime DEFAULT NULL COMMENT '标注开始时间',
  `anno_end_time` datetime DEFAULT NULL COMMENT '标注结束时间',
  `auditor` bigint(20) DEFAULT NULL COMMENT '审核员ID',
  `audit_start_time` datetime DEFAULT NULL COMMENT '审核开始时间',
  `audit_end_time` datetime DEFAULT NULL COMMENT '审核结束时间',
  `deleted` int(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`note_id`) USING BTREE,
  KEY `batch_id` (`batch_id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `update_time` (`last_update_time`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_note`
--

LOCK TABLES `t_note` WRITE;
/*!40000 ALTER TABLE `t_note` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_note` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_project`
--

DROP TABLE IF EXISTS `t_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_project` (
  `project_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(32) DEFAULT NULL,
  `name` varchar(30) NOT NULL,
  `creator_id` bigint(20) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  `status` tinyint(2) NOT NULL DEFAULT '1',
  `doc_id` bigint(20) DEFAULT NULL,
  `pre_sources` varchar(20) DEFAULT NULL,
  `deleted` tinyint(2) NOT NULL DEFAULT '0',
  PRIMARY KEY (`project_id`) USING BTREE,
  UNIQUE KEY `code` (`code`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `creator_id` (`creator_id`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_project`
--

LOCK TABLES `t_project` WRITE;
/*!40000 ALTER TABLE `t_project` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_project_user`
--

DROP TABLE IF EXISTS `t_project_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_project_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `role_id` int(2) NOT NULL,
  `deleted` tinyint(2) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `role_id` (`role_id`) USING BTREE,
  KEY `deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_project_user`
--

LOCK TABLES `t_project_user` WRITE;
/*!40000 ALTER TABLE `t_project_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_project_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_relation_label`
--

DROP TABLE IF EXISTS `t_relation_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_relation_label` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `special` int(11) DEFAULT NULL COMMENT '0是普通标签，一是特殊标签',
  `name` varchar(50) NOT NULL COMMENT '标签名字',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `status` int(11) DEFAULT NULL COMMENT '停启状态(1是正常，0是禁用)',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1代表删除 0代表未删除',
  `project_id` bigint(11) NOT NULL DEFAULT '0' COMMENT '0是公共标签，其他是私有标签',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `user_id` bigint(11) NOT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_relation_label`
--

LOCK TABLES `t_relation_label` WRITE;
/*!40000 ALTER TABLE `t_relation_label` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_relation_label` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_semantic_types`
--

DROP TABLE IF EXISTS `t_semantic_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_semantic_types` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `simple` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=128 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_semantic_types`
--

LOCK TABLES `t_semantic_types` WRITE;
/*!40000 ALTER TABLE `t_semantic_types` DISABLE KEYS */;
INSERT INTO `t_semantic_types` VALUES (1,'aapp','T116','Amino Acid, Peptide, or Protein'),(2,'acab','T020','Acquired Abnormality'),(3,'acty','T052','Activity'),(4,'aggp','T100','Age Group'),(5,'amas','T087','Amino Acid Sequence'),(6,'amph','T011','Amphibian'),(7,'anab','T190','Anatomical Abnormality'),(8,'anim','T008','Animal'),(9,'anst','T017','Anatomical Structure'),(10,'antb','T195','Antibiotic'),(11,'arch','T194','Archaeon'),(12,'bacs','T123','Biologically Active Substance'),(13,'bact','T007','Bacterium'),(14,'bdsu','T031','Body Substance'),(15,'bdsy','T022','Body System'),(16,'bhvr','T053','Behavior'),(17,'biof','T038','Biologic Function'),(18,'bird','T012','Bird'),(19,'blor','T029','Body Location or Region'),(20,'bmod','T091','Biomedical Occupation or Discipline'),(21,'bodm','T122','Biomedical or Dental Material'),(22,'bpoc','T023','Body Part, Organ, or Organ Component'),(23,'bsoj','T030','Body Space or Junction'),(24,'celc','T026','Cell Component'),(25,'celf','T043','Cell Function'),(26,'cell','T025','Cell'),(27,'cgab','T019','Congenital Abnormality'),(28,'chem','T103','Chemical'),(29,'chvf','T120','Chemical Viewed Functionally'),(30,'chvs','T104','Chemical Viewed Structurally'),(31,'clas','T185','Classification'),(32,'clna','T201','Clinical Attribute'),(33,'clnd','T200','Clinical Drug'),(34,'cnce','T077','Conceptual Entity'),(35,'comd','T049','Cell or Molecular Dysfunction'),(36,'crbs','T088','Carbohydrate Sequence'),(37,'diap','T060','Diagnostic Procedure'),(38,'dora','T056','Daily or Recreational Activity'),(39,'drdd','T203','Drug Delivery Device'),(40,'dsyn','T047','Disease or Syndrome'),(41,'edac','T065','Educational Activity'),(42,'eehu','T069','Environmental Effect of Humans'),(43,'elii','T196','Element, Ion, or Isotope'),(44,'emod','T050','Experimental Model of Disease'),(45,'emst','T018','Embryonic Structure'),(46,'enty','T071','Entity'),(47,'enzy','T126','Enzyme'),(48,'euka','T204','Eukaryote'),(49,'evnt','T051','Event'),(50,'famg','T099','Family Group'),(51,'ffas','T021','Fully Formed Anatomical Structure'),(52,'fish','T013','Fish'),(53,'fndg','T033','Finding'),(54,'fngs','T004','Fungus'),(55,'food','T168','Food'),(56,'ftcn','T169','Functional Concept'),(57,'genf','T045','Genetic Function'),(58,'geoa','T083','Geographic Area'),(59,'gngm','T028','Gene or Genome'),(60,'gora','T064','Governmental or Regulatory Activity'),(61,'grpa','T102','Group Attribute'),(62,'grup','T096','Group'),(63,'hcpp','T068','Human-caused Phenomenon or Process'),(64,'hcro','T093','Health Care Related Organization'),(65,'hlca','T058','Health Care Activity'),(66,'hops','T131','Hazardous or Poisonous Substance'),(67,'horm','T125','Hormone'),(68,'humn','T016','Human'),(69,'idcn','T078','Idea or Concept'),(70,'imft','T129','Immunologic Factor'),(71,'inbe','T055','Individual Behavior'),(72,'inch','T197','Inorganic Chemical'),(73,'inpo','T037','Injury or Poisoning'),(74,'inpr','T170','Intellectual Product'),(75,'irda','T130','Indicator, Reagent, or Diagnostic Aid'),(76,'lang','T171','Language'),(77,'lbpr','T059','Laboratory Procedure'),(78,'lbtr','T034','Laboratory or Test Result'),(79,'mamm','T015','Mammal'),(80,'mbrt','T063','Molecular Biology Research Technique'),(81,'mcha','T066','Machine Activity'),(82,'medd','T074','Medical Device'),(83,'menp','T041','Mental Process'),(84,'mnob','T073','Manufactured Object'),(85,'mobd','T048','Mental or Behavioral Dysfunction'),(86,'moft','T044','Molecular Function'),(87,'mosq','T085','Molecular Sequence'),(88,'neop','T191','Neoplastic Process'),(89,'nnon','T114','Nucleic Acid, Nucleoside, or Nucleotide'),(90,'npop','T070','Natural Phenomenon or Process'),(91,'nusq','T086','Nucleotide Sequence'),(92,'ocac','T057','Occupational Activity'),(93,'ocdi','T090','Occupation or Discipline'),(94,'orch','T109','Organic Chemical'),(95,'orga','T032','Organism Attribute'),(96,'orgf','T040','Organism Function'),(97,'orgm','T001','Organism'),(98,'orgt','T092','Organization'),(99,'ortf','T042','Organ or Tissue Function'),(100,'patf','T046','Pathologic Function'),(101,'phob','T072','Physical Object'),(102,'phpr','T067','Phenomenon or Process'),(103,'phsf','T039','Physiologic Function'),(104,'phsu','T121','Pharmacologic Substance'),(105,'plnt','T002','Plant'),(106,'podg','T101','Patient or Disabled Group'),(107,'popg','T098','Population Group'),(108,'prog','T097','Professional or Occupational Group'),(109,'pros','T094','Professional Society'),(110,'qlco','T080','Qualitative Concept'),(111,'qnco','T081','Quantitative Concept'),(112,'rcpt','T192','Receptor'),(113,'rept','T014','Reptile'),(114,'resa','T062','Research Activity'),(115,'resd','T075','Research Device'),(116,'rnlw','T089','Regulation or Law'),(117,'sbst','T167','Substance'),(118,'shro','T095','Self-help or Relief Organization'),(119,'socb','T054','Social Behavior'),(120,'sosy','T184','Sign or Symptom'),(121,'spco','T082','Spatial Concept'),(122,'tisu','T024','Tissue'),(123,'tmco','T079','Temporal Concept'),(124,'topp','T061','Therapeutic or Preventive Procedure'),(125,'virs','T005','Virus'),(126,'vita','T127','Vitamin'),(127,'vtbt','T010','Vertebrate');
/*!40000 ALTER TABLE `t_semantic_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'bnlp'
--

--
-- Dumping routines for database 'bnlp'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2021-03-15 19:44:28
