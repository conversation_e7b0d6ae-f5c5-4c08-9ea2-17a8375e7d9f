## 项目版本
**v3.0.1**  
2023年7月7日
1. bug: 修复重复标注时，实体识别错误，以及无法拖动问题
2. bug: 修复任务列表显示标注员错误
3. bug: 不允许审核自己标注的数据
4. bug: 下载的标注结果，邮箱中提示的时间差8小时
5. 优化: 一个属性要能在多个实体中使用
6. 优化: 属性标注如果关联错了属性，支持直接拖拽至另一个属性
7. 优化: 标注完成后，点击“继续”，从“待标注”中继续获取文章
8. 优化: 支持继承已共享的项目配置
9. 优化: 如果直接提交，发现有闲置属性，填加提示
10. 优化: json下载数据，增加api接口

**v3.0.2**  
2023年10月10日
1. bug: 修复实体清单显示数量不正确bug

**v3.0.3**  
2023年11月6日
1. bug: 没有任何标注信息的文章，在提交审核通过的时候会出现异常

**v3.0.4**  
2023年11月10日
1. 优化：增加标签的自定义排序

**v3.0.5**  
2023年11月13日
1. bug: 双轮标注自动审核的结果在下载的时候无法下载

**v3.0.6**  
2024年1月2日
1. bug: 统计页面中统计图表数值有错误

**v3.0.7**  
2024年1月11日
1. bug: 自动审核的准确率应该默认为100%

**v3.0.8**  
2024年1月11日
1. bug: 适配文本中的特殊字符<

**v3.0.9**  
2024年1月31日
1. bug: 点击全部导入原文时报错

**v3.1.0**  
2024年3月15日
1. 优化: 任务列表已验收标签页中，添加正确率的排序，筛选
2. 优化: 当用户的数据已验收的情况下，右上角提供一个复选框，勾选后页面中的实体只显示当前标注员的数据，取消勾选后显示全部的实体

**v3.1.2**  
2024年4月8日
1. bug: 修正任务列表准确率的统计错误

**v3.2.0**  
2024年4月23日
1. 优化: 在统计上增加准确率、精确率、召回率、F1值这四个指标

**v3.2.1**  
2024年5月13日
1. 优化: 在项目管理员的文章列表中，添加一键提交标注完成功能

**v3.2.2**  
2024年5月21日
1. 优化: 取消将批注信息纳入准确率计算

**v3.3.0**  
2024年6月17日
1. 优化: 开发第三数据与已验收的数据做比对计算出准确率的功能

**v3.4.0**  
2024年9月20日
1. 优化: 标签导入支持顺序保持
2. 优化: 标签属性展示
3. 优化：属性拖拽距离优化
4. 优化：管理界面文章支持翻页
5. 优化：支持删除已关联实体的属性标签
6. 优化：标签使用情况界面支持查看属性列表

**v3.4.1**  
2024年9月23日
1. 优化：实体标签使用情况属性列表改为鼠标悬浮

**v3.4.2**  
2024年9月24日
1. bug：修复当2个标注员已经标注，第三个标注员待标注列表重复问题

**v3.4.3**  
2024年9月26日
1. bug：修复消歧功能复选框回显bug
2. 优化：实体标签使用情况属性统计改为异步加载方案
3. 优化：添加更新记录日志功能