# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 8083
  connection-timeout: 5000ms
  servlet:
    context-path: /bnlp-api
    session:
      cookie:
        name: bnlp

spring:
  jmx:
    enabled: false
  # 环境 dev|prod
  profiles:
    active: @spring.profiles.active@
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    #实体类转json时字段为null不参与序列化
    default-property-inclusion: NON_EMPTY
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  datasource:
    druid:
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
  cache:
    type: redis
    redis:
      time-to-live: 300000
  redis:
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  mvc:
    throw-exception-if-no-handler-found: true

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.biosino.nlp.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1 #逻辑已删除
      logic-not-delete-value: 0 #逻辑未删除
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

renren:
  redis:
    open: false
  shiro:
    redis: false
  # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
  jwt:
    # 加密秘钥
    secret: f4e2e52034348f86b67cde581c0f9eb5[www.biosino.org]
    # token有效时长，7天，单位秒
    expire: 604800
    header: token

api:
  # UMLS Concept
  download-reference-pdf-url: https://idc.biosino.org/plosp-api/api/article/downPdfByPmid.do?pmid=
  download-reference-pdf-token: 57956b9d8f664b64b044ae29af6a5ff232602cf19b9924c08a7331770c884946
