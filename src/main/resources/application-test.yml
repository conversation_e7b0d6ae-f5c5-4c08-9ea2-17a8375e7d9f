spring:
  data:
    mongodb:
      uri: **************************************************************************
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://***************:3306/bnlp-test?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: root
      password: Lfgzs@2021
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    username: <EMAIL>
    password: '#x~tzxBumQrm#b6X'
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory
  redis:
    open: true
    host: ***************
    port: 6379
    timeout: 6000ms
    database: 3
# 外部服务API
api:
  # BFMS 系统API
  meta-map-url: https://idc.biosino.org/bnlp/utils-api/
  article-api: https://idc.biosino.org/bfms-api/api/article
#  article-api: http://localhost:8084/bfms-api/api/article

# 数据存放路径
app:
  dataHome: D:\test\bnlp
  download-host: http://localhost:8083
logging:
  level:
    org.springframework.data.mongodb.core: debug
