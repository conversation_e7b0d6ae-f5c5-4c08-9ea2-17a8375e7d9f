spring:
  data:
    mongodb:
      uri: mongodb://root:<EMAIL>:27017/bnlp3?authSource=admin
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ********************************************************************************************************************
      username: root
      password: bnlp@2021
      filter:
        stat:
          log-slow-sql: false
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    username: <EMAIL>
    password: '#x~tzxBumQrm#b6X'
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory
  redis:
    open: true
    host: redis.bnlp
    port: 6379
    password: bnlp@2021
    timeout: 60000ms
    database: 2

logging.level:
  root: warn

# 外部服务API
api:
  # BFMS 系统API
  meta-map-url: http://bnlp-proxy.bnlp:8088/
  article-api: http://tomcat-bfms.bnlp:8084/bfms-api/api/article

# 数据文件存放路径
app:
  dataHome: /data/bnlp3/data
  download-host: https://idc.biosino.org/bnlp-api-v3
