<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.biosino.nlp.modules.sys.dao.SysRoleMenuDao">

    <select id="queryMenuIdList" resultType="long">
		select menu_id from sys_role_menu where role_id = #{value}
	</select>

    <delete id="deleteBatch">
        delete from sys_role_menu where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

</mapper>
