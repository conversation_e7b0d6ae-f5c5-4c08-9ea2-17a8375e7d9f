<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.biosino.nlp.modules.sys.dao.SysUserDao">
    <!-- 查询用户的所有权限 -->
    <select id="queryAllPerms" resultType="string">
		select m.perms from sys_user_role ur
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
			LEFT JOIN sys_menu m on rm.menu_id = m.menu_id
		where ur.user_id = #{userId}
	</select>

    <select id="queryAllUserRolesPerms" resultType="string">
        select m.perms from sys_user_role ur
        LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
        LEFT JOIN sys_menu m on rm.menu_id = m.menu_id
        where ur.user_id = #{userId} and ur.role_id = #{roleId}
    </select>

    <!-- 查询用户的所有菜单ID -->
    <select id="queryAllMenuId" resultType="long">
		select distinct rm.menu_id from sys_user_role ur
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
		where ur.user_id = #{userId}
	</select>

    <select id="queryAllUserRoleMenuId" resultType="long">
        select distinct rm.menu_id from sys_user_role ur
        LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
        where ur.user_id = #{userId} and ur.role_id = #{roleId}
    </select>

    <select id="queryByUserName" resultType="org.biosino.nlp.modules.sys.entity.SysUserEntity">
		select * from sys_user where username = #{username}
	</select>


    <select id="queryRoleIdsByUserId" resultType="long">
        select ur.role_id from sys_user_role ur
        where ur.user_id = #{userId}
    </select>

</mapper>
