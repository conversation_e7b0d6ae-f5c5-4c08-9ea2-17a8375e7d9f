<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.biosino.nlp.modules.labels.dao.UmlsConceptDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="org.biosino.nlp.modules.labels.entity.UmlsConcept" id="umlsConceptMap">
        <result property="id" column="id"/>
        <result property="conceptId" column="concept_id"/>
        <result property="conceptName" column="concept_name"/>
        <result property="semanticTypes" column="semantic_types"/>
        <result property="preferredName" column="preferred_name"/>
        <result property="status" column="status"/>
        <result property="creater" column="creater"/>
        <result property="createTime" column="create_time"/>
        <result property="projectId" column="project_id"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>
