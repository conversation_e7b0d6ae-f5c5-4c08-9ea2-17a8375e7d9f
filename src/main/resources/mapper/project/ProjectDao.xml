<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.biosino.nlp.modules.project.dao.ProjectDao">
    <resultMap id="projectMap" type="org.biosino.nlp.modules.project.entity.Project">

        <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="pre_sources" jdbcType="VARCHAR" property="preSources"/>

        <collection property="batches" ofType="org.biosino.nlp.modules.project.entity.Batch">
            <id property="batchId" column="batch_id"/>
            <result property="name" column="batch_name"/>
        </collection>
    </resultMap>

    <select id="queryProjectParticipate" resultMap="projectMap">
        SELECT DISTINCT p.*,
                        b.batch_id,
                        b.name as batch_name
        FROM t_project p
                     INNER JOIN t_project_user pu ON pu.project_id = p.project_id AND p.status = 1 AND pu.deleted = 0
                     LEFT JOIN t_batch b ON b.project_id = p.project_id AND b.status = 1 AND b.deleted = 0
        WHERE p.deleted = 0 AND pu.role_id = #{roleId} AND pu.user_id = #{userId}
    </select>

    <select id="queryProjects" resultType="org.biosino.nlp.modules.project.vo.ProjectVO">
        SELECT
            p.project_id,
            p.`name` AS project_name
        FROM
            t_project p
        WHERE
            p.`status` = 1
          AND p.deleted = 0
          AND p.project_id IN ( SELECT DISTINCT pu.project_id FROM t_project_user pu
                WHERE pu.deleted = 0 AND pu.role_id =  #{roleId} AND pu.user_id = #{userId} )
        ORDER BY
            p.create_time DESC
    </select>

    <select id="findMultiAuditUserIdByPrjAndRoleId" resultType="java.lang.Long">
        SELECT
            pu.user_id
        FROM
            t_project_user pu, t_project p
        WHERE
            pu.deleted = 0
          AND p.deleted= 0
          AND p.project_id = pu.project_id
          AND p.mark_rounds &gt; 1
          AND pu.project_id = #{projectId}
          AND pu.role_id = #{roleId}
    </select>

</mapper>
