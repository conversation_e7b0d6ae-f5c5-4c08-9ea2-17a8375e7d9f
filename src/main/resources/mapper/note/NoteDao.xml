<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.biosino.nlp.modules.note.dao.NoteDao">
	<!-- 查询用户的所有权限 -->
    <select id="findByBatchAndUser" resultType="org.biosino.nlp.modules.note.entity.Note">
		SELECT
			n.*
		FROM
			`t_note` n
			LEFT JOIN l_operation l  ON (n.note_id = l.note_id AND l.deleted = 0 AND l.user_id != #{userId})
		WHERE
			n.batch_id = #{batchId}
			AND n.`status` = 0
			AND n.deleted = 0
		LIMIT 1
	</select>

	<!-- 查询Note文章列表 -->
    <select id="findArticleList" resultType="org.biosino.nlp.modules.project.vo.ArticleListVO">
		SELECT
			n.note_id noteId,
			n.article_id articleId,
			n.article_name articleName,
			n.step,
			n.invalid,
			GROUP_CONCAT( t.annotator ) annotators,
			GROUP_CONCAT( DISTINCT t.auditor ) auditor,
			n.update_time updateTime
		FROM
		(SELECT * FROM t_note WHERE deleted = 0) n
		LEFT JOIN (SELECT * FROM t_note_task WHERE deleted = 0) t ON ( n.note_id = t.note_id)
		<where>
			<if test="dto.batchId != null"> and n.batch_id = #{dto.batchId}</if>
			<if test="dto.articleId != null and dto.articleId != ''"> and n.article_id = #{dto.articleId}</if>
			<if test="dto.articleName != null and dto.articleName != ''"> and n.article_name like concat('%', #{dto.articleName}, '%')</if>
			<if test="dto.annotatorId != null"> and t.annotator = #{dto.annotatorId}</if>
			<if test="dto.auditorId != null"> and t.auditor = #{dto.auditorId}</if>
			<if test="dto.step != null"> and n.step = #{dto.step}</if>
			<if test="dto.invalid != null"> and n.invalid = #{dto.invalid}</if>
			<if test="dto.startDate != null"> and n.update_time >= #{dto.startDate}</if>
			<if test="dto.endDate != null"> and n.update_time &lt;= #{dto.endDate}</if>
			<if test="1 == 1"> AND n.deleted = 0</if>
		</where>
		GROUP BY
			n.note_id
	</select>

    <select id="avgAnnotatorTime" resultType="java.lang.Integer">
		SELECT # 计算平均值取整
			  ROUND(avg( work_time ))
		FROM ( # 取中间的时间
				SELECT work_time
				FROM ( # 计算标注耗时并排序
					SELECT
							@INDEX := @INDEX + 1 AS myindex,
							anno_complete_time AS work_time
					FROM
							t_note,
							( SELECT @INDEX := 0 ) AS initvar
					WHERE
							project_id = #{projectId} AND annotator = #{userId} AND anno_complete_time IS NOT NULL
					ORDER BY work_time
				) AS t
				WHERE
					floor( @INDEX / 2+1 )= myindex OR ceil( @INDEX / 2 )= myindex
		) AS x
    </select>

	<select id="avgAuditorTime" resultType="java.lang.Integer">
		SELECT
			   ROUND(avg( work_time ))
		FROM (
				 SELECT work_time
				 FROM (
						  SELECT
							  @INDEX := @INDEX + 1 AS myindex,
							  audit_complete_time AS work_time
						  FROM
							  t_note,
							  ( SELECT @INDEX := 0 ) AS initvar
						  WHERE
							  project_id = #{projectId} AND auditor = #{userId} AND audit_complete_time IS NOT NULL AND audit_start_time IS NOT NULL
						  ORDER BY work_time
					  ) AS t
				 WHERE
					 floor( @INDEX / 2+1 )= myindex OR ceil( @INDEX / 2 )= myindex
			 ) AS x
	</select>


</mapper>
