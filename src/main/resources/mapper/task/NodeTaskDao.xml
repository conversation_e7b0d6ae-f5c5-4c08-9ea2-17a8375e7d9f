<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.biosino.nlp.modules.note.dao.NoteTaskDao">

    <!--当前用户的项目批次列表-->
    <select id="queryProjectList" resultType="org.biosino.nlp.modules.note.vo.ProjectBatchVO">
        SELECT DISTINCT
            p.project_id,
            p.`name` AS project_name,
            b.batch_id,
            b.NAME AS batch_name
           ,b.create_time
        FROM
            t_project p
                INNER JOIN t_project_user pu ON pu.project_id = p.project_id
                AND p.STATUS = 1
                AND pu.deleted = 0
                LEFT JOIN t_batch b ON b.project_id = p.project_id
                AND b.STATUS = 1
                AND b.deleted = 0
        WHERE
            p.deleted = 0
          AND pu.role_id = #{roleId}
          AND b.batch_id IS NOT NULL
          AND pu.user_id = #{userId}
        ORDER BY b.create_time DESC
    </select>

    <!--全部——任务列表-->
    <select id="queryAllTaskList" resultType="org.biosino.nlp.modules.note.vo.TaskListVO"
            parameterType="org.biosino.nlp.modules.note.dto.TaskDTO">
        SELECT
        n.note_id noteId,
        t.task_id taskId,
        n.article_id articleId,
        n.article_name articleName,
        <if test="taskDTO.roleId == 2"> t.step  step,</if>
        <if test="taskDTO.roleId == 3"> IF(n.step = 1 AND t.step = 6, 5, t.step) step,</if>
        t.auditor auditorId,
        t.queries queries,
        t.update_time updateTime
        FROM
        (SELECT * FROM t_note WHERE deleted = 0) n
        LEFT JOIN (SELECT * FROM t_note_task WHERE deleted = 0) t ON ( n.note_id = t.note_id)
        <where>
            <if test="taskDTO.batchId != null"> and n.batch_id = #{taskDTO.batchId}</if>
            <if test="1 == 1"> and t.annotator = #{taskDTO.annotatorId}</if>
            <if test="taskDTO.roleId == 3 and taskDTO.auditorId != null"> and t.auditor = #{taskDTO.auditorId}</if>
            <if test="taskDTO.roleId == 3 and taskDTO.auditorId == null"> and t.auditor is not null</if>
            <if test="taskDTO.articleId != null and taskDTO.articleId != ''"> and n.article_id = #{taskDTO.articleId}</if>
            <if test="taskDTO.articleName != null and taskDTO.articleName != ''"> and n.article_name like concat('%', #{taskDTO.articleName}, '%')</if>
            <if test="taskDTO.startDate != null"> and COALESCE ( t.update_time, n.update_time ) >= #{taskDTO.startDate}</if>
            <if test="taskDTO.endDate != null"> and COALESCE ( t.update_time, n.update_time ) &lt;= #{taskDTO.endDate}</if>
        </where>
    </select>

    <!--标注员——任务列表-->
    <select id="queryAnnotatorTaskList" resultType="org.biosino.nlp.modules.note.vo.TaskListVO"
            parameterType="org.biosino.nlp.modules.note.dto.TaskDTO">
        SELECT
            n.note_id noteId,
            t.task_id taskId,
            n.article_id articleId,
            n.article_name articleName,
            t.correct_rate correctRate,
            t.queries queries,
            IF(t.annotator != #{taskDTO.annotatorId} or t.annotator is null ,0 , t.step) step,
            COALESCE ( t.update_time, n.update_time ) updateTime
        FROM
            (SELECT * FROM t_note WHERE deleted = 0) n
            LEFT JOIN (SELECT * FROM t_note_task WHERE deleted = 0) t ON ( n.note_id = t.note_id)
        <where>
            <if test="taskDTO.batchId != null"> and n.batch_id = #{taskDTO.batchId}</if>
            <if test="taskDTO.activeStep == 'unmarked' and taskDTO.annotatorId != null">
                AND (t.annotator is null OR t.annotator != #{taskDTO.annotatorId}) AND n.pull_count &lt; #{taskDTO.markRounds}
                AND n.note_id NOT IN ( SELECT note_id FROM t_note_task WHERE annotator = #{taskDTO.annotatorId} AND deleted = 0)</if>
            <if test="taskDTO.activeStep != 'unmarked' and taskDTO.annotatorId != null">
                AND (t.annotator = #{taskDTO.annotatorId} OR (t.annotator is null AND n.step = 0))</if>
            <if test="taskDTO.activeStep != 'unmarked' and taskDTO.step != null"> and COALESCE ( t.step, n.step ) in
                <foreach collection="taskDTO.step" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskDTO.articleId != null and taskDTO.articleId != ''"> and n.article_id = #{taskDTO.articleId}</if>
            <if test="taskDTO.articleName != null and taskDTO.articleName != ''"> and n.article_name like concat('%', #{taskDTO.articleName}, '%')</if>
            <if test="taskDTO.correctRate != null"> and t.correct_rate >= #{taskDTO.correctRate}</if>
            <if test="taskDTO.startDate != null"> and COALESCE ( t.update_time, n.update_time ) >= #{taskDTO.startDate}</if>
            <if test="taskDTO.endDate != null"> and COALESCE ( t.update_time, n.update_time ) &lt;= #{taskDTO.endDate}</if>
        </where>
        GROUP BY n.note_id
    </select>

    <!--审核员——任务列表-->
    <select id="queryAuditorTaskList" resultType="org.biosino.nlp.modules.note.vo.TaskListVO"
            parameterType="org.biosino.nlp.modules.note.dto.TaskDTO">
        SELECT
            n.note_id noteId,
            t.task_id taskId,
            n.article_id articleId,
            n.article_name articleName,
            t.step step,
            t.auditor auditorId,
            t.queries queries,
            t.update_time updateTime
        FROM
        (SELECT * FROM t_note_task  WHERE deleted = 0) t
        LEFT JOIN (SELECT * FROM t_note WHERE deleted = 0) n ON (
                t.note_id = n.note_id
                AND t.annotator = #{taskDTO.annotatorId}
                AND (n.step IN ( 2, 3, 4 )  or (n.step = 1 and t.step = 5) )
            )
        <where>
            <if test="taskDTO.batchId != null"> and n.batch_id = #{taskDTO.batchId} and (</if>
            <if test="taskDTO.auditorId != null"> t.auditor = #{taskDTO.auditorId} </if>
            <if test="taskDTO.auditorId == null"> t.auditor is not null </if>
            <if test="taskDTO.step.contains(2)"> OR (t.auditor is null AND n.step = 2) </if>
            <if test="taskDTO.step != null">) and COALESCE ( t.step, n.step ) in
                <foreach collection="taskDTO.step" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskDTO.articleId != null and taskDTO.articleId != ''"> and n.article_id = #{taskDTO.articleId}</if>
            <if test="taskDTO.articleName != null and taskDTO.articleName != ''"> and n.article_name like concat('%', #{taskDTO.articleName}, '%')</if>
            <if test="taskDTO.startDate != null"> and COALESCE ( t.update_time, n.update_time ) >= #{taskDTO.startDate}</if>
            <if test="taskDTO.endDate != null"> and COALESCE ( t.update_time, n.update_time ) &lt;= #{taskDTO.endDate}</if>
        </where>
    </select>

    <!--审核员——打回中的任务列表-->
    <select id="queryRepulseTaskList" resultType="org.biosino.nlp.modules.note.vo.TaskListVO"
            parameterType="org.biosino.nlp.modules.note.dto.TaskDTO">
        SELECT
            n.note_id noteId,
            t.task_id taskId,
            n.article_id articleId,
            n.article_name articleName,
            IF(n.step = 1 AND t.step = 6, 5, t.step) step,
            t.auditor auditorId,
            t.queries queries,
            t.update_time updateTime
        FROM
            ( SELECT * FROM t_note WHERE deleted = 0 ) n
            LEFT JOIN ( SELECT * FROM t_note_task WHERE deleted = 0 ) t ON ( n.note_id = t.note_id )
        <where>
            <if test="taskDTO.batchId != null"> and n.batch_id = #{taskDTO.batchId}</if>
            <if test="taskDTO.auditorId != null"> AND t.auditor = #{taskDTO.auditorId}</if>
            <if test="taskDTO.auditorId == null"> AND t.auditor is not null</if>
            <if test="taskDTO.annotatorId != null"> AND t.annotator = #{taskDTO.annotatorId}</if>
            <if test="taskDTO.step != null"> AND (t.step = 5 OR n.step = 1 AND t.step = 6)</if>
            <if test="taskDTO.articleId != null and taskDTO.articleId != ''"> and n.article_id = #{taskDTO.articleId}</if>
            <if test="taskDTO.articleName != null and taskDTO.articleName != ''"> and n.article_name like concat('%', #{taskDTO.articleName}, '%')</if>
            <if test="taskDTO.startDate != null"> and COALESCE ( t.update_time, n.update_time ) >= #{taskDTO.startDate}</if>
            <if test="taskDTO.endDate != null"> and COALESCE ( t.update_time, n.update_time ) &lt;= #{taskDTO.endDate}</if>
        </where>
    </select>

    <!--统计标注员待标注任务-->
    <select id="countUnmarkedTask" resultType="integer">
        SELECT count(*) FROM
            ( SELECT * FROM t_note WHERE project_id = #{projectId} AND deleted = 0 ) n
            LEFT JOIN ( SELECT * FROM t_note_task WHERE project_id = #{projectId} AND deleted = 0 ) t
            ON ( n.note_id = t.note_id )
        WHERE
            ( t.annotator IS NULL OR t.annotator != #{userId} )
            AND n.pull_count &lt; ( SELECT p.mark_rounds FROM t_project p WHERE p.project_id = #{projectId} )
    </select>

    <!--统计审核员待审核任务-->
    <select id="countUnreviewTask" resultType="integer">
        SELECT COUNT(DISTINCT n.note_id) FROM
                ( SELECT * FROM t_note_task WHERE deleted = 0 ) t
                    LEFT JOIN ( SELECT * FROM t_note WHERE deleted = 0 ) n ON (
                        t.note_id = n.note_id
                    AND (  n.step IN ( 2, 3, 4 )  OR ( n.step = 1 AND t.step = 5 )))
        WHERE
            n.project_id = #{projectId} AND ( t.auditor = #{userId}  OR ( t.auditor IS NULL AND n.step = 2 ))
                AND COALESCE ( t.step, n.step ) = 2
    </select>

    <!--查询当前批次下的标注员用户列表-->
    <select id="queryUserList" resultType="org.biosino.nlp.modules.note.vo.UserVO">
        SELECT
            u.user_id userId,
            u.username
        FROM
            sys_user u
        WHERE
            u.user_id IN (
        SELECT DISTINCT
            pu.user_id
        FROM
            t_project_user pu
        WHERE
            pu.project_id = ( SELECT b.project_id FROM `t_batch` b WHERE b.batch_id = #{batchId} )
            AND role_id = #{roleId}
            AND deleted = 0)
    </select>

    <!--审核员——任务列表-->
    <select id="queryAnnoDetail" resultType="org.biosino.nlp.modules.project.vo.AnnoDetailsVO">
        SELECT
        t.batch_id batchId,
        t.task_id taskId,
        n.article_id articleId,
        t.step,
        <if test="roleId == 2">
            t.anno_start_time annoStartTime,
            t.anno_end_time annoEndTime,
            TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) diffTime,
        </if>
        <if test="roleId == 3">
            t.audit_start_time auditStartTime,
            t.audit_end_time auditEndTime,
            TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) diffTime,
        </if>

        t.auditor auditorId,
        t.invalid invalid,

        t.correct_entity correctEntityCount,
        t.correct_attr correctAttrCount,
        t.correct_relation correctRelationGroupCount,

        t.correct_rate correctRate,
        t.need_total needTotal,
        t.correct_total correctTotal,
        t.error_total errorTotal,
        t.miss_total missTotal
        FROM
        `t_note_task` t
        LEFT JOIN t_note n ON ( t.note_id = n.note_id )
        <where>
            <if test="projectId != null "> AND t.project_id = #{projectId} AND t.deleted = 0</if>
            <if test="roleId == 2"> AND t.annotator = #{userId}</if>
            <if test="roleId == 3"> AND t.auditor = #{userId} AND t.`master` = 1 </if>
        </where>
        ORDER BY t.batch_id, t.step
    </select>

    <!--审核员——分配新任务-->
    <select id="assignAuditorTask" resultType="org.biosino.nlp.modules.note.entity.NoteTask">
        SELECT
            t.*
        FROM
            ( SELECT * FROM t_note_task WHERE deleted = 0 ) t
              LEFT JOIN ( SELECT * FROM t_note WHERE deleted = 0 ) n ON ( t.note_id = n.note_id
              AND t.annotator = #{annotatorId}
              AND t.annotator != #{auditorId}
              AND (n.step IN ( 2, 3, 4 ) OR ( n.step = 1 AND t.step = 5 )))
        WHERE
            n.batch_id = #{batchId}
          AND (t.auditor = #{auditorId} OR ( t.auditor IS NULL AND n.step = 2 ))
          AND COALESCE ( t.step, n.step ) = 2
        ORDER BY
            RAND()
            LIMIT 1
    </select>

    <!--真删除Task-->
    <delete id="reallyDeleteByTaskId">
        delete from t_note_task where task_id = #{taskId}
    </delete>

    <delete id="reallyDeleteByNoteId">
        delete from t_note_task where note_id = #{noteId}
    </delete>
</mapper>
