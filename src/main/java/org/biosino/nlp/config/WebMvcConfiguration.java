package org.biosino.nlp.config;

import org.biosino.nlp.common.utils.Constant;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web相关配置（拦截器，跨域等）
 *
 * <AUTHOR>
 */
@Primary
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/file/**").addResourceLocations("file:" + Constant.dataHome + "/" + Constant.DirectoryEnum.pdf + "/");
        registry.addResourceHandler("/temp/**").addResourceLocations("file:" + Constant.dataHome + "/" + Constant.DirectoryEnum.temp + "/");
    }

}
