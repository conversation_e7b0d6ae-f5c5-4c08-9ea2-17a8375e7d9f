package org.biosino.nlp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * NLP 2.0
 * 项目启动入口
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@EnableScheduling
@EnableCaching
@SpringBootApplication
public class NLPApplication {

    public static void main(String[] args) {
        SpringApplication.run(NLPApplication.class, args);
    }

}
