package org.biosino.nlp.modules.project.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.common.validator.ValidatorUtils;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.note.service.DeletedService;
import org.biosino.nlp.modules.project.dto.ExportDTO;
import org.biosino.nlp.modules.project.dto.ProjectDTO;
import org.biosino.nlp.modules.project.entity.Attachment;
import org.biosino.nlp.modules.project.entity.ExportLog;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.service.*;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
@RestController
@RequestMapping("project")
public class ProjectController extends AbstractController {

    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private DeletedService deletedService;

    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private PreAnnotationService preAnnotationService;
    @Autowired
    private DataExportService dataExportService;

    /**
     * 参与的项目批次
     */
    @RequestMapping("/participateProject")
    public R participateProject(Long roleId) {
        List<Project> list = projectService.participateProject(getUserId(), roleId);
        return R.success(list);
    }

    /**
     * 项目列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("project:manage")
    public R list(ProjectDTO projectDTO) {
        PageUtils page = projectService.queryPage(projectDTO, getUserId());
        return R.ok().put("page", page);
    }

    /**
     * 信息（编辑时回显）
     */
    @RequestMapping("/info/{projectId}")
    @RequiresPermissions("project:manage")
    public R info(@PathVariable("projectId") Integer projectId) {
        Project project = projectService.getById(projectId);
        return R.ok().put("project", project);
    }

    /**
     * 保存
     */
    @SysLog("保存项目")
    @RequestMapping("/save")
    @RequiresPermissions("project:manage")
    public R save(@RequestBody Project project) {
        projectService.saveOrUpdate(project, getUserId());
        return R.ok();
    }

    /**
     * 预标注来源
     */
    @RequestMapping("/preSources")
    @RequiresPermissions("project:manage")
    public R getPreSources() {
        Map<String, String> map = new HashMap<>(4);
        for (PreSourceEnum value : PreSourceEnum.values()) {
            if (value.equals(PreSourceEnum.CUSTOMIZE)) {
                continue;
            }
            map.put(value.name(), value.getTitle());
        }
        return R.success(map);
    }

    /**
     * 查询当前用户下的所有项目
     */
    @RequestMapping("/findAllProject")
    @RequiresPermissions("project:manage")
    public R getAllProject() {
        Map<Long, String> map = projectService.findAllProject(getUserId());
        return R.success(map);
    }

    /**
     * 上传预标注数据
     */
    @RequestMapping("/uploadPreAnno/{type}")
    @RequiresPermissions("project:manage")
    public R loadPreAnno(@PathVariable String type, Long projectId, @RequestParam("file") MultipartFile multipartFile) {
        preAnnotationService.uploadPreAnno(type, projectId, multipartFile, getUserId());
        return R.ok();
    }

    /**
     * 删除预计标准数据
     */
    @RequestMapping("/deletePreAnno/{type}")
    @RequiresPermissions("project:manage")
    public R deletePreAnno(@PathVariable String type, Long id) {
        preAnnotationService.deletePreAnno(type, id);
        return R.ok();
    }

    /**
     * 整合预标注实体到原文
     */
    @RequestMapping("/mergeEntity")
    @RequiresPermissions("project:manage")
    public R mergeEntity(Long projectId) {
        preAnnotationService.mergeEntity(projectId);
        return R.ok();
    }



    /**
     * 删除项目
     */
    @SysLog("删除项目")
    @RequestMapping("/delete")
    @RequiresPermissions("project:manage")
    public R delete(Long projectId) {
        deletedService.deleteProject(projectId, getUserId());
        return R.ok();
    }

    /**
     * 查询所有启用的用户
     */
    @RequestMapping("/user")
    @RequiresPermissions("project:manage")
    public R userList() {
        List<SysUserEntity> list = userService.queryUsers();
        return R.ok().put("users", list);
    }

    /**
     * 查询项目的参与者
     */
    @RequestMapping("/participantUser")
    @RequiresPermissions("project:manage")
    public R participantUser(Long projectId) {
        List<ProjectUser> users = projectUserService.queryUsers(projectId);
        return R.success(users);
    }

    /**
     * 项目名查重
     */
    @RequestMapping("/validate/projectName")
    public R validateProject(String projectName, Long projectId) {
        projectService.validateProjectName(projectName, projectId, getUserId());
        return R.ok();
    }

    /**
     * 某个项目的所有实体标签
     */
    @RequestMapping("/labels")
    @RequiresPermissions("project:manage")
    public R labels(Long projectId) {
        List<EntityLabel> entityLabels = projectService.queryLabels(projectId);
        return R.ok().put("entityLabels", entityLabels);
    }

    /**
     * 重置项目唯一码
     */
    @SysLog("重置项目唯一码")
    @RequestMapping("/resetToken")
    @RequiresPermissions("project:manage")
    public R resetToken(Long projectId) {
        projectService.resetToken(projectId, getUserId());
        return R.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/exportAnnotation")
    public R exportAnnotation(@RequestBody ExportDTO dto) throws IOException {
        ValidatorUtils.validateEntity(dto);
        dto.setUserId(getUserId());
        dataExportService.generateExportLog(dto);
        return R.ok();
    }

    /**
     * 查询导出记录
     */
    @GetMapping("/findExportLog")
    public R findExportLog(Long projectId) {
        ExportLog exportLog = dataExportService.findExportLog(projectId);
        return R.success(exportLog);
    }

}
