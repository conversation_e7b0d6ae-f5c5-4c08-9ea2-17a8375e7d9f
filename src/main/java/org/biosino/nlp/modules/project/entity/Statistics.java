package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 统计日志
 *
 * <AUTHOR>
 * @date 2021-01-04 11:13
 */
@Data
@TableName("l_statistics_project")
public class Statistics implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private Long projectId;
    private Integer unmarked;
    private Integer marked;
    private Integer reviewed;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

}
