package org.biosino.nlp.modules.project.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/10
 */
@Data
public class RelationPreAnnoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String articleId;

    private List<RelationItem> items = new ArrayList<>();

    @Data
    public static class RelationItem {
        private Integer order;

        private List<EntityAttrPreAnnoDTO.Info> subject = new ArrayList<>();

        private String subjectForeign;

        private Boolean negation;

        private List<String> annotations = new ArrayList<>();

        private String relation;

        private List<EntityAttrPreAnnoDTO.Info> objects = new ArrayList<>();

        private String objectsForeign;

    }

    @NotNull
    private String pattern;

}
