package org.biosino.nlp.modules.project.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * AI预标注任务VO
 *
 * @date 2023/11/28
 */
@Data
public class AIPreAnnoTaskVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    private String projectName;

    /**
     * 金标准批次ID
     */
    private Long sourceBatchId;

    private String sourceBatchName;

    /**
     * 金标准文献ID列表
     */
    private List<String> sourceArticleIds;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务状态: 0-运行中, 1-成功, 2-失败
     */
    private Integer status;

    /**
     * 用户输入的提示词
     */
    private String prompt;

    /**
     * 待标注文献批次ID
     */
    private Long batchId;

    private String batchName;

    /**
     * 待标注文献ID列表
     */
    private List<String> articleIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long creator;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 已处理的数量
     */
    private Integer processSize;

    /**
     * 处理速度
     */
    private Long processSpeed;

    /**
     * 已耗时
     */
    private String processTime;

    /**
     * 预计剩余耗时
     */
    private String processLeftTime;

    /**
     * Prompt消耗的token数
     */
    private Long promptTokens;

    /**
     * Completion消耗的token数
     */
    private Long completionTokens;

    /**
     * 总token消耗数
     */
    private Long totalTokens;
}
