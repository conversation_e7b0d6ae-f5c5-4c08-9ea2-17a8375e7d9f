package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.modules.project.dao.LoadArticleDao;
import org.biosino.nlp.modules.project.entity.LoadDocument;
import org.biosino.nlp.modules.project.service.LoadDocumentService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-15 09:57
 */
@Service
public class LoadDocumentServiceImpl extends ServiceImpl<LoadArticleDao, LoadDocument> implements LoadDocumentService {
    @Override
    public List<LoadDocument> findAllByImportIdIn(Collection<Long> importLogIds) {
        if (CollUtil.isEmpty(importLogIds)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<LoadDocument>lambdaQuery().in(LoadDocument::getImportLogId, importLogIds));
    }

    @Override
    public void deletedByArticleId(Long batchId, String articleId) {
        LambdaQueryWrapper<LoadDocument> wrapper = new LambdaQueryWrapper<LoadDocument>().
                eq(LoadDocument::getBatchId, batchId)
                .eq(LoadDocument::getArticleId, articleId);
        this.remove(wrapper);
    }

    @Override
    public void deletedByBatchId(Long batchId) {
        LambdaQueryWrapper<LoadDocument> wrapper = new LambdaQueryWrapper<LoadDocument>().
                eq(LoadDocument::getBatchId, batchId);
        this.remove(wrapper);
    }
}
