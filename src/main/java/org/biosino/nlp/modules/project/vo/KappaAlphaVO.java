package org.biosino.nlp.modules.project.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
public class KappaAlphaVO implements Serializable {
    private List<String> annotater;
    // Fleiss' Kappa (κ)
    private Double entityKappa = 0D;
    private Double attrKappa = 0D;

    // <PERSON><PERSON><PERSON><PERSON>f's Alpha（α）
    private Double entityAlpha = 0D;
    private Double attrAlpha = 0D;
}
