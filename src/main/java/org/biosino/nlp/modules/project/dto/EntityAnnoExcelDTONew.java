package org.biosino.nlp.modules.project.dto;

import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体导出类
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class EntityAnnoExcelDTONew implements Serializable {

    private static final long serialVersionUID = -7242738411604709117L;

    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "文本", order = 2)
    private String text;

    @ExcelProperty(value = "实体类型", order = 3)
    private String type;

    @ExcelProperty(value = "实体名", order = 4)
    private String name;

    @ExcelProperty(value = "实体消歧名称", order = 5)
    private String conceptText;

    @ExcelProperty(value = "实体消歧ID", order = 6)
    private String conceptId;

    @ExcelProperty(value = "实体消歧来源", order = 7)
    private String conceptSource;

    @ExcelProperty(value = "实体属性", order = 8)
    private String attr;

    @ExcelProperty(value = "实体属性详情", order = 9)
    private String attrInfo;

    @ExcelProperty(value = "实体起始位", order = 10)
    private String start;

    @ExcelProperty(value = "实体结束位", order = 11)
    private String end;

    @ExcelProperty(value = "实体批注信息", order = 12)
    private String annotate;

    @ExcelProperty(value = "标注人", order = 13)
    private String annotatorName;

    @ExcelProperty(value = "审核人", order = 14)
    private String auditorName;

    @ExcelProperty(value = "标注时间", order = 15)
    private Date createTime;

}
