package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.biosino.nlp.modules.note.entity.Note;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-12-15 09:39
 */
@Data
@TableName("l_load_document")
public class LoadDocument implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String no;
    private Long importLogId;
    private Long projectId;
    private Long batchId;
    private String articleId;
    private String documentId;
    private Date createTime;
    private Date updateTime;
    /**
     * 导入结果
     * ImportResultEnum: 导入结果
     */
    private Integer status;
    private String msg;

    @TableField(exist = false)
    private Note note;

}
