package org.biosino.nlp.modules.project.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.nlp.modules.project.entity.ImportLog;

/**
 * <AUTHOR>
 * @date 2024-12-27 10:46
 */
@Mapper
public interface ImportLogDao extends BaseMapper<ImportLog> {

    /**
     * 查询日志，数据有可能是删除的
     */
    @Select("SELECT  * FROM t_import_log WHERE id = #{id}")
    ImportLog findById(@Param("id") Long id);
}
