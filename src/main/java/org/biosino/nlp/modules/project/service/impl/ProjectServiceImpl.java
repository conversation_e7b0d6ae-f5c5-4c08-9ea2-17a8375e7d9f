package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.DownloadUtil;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.labels.entity.*;
import org.biosino.nlp.modules.labels.service.*;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.dto.ProjectDTO;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.service.PreAnnotationService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.biosino.nlp.modules.project.service.ProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:30
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectDao, Project> implements ProjectService {

    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private EntityLabelService entityLabelService;
    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private RelationLabelService relationLabelService;
    @Autowired
    private RelationPatternService relationPatternService;
    @Autowired
    private UmlsConceptService umlsConceptService;
    @Lazy
    @Autowired
    private PreAnnotationService preAnnotationService;

    @Override
    public PageUtils queryPage(ProjectDTO dto, Long userId) {
        List<Long> projectIds = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                        .eq(ProjectUser::getUserId, userId).eq(ProjectUser::getRoleId, dto.getRoleId()))
                .stream().map(ProjectUser::getProjectId).collect(Collectors.toList());

        LambdaQueryWrapper<Project> wrapper = Wrappers.lambdaQuery();
        if (CollUtil.isNotEmpty(projectIds)) {
            wrapper.and((obj) -> {
                obj.eq(Project::getCreatorId, userId).or().in(Project::getProjectId, projectIds);
            });
        } else {
            wrapper.eq(Project::getCreatorId, userId);
        }

        wrapper.like(StrUtil.isNotBlank(dto.getName()), Project::getName, dto.getName())
                .eq(dto.getStatus() != null, Project::getStatus, dto.getStatus())
                .ge(dto.getStartDate() != null, Project::getCreateTime, dto.getStartDate())
                .le(dto.getEndDate() != null, Project::getCreateTime, DateUtil.format(dto.getEndDate(), "yyyy-MM-dd 23:59:59"));

        IPage<Project> page = this.page(new Query<Project>().getPage(dto, "createTime", false, true), wrapper);
        page.getRecords().forEach(it -> {
            it.setIsAuth(it.getCreatorId().equals(userId));
        });
        return new PageUtils(page);
    }

    @Override
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Project project, Long userId) {
        final Date currentDate = new Date();

        Integer markRounds = project.getMarkRounds();
        List<Long> annotators = project.getAnnotators();
        if (annotators.size() < markRounds) {
            throw new RRException("标注员数量不能少于标注轮数");
        }

        // 更新
        if (project.getProjectId() != null) {
            Project oldProject = Optional.ofNullable(this.getOne(Wrappers.<Project>lambdaQuery()
                    .eq(Project::getProjectId, project.getProjectId()))
            ).orElseThrow(() -> new RRException("项目不存在"));

            // 删除原有成员
            projectUserService.remove(Wrappers.<ProjectUser>lambdaQuery().eq(ProjectUser::getProjectId, project.getProjectId()));

            project.setCreateTime(oldProject.getCreateTime());
            project.setUpdateTime(currentDate);

            this.saveOrUpdate(project);

            // 项目标注说明markdown内容已通过saveOrUpdate自动处理，无需额外处理
            if (project.getPreSources() == null && oldProject.getPreSources() != null) {
                this.update(Wrappers.<Project>lambdaUpdate()
                        .eq(Project::getProjectId, project.getProjectId())
                        .set(Project::getPreSources, null));
            }

            // 拉取预标注信息
            if (project.getPreSources() != null) {
                Arrays.stream(project.getPreSources().split(","))
                        .map(PreSourceEnum::valueOf)
                        .parallel()
                        .forEach(source -> preAnnotationService.savePreAnnotation(project.getProjectId(), source));
            }
            // 新增
        } else {
            project.setCreatorId(userId);
            project.setCreateTime(currentDate);
            project.setUpdateTime(currentDate);
            project.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
            saveOrUpdate(project);

            // 判断是否选择其他项目的配置文件
            boolean checkConfig = project.getProjectConfig() != null;
            Long projectId = checkConfig ? project.getProjectConfig() : 0L;

            // 实体标签
            List<EntityLabel> entityLabels = entityLabelService.list(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getProjectId, projectId));
            for (EntityLabel entityLabel : entityLabels) {
                final Long labelId = entityLabel.getId();
                entityLabel.setCreateTime(currentDate);
                entityLabel.setId(null);
                if (!checkConfig) {
                    entityLabel.setStatus(StatusEnum.disable.getValue());
                }
                entityLabel.setProjectId(project.getProjectId());
                entityLabelService.saveOne(entityLabel);
                // 查询当前根标签的属性
                List<AttributeLabel> attributeLabels = attributeLabelService.findAllByEntityLabelId(labelId);
                if (CollUtil.isNotEmpty(attributeLabels)) {
                    for (AttributeLabel attributeLabel : attributeLabels) {
                        attributeLabel.setId(null);
                        attributeLabel.setEntityLabelId(entityLabel.getId());
                        attributeLabel.setCreateTime(currentDate);
                        attributeLabel.setStatus(StatusEnum.enable.getValue());
                        attributeLabelService.save(attributeLabel);
                    }
                }
            }

            // 关系标签
            List<RelationLabel> relationLabels = relationLabelService.list(Wrappers.<RelationLabel>lambdaQuery().eq(RelationLabel::getProjectId, projectId));
            relationLabels.forEach(relationLabel -> {
                relationLabel.setProjectId(project.getProjectId());
                relationLabel.setCreateTime(currentDate);
                if (!checkConfig) {
                    relationLabel.setStatus(StatusEnum.disable.getValue());
                }
                relationLabel.setUserId(userId);
            });
            relationLabelService.saveBatch(relationLabels);

            // 从公有关系模板创建公有关系模板
            List<RelationPattern> relationPatterns = relationPatternService.list(Wrappers.<RelationPattern>lambdaQuery()
                    .eq(RelationPattern::getProjectId, projectId));
            relationPatterns.forEach(relationPattern -> {
                relationPattern.setProjectId(project.getProjectId());
                relationPattern.setCreateTime(currentDate);
                if (!checkConfig) {
                    relationPattern.setStatus(StatusEnum.disable.getValue());
                }
            });

            relationPatternService.saveBatch(relationPatterns);

            // UMLS Concept
            List<UmlsConcept> umlsConcepts = umlsConceptService.list(Wrappers.<UmlsConcept>lambdaQuery()
                    .eq(UmlsConcept::getProjectId, projectId));
            umlsConcepts.forEach(umlsConcept -> {
                umlsConcept.setCreateTime(currentDate);
                if (!checkConfig) {
                    umlsConcept.setStatus(StatusEnum.disable.getValue());
                }
                umlsConcept.setCreater(userId);
                umlsConcept.setProjectId(project.getProjectId());
            });
            umlsConceptService.saveBatch(umlsConcepts);
        }

        if (CollUtil.isNotEmpty(project.getAnnotators())) {
            saveProjectUserBatch(project.getAnnotators(), project, RoleEnum.annotator.getId());
        }
        if (CollUtil.isNotEmpty(project.getAuditors())) {
            saveProjectUserBatch(project.getAuditors(), project, RoleEnum.auditor.getId());
        }
        project.getProjectAdmins().add(project.getCreatorId());
        saveProjectUserBatch(project.getProjectAdmins(), project, RoleEnum.projectAdmin.getId());
        if (CollUtil.isNotEmpty(project.getProjectWatchers())) {
            saveProjectUserBatch(project.getProjectWatchers(), project, RoleEnum.projectWatcher.getId());
        }
    }

    private void saveProjectUserBatch(List<Long> ids, Project project, Long roleId) {
        projectUserService.saveBatch(ids.stream().distinct().map(it -> {
            ProjectUser projectUser = new ProjectUser();
            projectUser.setProjectId(project.getProjectId());
            projectUser.setUserId(it);
            projectUser.setRoleId(roleId);
            return projectUser;
        }).collect(Collectors.toList()));
    }

    @Override
    public List<Project> participateProject(Long userId, Long roleId) {
        return baseMapper.queryProjectParticipate(userId, roleId);
    }

    @Override
    public List<EntityLabel> queryLabels(Long projectId) {
        return entityLabelService.list(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getProjectId, projectId));
    }

    /**
     * 根据token查询找项目
     *
     * @param token 项目标识码
     * @return 项目
     */
    @Override
    public Project findByToken(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        LambdaQueryWrapper<Project> wrapper = Wrappers.<Project>lambdaQuery()
                .eq(Project::getCode, token);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public void resetToken(Long projectId, Long userId) {
        Project project = this.getById(projectId);
        if (project == null) {
            throw new RRException("项目不存在");
        }
        if (!project.getCreatorId().equals(userId)) {
            throw new RRException("没有权限操作");
        }
        this.update(Wrappers.<Project>lambdaUpdate()
                .eq(Project::getProjectId, projectId)
                .set(Project::getCode, UUID.randomUUID().toString().replaceAll("-", "")));
    }



    @Override
    public Map<Long, String> findAllProject(Long userId) {
        if (userId == null) {
            return null;
        }
        List<ProjectUser> projectUsers = projectUserService.queryProjectByUser(userId);
        Set<Long> projectIdSet = new HashSet<>();
        if (CollUtil.isNotEmpty(projectUsers)) {
            for (ProjectUser projectUser : projectUsers) {
                if (RoleEnum.projectAdmin.getId() == projectUser.getRoleId()
                        || RoleEnum.projectWatcher.getId() == projectUser.getRoleId()) {
                    projectIdSet.add(projectUser.getProjectId());
                }
            }
        }
        if (CollUtil.isNotEmpty(projectIdSet)) {
            LambdaQueryWrapper<Project> wrapper = Wrappers.<Project>lambdaQuery()
                    .in(CollUtil.isNotEmpty(projectIdSet), Project::getProjectId, projectIdSet)
                    .select(Project::getProjectId, Project::getName);
            List<Project> list = this.list(wrapper);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().collect(Collectors.toMap(Project::getProjectId, Project::getName));
            }
        }
        return null;
    }

    @Override
    public void validateProjectName(String projectName, Long projectId, Long userId) {
        if (StringUtils.isBlank(projectName)) {
            throw new RRException("名称不能为空");
        }
        projectName = projectName.trim();
        int count = this.count(Wrappers.<Project>lambdaQuery()
                .eq(Project::getName, projectName)
                .eq(Project::getCreatorId, userId)
                .ne(projectId != null, Project::getProjectId, projectId));
        if (count > 0) {
            throw new RRException("名称重复");
        }
    }

}
