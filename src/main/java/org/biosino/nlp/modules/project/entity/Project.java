package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
@Data
@TableName("t_project")
public class Project implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long projectId;
    private String code;
    private String name;
    /**
     * 多人标注，1是单人标注，N是N是标注，N为1~5
     */
    private Integer markRounds;
    private Boolean autoReview;
    private Long creatorId;
    private String description;
    private Date createTime;
    private Date updateTime;
    private Integer status;
    /**
     * 预标注来源，','拼接
     */
    private String preSources;
    /**
     * 选择的配置文件
     */
    @TableField(exist = false)
    private Long projectConfig;

    /**
     * 标注说明markdown内容
     */
    private String markdownContent;

    @TableLogic
    private Integer deleted;
    /**
     * 标注员
     */
    @TableField(exist = false)
    private List<Long> annotators;
    /**
     * 审批员
     */
    @TableField(exist = false)
    private List<Long> auditors;
    /**
     * 项目共享管理员
     */
    @TableField(exist = false)
    private List<Long> projectAdmins;
    /**
     * 项目观察员
     */
    @TableField(exist = false)
    private List<Long> projectWatchers;
    /**
     * 批次
     */
    @TableField(exist = false)
    private List<Batch> batches;

    @TableField(exist = false)
    private Boolean isAuth;

}
