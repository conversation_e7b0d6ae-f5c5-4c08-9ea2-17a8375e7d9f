package org.biosino.nlp.modules.project.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.note.service.DeletedService;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.project.dto.ArticleListDTO;
import org.biosino.nlp.modules.project.dto.BatchDTO;
import org.biosino.nlp.modules.project.dto.LoadArticleDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.LoadDocument;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.vo.ArticleListVO;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 批次管理
 *
 * <AUTHOR>
 * @date 2020-12-04 10:58
 */
@RestController
@RequestMapping("/batch")
public class BatchController extends AbstractController {

    @Autowired
    private BatchService batchService;
    @Autowired
    private DeletedService deletedService;

    @SysLog("保存批次")
    @RequestMapping("/save")
    @RequiresPermissions("project:manage")
    public R save(@RequestBody Batch batch) {
        batchService.saveOrUpdate(batch, getUserId());
        return R.ok();
    }

    /**
     * 批次列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("project:manage")
    public R list(BatchDTO batchDTO) {
        PageUtils page = batchService.queryPage(batchDTO);
        return R.ok().put("page", page);
    }

    @SysLog("删除批次")
    @RequestMapping("/delete")
    @RequiresPermissions("project:manage")
    public R delete(Long batchId) {
        deletedService.deleteBatch(batchId);
        return R.ok();
    }

    /**
     * 单条批次详细信息
     */
    @RequestMapping("/info")
    @RequiresPermissions("project:manage")
    public R labels(Long batchId) {
        Batch batch = batchService.queryBatch(batchId);
        return R.ok().put("batch", batch);
    }

    /**
     * 上传文件导入文章数据
     */
    @SysLog("导入JSON文章")
    @RequestMapping("/uploadArticles")
    public R uploadArticles(MultipartFile file, Long batchId) {
        batchService.uploadArticles(file, batchId, getUserId());
        return R.ok();
    }

    /**
     * 导入文章
     */
    @SysLog("导入PMID文章")
    @RequestMapping("/loadArticles")
    public R loadArticles(MultipartFile file, Long batchId) throws IOException {
        batchService.loadArticles(file, batchId, getUserId());
        return R.ok();
    }

    /**
     * 查询文章列表
     */
    @RequestMapping("/getArticleList")
    public R getArticleList(ArticleListDTO dto) {
        PageIdsListVO vo = batchService.getArticleList(dto);
        vo.setListData(new PageUtils((IPage<ArticleListVO>) vo.getListData()));
        return R.success(vo);
    }

    /**
     * 查询导入文章列表
     */
    @RequestMapping("/loadArticleList")
    public R loadArticleList(@RequestBody LoadArticleDTO dto) {
        PageUtils page = batchService.loadArticleList(dto, getUserId());
        return R.ok().put("page", page);
    }

    /**
     * 批量提交
     */
    @SysLog("批量提交")
    @RequestMapping("/batchSubmit")
    @RequiresPermissions("project:editable")
    public R batchSubmit(@RequestBody Long[] ids) {
        if (CollUtil.isEmpty(CollUtil.newArrayList(ids))) {
            throw new RRException("操作失败，ID不能为空");
        }
        batchService.batchSubmit(ids);
        return R.ok();
    }

    /**
     * 重置/删除文章
     */
    @SysLog("重置/删除文章")
    @RequestMapping("/resetArticle/{deleted}")
    @RequiresPermissions("project:editable")
    public R resetArticle(@RequestBody Long[] ids, @PathVariable Boolean deleted) {
        if (CollUtil.isEmpty(CollUtil.newArrayList(ids))) {
            throw new RRException("操作失败，ID不能为空");
        }
        for (Long id : ids) {
            deletedService.resetArticle(id, deleted);
        }
        return R.ok();
    }

    /**
     * 查询文章导入结果
     */
    @RequestMapping("/loadArticleResult")
    public R loadArticleResult(Long importLogId) {
        List<LoadDocument> result = batchService.loadArticleResult(importLogId, getUserId());
        return R.ok().put("result", result);
    }

    /**
     * 重新导入失败的文章
     */
    @RequestMapping("/reloadArticles")
    public R reloadArticles(Long importLogId) {
        batchService.reloadArticle(importLogId, getUserId());
        return R.ok();
    }

    /**
     * 导出"文章导入"的结果
     */
    @RequestMapping("/exportArticleResult")
    public void exportArticleResult(Long importLogId, HttpServletResponse response) throws IOException {
        batchService.exportArticleResult(importLogId, getUserId(), response);
    }

    @RequestMapping("/validate/batchName")
    public R validateProject(String batchName, Long projectId, Long batchId) {
        batchService.validateBatchName(batchName, projectId, batchId);
        return R.ok();
    }

    @SysLog("修改文章状态(启用/弃用)")
    @RequestMapping("/changeNoteArticleStatus")
    public R changeNoteArticleStatus(Long batchId, String articleId) {
        batchService.changeNoteArticleStatus(batchId, articleId, getUserId());
        return R.ok();
    }

    @SysLog("重置文章")
    @RequestMapping("/resetNoteArticle")
    public R resetNoteArticle(@RequestBody Long noteId) {
        batchService.resetNoteArticle(noteId);
        return R.ok();
    }

    /**
     * 批量改变标注状态
     */
    @SysLog("批量改变标注状态")
    @RequestMapping("/batchReset")
    public R batchReset(@RequestBody Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new RRException("请选择文章");
        }
        for (Long id : ids) {
            batchService.resetNoteArticle(id);
        }
        return R.ok();
    }

}
