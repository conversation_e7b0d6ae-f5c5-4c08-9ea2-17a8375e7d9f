package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.biosino.nlp.common.enums.*;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.JsonValidator;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.api.service.impl.ArticleServiceImpl;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.DocumentRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.Operation;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.CommentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.service.OpertionService;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.note.vo.PageIdsVO;
import org.biosino.nlp.modules.project.dao.BatchDao;
import org.biosino.nlp.modules.project.dto.*;
import org.biosino.nlp.modules.project.entity.*;
import org.biosino.nlp.modules.project.service.*;
import org.biosino.nlp.modules.project.vo.ArticleListVO;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserRoleService;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static org.biosino.nlp.common.utils.Constant.LIMIT_ONE;

/**
 * <AUTHOR>
 * @date 2020-12-04 10:55
 */
@Service
public class BatchServiceImpl extends ServiceImpl<BatchDao, Batch> implements BatchService {
    private final String[] ESCAPE_CHARS = {"\b", "\r", "\n", "\t", "\f", "\u000B"};

    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private LoadDocumentService loadDocumentService;
    @Autowired
    private NoteService noteService;
    @Autowired
    private NoteDao noteDao;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private NoteTaskService noteTaskService;
    @Autowired
    private DocumentRepository documentRepository;
    @Lazy
    @Autowired
    private ArticleApiService articleApiService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private RelationshipRepository relationshipRepository;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private OpertionService opertionService;
    @Autowired
    private ImportLogService importLogService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private CommentService commentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Batch batch, Long creatorId) {
        if (batch == null || batch.getProjectId() == null) {
            throw new RRException("请先选择项目");
        }

        int count = projectService.count(Wrappers.<Project>lambdaQuery().eq(Project::getProjectId, batch.getProjectId()));
        if (count != 1) {
            throw new RRException("请先选择项目");
        }

        int batchCount = this.count(Wrappers.<Batch>lambdaQuery().eq(Batch::getName, batch.getName()).ne(batch.getBatchId() != null, Batch::getBatchId, batch.getBatchId()).eq(Batch::getProjectId, batch.getProjectId()));
        if (batchCount != 0) {
            throw new RRException("项目已存在");
        }

        Date currentDate = new Date();
        if (batch.getBatchId() != null) {
            Batch oldBatch = this.getById(batch.getBatchId());

            batch.setCreateTime(oldBatch.getCreateTime());
            batch.setTotalArticle(oldBatch.getTotalArticle());
            // batch.setImportStatus(oldBatch.getImportStatus());
        } else {
            batch.setCreateTime(currentDate);
            // batch.setImportStatus(ImportStatusEnum.wait.getValue());
            batch.setTotalArticle(0);
        }
        batch.setUpdateTime(currentDate);

        this.saveOrUpdate(batch);
    }

    @Override
    public Batch queryBatch(Long batchId) {
        return this.getById(batchId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadArticles(MultipartFile multipartFile, Long batchId, Long userId) throws IOException {
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        if (!"txt".equalsIgnoreCase(suffix)) {
            throw new RRException("请上传 txt 格式文本");
        }

        // 验证权限
        Batch batch = this.getById(batchId);
        if (batch == null) {
            throw new RRException("该批次不存在");
        }
        Project project = projectService.getById(batch.getProjectId());
        if (project == null || !CollUtil.containsAny(sysUserRoleService.queryRoleIdList(userId), Arrays.asList(RoleEnum.projectAdmin.getId(), RoleEnum.projectWatcher.getId()))) {
            throw new RRException("没有权限操作");
        }

        BufferedReader reader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()));
        List<String> list = reader.lines().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        if (list.isEmpty()) {
            throw new RRException("文本内容为空");
        }

        Date currentDate = new Date();
        List<LoadDocument> loadDocuments = list.stream().map(it -> {
            LoadDocument loadDocument = new LoadDocument();
            loadDocument.setId(batchId + "_" + it);
            loadDocument.setNo(it);
            loadDocument.setArticleId(null);
            loadDocument.setBatchId(batchId);
            loadDocument.setStatus(ImportResultEnum.wait.getValue());
            loadDocument.setProjectId(project.getProjectId());
            loadDocument.setCreateTime(currentDate);
            loadDocument.setUpdateTime(currentDate);
            return loadDocument;
        }).collect(Collectors.toList());
        loadDocumentService.saveBatch(loadDocuments);
        articleApiService.importArticles(loadDocuments);
    }

    @Override
    @SneakyThrows
    public void uploadArticles(MultipartFile multipartFile, Long batchId, Long userId) {
        String originalFilename = multipartFile.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new RRException("文件名不合法，不能为空");
        }
        String suffix = FileUtil.getSuffix(originalFilename);
        boolean isJsonFile = "json".equalsIgnoreCase(suffix);
        boolean isTxtFile = "txt".equalsIgnoreCase(suffix);
        boolean isZipFile = "zip".equalsIgnoreCase(suffix);
        if (!isJsonFile && !isTxtFile && !isZipFile) {
            throw new RRException("请上传 json、txt 或 zip 格式文件");
        }
        if (multipartFile.getSize() > 100 * 1024 * 1024) {
            throw new RRException("请上传的文件不能大于100M");
        }
        // ZIP 文件跳过内容验证，其他文件进行内容验证
        if (!isZipFile) {
            InputStream inputStream = null;
            BufferedReader bufferedReader = null;
            try {
                inputStream = multipartFile.getInputStream();
                // 上传的文件内容不能为空
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                if (!bufferedReader.lines().filter(StrUtil::isNotBlank).distinct().findAny().isPresent()) {
                    throw new RRException("文件内容不能为空");
                }
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            } finally {
                IoUtil.close(bufferedReader);
                IoUtil.close(inputStream);
            }

            String content = StrUtil.utf8Str(multipartFile.getBytes());
            if (isJsonFile && !JsonValidator.isJsonValid(content)) {
                throw new RRException("文件内容不是合法的json字符串");
            }
        }

        // 验证权限
        Batch batch = this.getById(batchId);
        if (batch == null) {
            throw new RRException("该批次不存在");
        }
        Project project = projectService.getById(batch.getProjectId());
        if (project == null || !CollUtil.containsAny(sysUserRoleService.queryRoleIdList(userId), Arrays.asList(RoleEnum.projectAdmin.getId(), RoleEnum.projectWatcher.getId()))) {
            throw new RRException("没有权限操作");
        }

        // 生成导入记录
        ImportLog importLog = new ImportLog();
        importLog.setName(originalFilename);
        importLog.setProjectId(project.getProjectId());
        importLog.setBatchId(batchId);
        importLog.setStatus(ImportStatusEnum.wait.getValue());
        importLog.setCreateTime(new Date());
        importLog.setUserId(userId);
        // 将上传的文件落盘
        File homeDir = Constant.getHomeDir(Constant.DirectoryEnum.attachment);
        String currentDateStr = DateUtil.date().toString("yyyy-MM-dd");
        File file = FileUtil.file(homeDir, currentDateStr, System.currentTimeMillis() + RandomUtil.randomString(6) + "." + suffix);
        FileUtil.mkParentDirs(file);
        FileUtil.touch(file);
        multipartFile.transferTo(file);
        importLog.setImportFilePath(file.getAbsolutePath());

        // 如果是JSON格式的文件就是自有文章数据导入
        if (isJsonFile) {
            importLog.setType(ImportTypeEnum.article_bnlp.getValue());
        }
        // 如果是ZIP格式的文件就是自有文章数据导入（ZIP包中的TXT文件）
        else if (isZipFile) {
            importLog.setType(ImportTypeEnum.article_bnlp.getValue());
        }
        // 如果是txt格式的文件就是从bfms拉取文章
        else {
            importLog.setType(ImportTypeEnum.article_bfms.getValue());
        }
        importLogService.save(importLog);
        importLog.setStatus(ImportStatusEnum.processing.getValue());
        importLogService.saveOrUpdate(importLog);

        if (isJsonFile) {
            ThreadUtil.execAsync(() -> {
                importBNLPArticle(file, importLog);
                distributeTasksEvenly(batchId);
            });
        } else if (isZipFile) {
            ThreadUtil.execAsync(() -> {
                importZipArticle(file, importLog);
                distributeTasksEvenly(batchId);
            });
        } else {
            ThreadUtil.execAsync(() -> {
                importBFMSArticle(file, importLog);
                distributeTasksEvenly(batchId);
            });
        }

    }

    public static <T extends BaseDTO> T initBaseDTO4Ids(T obj, Class<T> clz) {
        T baseDTO = BeanUtil.copyProperties(obj, clz);
        baseDTO.setPage(1);
        baseDTO.setLimit(10000);
        baseDTO.setStartDate(null);
        baseDTO.setEndDate(null);
        baseDTO.setForPageIds(true);
        return baseDTO;
    }

    private List<PageIdsVO> findAllPageIds(final ArticleListDTO articleListDTO) {
        final ArticleListDTO dto = initBaseDTO4Ids(articleListDTO, ArticleListDTO.class);
        final IPage<ArticleListVO> allPage = noteDao.findArticleList(new Query<ArticleListVO>().getPage(dto, "updateTime", false), dto);

        final Set<PageIdsVO> set = new LinkedHashSet<>();
        final List<ArticleListVO> records = allPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (ArticleListVO record : records) {
                final PageIdsVO vo = new PageIdsVO();
                vo.setNoteId(record.getNoteId());
                vo.setBatchId(dto.getBatchId());
                set.add(vo);
            }
        }
        return new ArrayList<>(set);
    }

    @Override
    public PageIdsListVO getArticleList(ArticleListDTO dto) {
        dto.setStep(NoteStepEnum.findStep(dto.getActiveStep()));

        final PageIdsListVO vo = new PageIdsListVO();
        vo.setPageIdsVOList(findAllPageIds(dto));

        IPage<ArticleListVO> articleList = noteDao.findArticleList(new Query<ArticleListVO>().getPage(dto, "updateTime", false), dto);
        // 把标注员和审核员的用户名查出来
        List<ArticleListVO> records = articleList.getRecords();
        if (CollUtil.isEmpty(records)) {
            vo.setListData(articleList);
            return vo;
        }

        Map<Long, String> userMap = new HashMap<>();
        for (ArticleListVO article : records) {
            String annotators = article.getAnnotators();
            if (StrUtil.isNotBlank(annotators)) {
                List<String> usernames = getUsernames(userMap, annotators);
                if (CollUtil.isNotEmpty(usernames)) {
                    article.setAnnotators(CollUtil.join(usernames, "; "));
                }
            }
            String auditors = article.getAuditor();
            if (StrUtil.isNotBlank(auditors)) {
                List<String> usernames = getUsernames(userMap, auditors);
                if (CollUtil.isNotEmpty(usernames)) {
                    article.setAuditor(CollUtil.join(usernames, "; "));
                }
            }
        }

        vo.setListData(articleList);
        return vo;
    }

    @Override
    public void batchSubmit(Long[] ids) {
        for (Long noteId : ids) {
            Note note = noteDao.selectById(noteId);
            if (note == null) {
                throw new RRException(StrUtil.format("操作失败！Note: {},不存在", noteId));
            }
            if (!NoteStepEnum.noting.getCode().equals(note.getStep())) {
                throw new RRException(StrUtil.format("操作失败！Note: {},状态不是标注中", noteId));
            }
            NoteTask noteTask = noteTaskDao.selectOne(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getNoteId, noteId).last(LIMIT_ONE));
            if (noteTask == null) {
                throw new RRException(StrUtil.format("操作失败！Note: {},没有相关NoteTask", noteId));
            }
            // 强制提交任务
            noteTaskService.submitAnnotatorTask(noteTask.getTaskId(), true);
        }

    }

    private List<String> getUsernames(Map<Long, String> userMap, String users) {
        String[] annotatorArray = users.split(",");
        List<String> usernames = new ArrayList<>();
        for (String annotator : annotatorArray) {
            Long userId = Long.parseLong(annotator);
            if (userMap.containsKey(userId)) {
                String username = userMap.get(userId);
                usernames.add(username);
            } else {
                SysUserEntity sysUser = sysUserService.getById(userId);
                userMap.put(userId, sysUser.getUsername());
                usernames.add(sysUser.getUsername());
            }
        }
        return usernames;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importBFMSArticle(File file, ImportLog importLog) {
        List<String> list = FileUtil.readLines(file, StandardCharsets.UTF_8).stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (list.isEmpty()) {
            throw new RRException("文本内容为空");
        }
        Date currentDate = new Date();
        // 生成需要导入的文章列表
        List<LoadDocument> loadDocuments = list.stream().map(it -> {
            LoadDocument loadDocument = new LoadDocument();
            loadDocument.setNo(it);
            loadDocument.setImportLogId(importLog.getId());
            loadDocument.setArticleId(null);
            loadDocument.setBatchId(importLog.getBatchId());
            loadDocument.setStatus(ImportResultEnum.wait.getValue());
            loadDocument.setProjectId(importLog.getProjectId());
            loadDocument.setCreateTime(currentDate);
            loadDocument.setUpdateTime(currentDate);
            return loadDocument;
        }).collect(Collectors.toList());
        loadDocumentService.saveBatch(loadDocuments);
        // 导入文章数据
        articleApiService.importArticles(loadDocuments);
        importLog.setFinishTime(new Date());
        importLog.setStatus(ImportStatusEnum.finish.getValue());
        importLogService.saveOrUpdate(importLog);
    }

    @SneakyThrows
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importBNLPArticle(File file, ImportLog importLog) {
        Set<String> allErrors = new LinkedHashSet<>();
        List<BNLPArticleDTO> list = new ArrayList<>();
        String jsonString = FileUtil.readUtf8String(file);
        try {
            list = JSON.parseArray(jsonString, BNLPArticleDTO.class);
            // 校验json文件内容是否合法
            validJSONObject(list, allErrors, importLog.getProjectId());
        } catch (JSONException e) {
            allErrors.add("json格式错误");
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            // 如果allErrors不为空证明导入有异常,不导入此次
            if (!allErrors.isEmpty()) {
                // 将错误日志落盘
                PreAnnotationService.syncErrorLog(importLog, allErrors);
            } else {
                // 校验通过，入库
                importBNLPArticle(list, importLog);
                // 更新 "导入记录" 的状态
                importLog.setStatus(ImportStatusEnum.finish.getValue());
                importLog.setFinishTime(new Date());
            }
            importLogService.saveOrUpdate(importLog);
        }
    }

    /**
     * 校验json内容是否符合规格
     *
     * @param list      读取json文件获得的JSONObject列表
     * @param projectId 项目id
     * @return
     */
    public void validJSONObject(List<BNLPArticleDTO> list, Set<String> allErrors, Long projectId) {
        Set<String> idSet = new LinkedHashSet<>();
        int index = 1;
        for (BNLPArticleDTO jsonObject : list) {
            // 校验id
            String id = jsonObject.getId();
            if (StrUtil.isBlank(id)) {
                allErrors.add(StrUtil.format("第 {} 条数据，id不能为空", index));
            } else {
                // 现在允许重复导入。校验id在数据库中是否存在，查询l_load_document的no=id and project_id=projectId and status=0
                /*int count = loadDocumentService.count(Wrappers.<LoadDocument>lambdaQuery().eq(LoadDocument::getNo, id).eq(LoadDocument::getProjectId, projectId).eq(LoadDocument::getStatus, ImportResultEnum.success.getValue()));
                if (count > 0) {
                    allErrors.add(StrUtil.format("第 {} 条数据，id: {} ,已在该项目中存在", index, id));
                }*/
                if (!idSet.add(id)) {
                    allErrors.add(StrUtil.format("第 {} 条数据，id: {} ,已在文件中存在", index, id));
                }
            }
            // 校验metadata
            LinkedHashMap<String, Object> metadata = jsonObject.getMetadata();
            if (metadata == null) {
                allErrors.add(StrUtil.format("第 {} 条数据, metadata 不能为null", index, id));
            }
            Set<String> keySet = metadata.keySet();
            for (String key : keySet) {
                Object o = metadata.get(key);
                if (!(o instanceof String) && !(o instanceof JSONArray)) {
                    allErrors.add(StrUtil.format("第 {} 条数据，metadata中的json对象的属性必须是字符串或字符串数组", index));
                } else if (o instanceof JSONArray) {
                    JSONArray array = (JSONArray) metadata.get(key);
                    for (Object item : array) {
                        if (!(item instanceof String)) {
                            allErrors.add(StrUtil.format("第 {} 条数据，metadata中的json对象的属性必须是字符串或字符串数组", index));
                            break;
                        }
                    }
                } else if (StrUtil.equalsAnyIgnoreCase(key, "title", "标题") && !(o instanceof String)) {
                    allErrors.add(StrUtil.format("第 {} 条数据，metadata中的 {} 的属性必须是字符串", index, key));
                }
            }
            // 校验content
            List<String> content = jsonObject.getContent();
            if (content.isEmpty()) {
                allErrors.add(StrUtil.format("第 {} 条数据，content不能为空", index));
                break;
            }
            // 校验 content的内容不能有 转义字符
            for (String text : content) {
                if (StrUtil.isNotBlank(text) && HtmlUtil.unescape(text).length() != text.length()) {
                    allErrors.add(StrUtil.format("第 {} 条数据，content内容不能包含HTML转义字符", index));
                    break;
                }

                // 校验content不能有 \r \n \t \f \v
                for (String escapeChar : ESCAPE_CHARS) {
                    if (StrUtil.contains(text, escapeChar)) {
                        allErrors.add(StrUtil.format("第 {} 条数据，content不能有 {} 字符", index, getCharName(escapeChar)));
                        break;
                    }
                }
            }

            index++;
        }
    }

    private String getCharName(String escapeChar) {
        switch (escapeChar) {
            case "\b":
                return "\\b (backspace)";
            case "\r":
                return "\\r (carriage return)";
            case "\n":
                return "\\n (newline)";
            case "\t":
                return "\\t (tab)";
            case "\f":
                return "\\f (form feed)";
            case "\u000B":
                return "\\v (vertical tab)";
            default:
                return escapeChar;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void importBNLPArticle(List<BNLPArticleDTO> list, ImportLog importLog) {
        final List<Document> documentList = new ArrayList<>();
        List<LoadDocument> loadDocumentList = new ArrayList<>();
        Map<String, String> articleNameMap = new HashMap<>();

        final Map<String, Document> documentMap = new HashMap<>();
        for (BNLPArticleDTO jsonObject : list) {
            String id = jsonObject.getId();
            LinkedHashMap<String, Object> metadata = jsonObject.getMetadata();
            List<String> content = jsonObject.getContent();

            Document document = new Document();
            // 设置document的基本信息
            String documentId = id + "BNLP" + importLog.getProjectId();
            document.setId(documentId);
            document.setSource("BNLP");
            document.setArticleId(id);

            // 设置document的info 摘要信息
            ArrayList<Document.InfoDTO> infoDTOS = new ArrayList<>();
            Set<String> metadataKeySet = metadata.keySet();
            int infoCount = 0;
            for (String key : metadataKeySet) {
                Object o = metadata.get(key);
                if (StrUtil.equalsAnyIgnoreCase(key, "title", "标题")) {
                    Document.TitleDTO titleDTO = new Document.TitleDTO();
                    titleDTO.setCls("Title");
                    titleDTO.setContent(metadata.get(key).toString());
                    titleDTO.setLevel(0);
                    document.setTitle(titleDTO);
                    articleNameMap.put(id, metadata.get(key).toString());
                }
                Document.InfoDTO infoDTO = new Document.InfoDTO();
                infoDTO.setCls("Section");
                Document.TitleDTO titleDTO = new Document.TitleDTO();
                titleDTO.setCls("Title");
                titleDTO.setContent(key);
                titleDTO.setLevel(0);
                infoDTO.setTitle(titleDTO);
                // metadata 的 value 是String 类型
                if (o instanceof String) {
                    Document.ItemsDTO itemsDTO = new Document.ItemsDTO();
                    itemsDTO.setCls("Paragraph");
                    itemsDTO.setContent(metadata.get(key).toString());
                    itemsDTO.setId("info-" + infoCount++);
                    infoDTO.setItems(CollUtil.newArrayList(itemsDTO));
                }
                // metadata 的 value 是 数组类型
                else if (o instanceof JSONArray) {
                    ArrayList<Document.ItemsDTO> itemsDTOS = new ArrayList<>();
                    for (String s : JSON.parseArray(JSON.toJSONString(metadata.get(key)), String.class)) {
                        Document.ItemsDTO itemsDTO = new Document.ItemsDTO();
                        itemsDTO.setCls("Paragraph");
                        itemsDTO.setContent(s);
                        itemsDTO.setId("info-" + infoCount++);
                        itemsDTOS.add(itemsDTO);
                    }
                    infoDTO.setItems(itemsDTOS);
                }
                infoDTOS.add(infoDTO);
            }
            Document.BodyDTO bodyDTO = new Document.BodyDTO();
            bodyDTO.setCls("Section");
            ArrayList<Document.ItemsDTO> itemsDTOS = new ArrayList<>();
            int bodyCount = 1;
            for (String s : content) {
                Document.ItemsDTO itemsDTO = new Document.ItemsDTO();
                itemsDTO.setCls("Paragraph");
                itemsDTO.setContent(s);
                itemsDTO.setId("body-" + bodyCount++);
                itemsDTOS.add(itemsDTO);
            }
            bodyDTO.setItems(itemsDTOS);

            document.setInfo(infoDTOS);
            document.setBody(CollUtil.newArrayList(bodyDTO));
            // key是body的md5
            document.setKey(SecureUtil.md5(JSON.toJSONString(CollUtil.newArrayList(bodyDTO))));
            // 自有数据导入的版本默认为1
            document.setVersion(1);
            document.setLanguage("zh");
            document.setCls("Article");

            documentMap.put(document.getId(), document);
            documentList.add(document);

            Date currentDate = new Date();

            // 生成LoadDocument
            LoadDocument loadDocument = new LoadDocument();
            loadDocument.setNo(id);
            loadDocument.setImportLogId(importLog.getId());
            loadDocument.setArticleId(id);
            loadDocument.setBatchId(importLog.getBatchId());
            loadDocument.setStatus(ImportResultEnum.success.getValue());
            loadDocument.setProjectId(importLog.getProjectId());
            loadDocument.setDocumentId(documentId);
            loadDocument.setCreateTime(currentDate);
            loadDocument.setUpdateTime(currentDate);
            loadDocumentList.add(loadDocument);
        }
        // 批量插入数据库
        loadDocumentService.saveBatch(loadDocumentList);
        documentRepository.saveAll(documentList);
        // note数据入库
        List<Note> noteList = new ArrayList<>();

        for (LoadDocument loadDocument : loadDocumentList) {
            Note note = new Note();
            note.setProjectId(loadDocument.getProjectId());
            note.setBatchId(loadDocument.getBatchId());

            final String documentId = loadDocument.getDocumentId();
            note.setDocumentId(documentId);
            note.setArticleId(loadDocument.getArticleId());
            if (articleNameMap.containsKey(loadDocument.getArticleId())) {
                note.setArticleName(articleNameMap.get(loadDocument.getArticleId()));
            }
            note.setStep(NoteStepEnum.unmarked.getCode());
            note.setCreateTime(new Date());
            note.setUpdateTime(new Date());
            note.setInvalid(NoteInvalidEnum.normal.getCode());

            ArticleServiceImpl.statWordsCount(note, documentMap.get(documentId));
            noteList.add(note);
        }
        noteService.saveBatch(noteList);

        // 更新批次文章的条数
        int countNote = noteService.count(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, importLog.getBatchId()));
        Batch batch = this.getById(importLog.getBatchId());
        batch.setTotalArticle(countNote);
        this.saveOrUpdate(batch);
    }

    /**
     * 导入ZIP包中的TXT文件
     * ZIP包中每个TXT文件代表一篇文章，文件名为article_id，第一行为title，其余为正文内容
     *
     * @param file      ZIP文件
     * @param importLog 导入日志
     */
    @SneakyThrows
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importZipArticle(File file, ImportLog importLog) {
        Set<String> allErrors = new LinkedHashSet<>();
        List<BNLPArticleDTO> articleList = new ArrayList<>();

        try {
            // 创建临时解压目录
            File tempDir = FileUtil.file(file.getParent(), "temp_" + System.currentTimeMillis());
            FileUtil.mkdir(tempDir);

            try {
                // 解压ZIP文件
                ZipUtil.unzip(file, tempDir, StandardCharsets.UTF_8);

                // 递归遍历解压后的所有TXT文件
                List<File> txtFiles = FileUtil.loopFiles(tempDir, pathname -> pathname.getName().toLowerCase().endsWith(".txt"));
                if (CollUtil.isEmpty(txtFiles)) {
                    allErrors.add("ZIP包中没有找到TXT文件");
                } else {
                    for (File txtFile : txtFiles) {
                        try {
                            // 文件名作为article_id（去掉.txt扩展名）
                            String articleId = FileUtil.getPrefix(txtFile.getName());

                            // 读取文件内容
                            List<String> lines = FileUtil.readLines(txtFile, StandardCharsets.UTF_8);
                            if (CollUtil.isEmpty(lines)) {
                                allErrors.add(StrUtil.format("文件 {} 内容为空", txtFile.getName()));
                                continue;
                            }

                            // 第一行作为title
                            String title = lines.get(0).trim();
                            if (StrUtil.isBlank(title)) {
                                allErrors.add(StrUtil.format("文件 {} 的第一行（标题）不能为空", txtFile.getName()));
                                continue;
                            }

                            // 其余行作为正文内容
                            List<String> content = new ArrayList<>();
                            for (int i = 1; i < lines.size(); i++) {
                                String line = lines.get(i).trim();
                                if (StrUtil.isNotBlank(line)) {
                                    content.add(line);
                                }
                            }

                            if (content.isEmpty()) {
                                allErrors.add(StrUtil.format("文件 {} 的正文内容不能为空", txtFile.getName()));
                                continue;
                            }

                            // 构建BNLPArticleDTO
                            BNLPArticleDTO articleDTO = new BNLPArticleDTO();
                            articleDTO.setId(articleId);

                            // 设置metadata，包含title
                            LinkedHashMap<String, Object> metadata = new LinkedHashMap<>();
                            metadata.put("title", title);
                            articleDTO.setMetadata(metadata);

                            // 设置content
                            articleDTO.setContent(content);

                            articleList.add(articleDTO);

                        } catch (Exception e) {
                            allErrors.add(StrUtil.format("处理文件 {} 时发生错误: {}", txtFile.getName(), e.getMessage()));
                        }
                    }
                }
            } finally {
                // 清理临时目录
                FileUtil.del(tempDir);
            }

            // 校验转换后的数据
            if (CollUtil.isNotEmpty(articleList)) {
                validJSONObject(articleList, allErrors, importLog.getProjectId());
            }

        } catch (Exception e) {
            allErrors.add("解压ZIP文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 如果allErrors不为空证明导入有异常,不导入此次
            if (CollUtil.isNotEmpty(allErrors)) {
                // 将错误日志落盘
                PreAnnotationService.syncErrorLog(importLog, allErrors);
            } else if (CollUtil.isNotEmpty(articleList)) {
                // 校验通过，入库
                importBNLPArticle(articleList, importLog);
                // 更新 "导入记录" 的状态
                importLog.setStatus(ImportStatusEnum.finish.getValue());
                importLog.setFinishTime(new Date());
            } else {
                allErrors.add("ZIP包中没有有效的文章数据");
                PreAnnotationService.syncErrorLog(importLog, allErrors);
            }
            importLogService.saveOrUpdate(importLog);
        }
    }


    @Override
    public void validateBatchName(String batchName, Long projectId, Long batchId) {
        if (StringUtils.isBlank(batchName)) {
            throw new RRException("名称不能为空");
        }
        batchName = batchName.trim();
        int count = this.count(Wrappers.<Batch>lambdaQuery().eq(Batch::getName, batchName).ne(Batch::getBatchId, batchId).eq(Batch::getProjectId, projectId));
        if (count > 0) {
            throw new RRException("名称重复");
        }
    }

    /**
     * 将批次下的文章均衡分配给标注员
     *
     * @param batchId 批次ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributeTasksEvenly(Long batchId) {

        Batch batch = this.getById(batchId);

        if (batch == null) {
            throw new RRException("批次不存在");
        }

        // 均衡分配任务模式才执行
        if (batch.getMode() != 1) {
            return;
        }

        // 查询所有未标注的文章
        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .eq(Note::getStep, NoteStepEnum.unmarked.getCode()));

        if (CollUtil.isEmpty(notes)) {
            throw new RRException("该批次下没有未标注的文章");
        }

        // 获取项目信息
        Project project = projectService.getById(batch.getProjectId());
        if (project == null) {
            throw new RRException("项目不存在");
        }

        // 获取项目的标注员列表
        List<ProjectUser> annotators = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, project.getProjectId())
                .eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));

        if (CollUtil.isEmpty(annotators)) {
            throw new RRException("该项目下没有标注员");
        }

        // 分配任务
        distributeNotes(project.getMarkRounds(), notes, annotators);
    }

    /**
     * 平均分配文章给标注员

     * 标注员协作关系跟踪：
     * 建立标注员之间的协作关系图，记录每对标注员共同标注过多少篇文章
     * 使用annotatorToNotesMap追踪每个标注员已标注的文章
     * 使用annotatorPairsMap记录标注员之间的配对频率

     * 最佳标注员组合选择算法：
     * selectBestAnnotatorCombination方法，为每篇文章智能选择最佳标注员组合
     * 首先选择任务量最少的标注员作为起点
     * 然后依次选择与已选标注员重合度最低且任务量较少的标注员加入组合

     * 动态更新标注情况：
     * 每分配一个任务后，实时更新标注员的任务数、已标注文章和配对情况
     * 使算法能够自适应地调整标注员组合，避免重复组合

     * 兼顾负载均衡和随机性：
     * 在保证标注员任务量均衡的前提下，优先选择重合度最低的标注员组合
     * 使用"选择重合度最低的前N个候选标注员，再从中选出任务量最少的"策略
     * 这种方法不仅能够确保标注任务在标注员间均衡分配，还能最大程度地避免标注员总是以相同的组合标注文章，有效实现了随机双盲标注效果。
     *
     * @param markRounds 标注轮数
     * @param notes      待分配的文章列表
     * @param annotators 标注员列表
     */
    private void distributeNotes(Integer markRounds, List<Note> notes, List<ProjectUser> annotators) {
        if (markRounds == null || markRounds < 1) {
            markRounds = 1;
        }

        Date now = new Date();

        // 存储每个标注员标注过的文章和相应的标注员组合
        Map<Long, Set<Long>> annotatorToNotesMap = new HashMap<>(); // 标注员ID -> 已标注的文章ID集合
        Map<Long, Map<Long, Integer>> annotatorPairsMap = new HashMap<>(); // 标注员ID -> (其他标注员ID -> 共同标注的文章数)

        // 初始化标注员的任务数和标注文章集合
        Map<Long, Integer> taskCountMap = new HashMap<>();
        for (ProjectUser annotator : annotators) {
            taskCountMap.put(annotator.getUserId(), 0);
            annotatorToNotesMap.put(annotator.getUserId(), new HashSet<>());
            annotatorPairsMap.put(annotator.getUserId(), new HashMap<>());
        }

        // 查询当前批次下已存在的任务，统计现有的标注情况
        List<NoteTask> existingTasks = noteTaskService.list(Wrappers.<NoteTask>lambdaQuery()
                .select(NoteTask::getAnnotator, NoteTask::getNoteId)
                .eq(NoteTask::getBatchId, notes.isEmpty() ? 0L : notes.get(0).getBatchId()));

        // 初始化标注员任务数和已标注文章集合
        for (NoteTask task : existingTasks) {
            Long annotatorId = task.getAnnotator();
            Long noteId = task.getNoteId();

            // 更新任务数量
            taskCountMap.merge(annotatorId, 1, Integer::sum);

            // 更新标注员已标注的文章集合
            if (annotatorToNotesMap.containsKey(annotatorId)) {
                annotatorToNotesMap.get(annotatorId).add(noteId);
            }
        }

        // 计算标注员之间的共同标注文章数
        for (Long annotatorId : annotatorToNotesMap.keySet()) {
            Set<Long> annotatorNotes = annotatorToNotesMap.get(annotatorId);

            for (Long otherAnnotatorId : annotatorToNotesMap.keySet()) {
                if (!annotatorId.equals(otherAnnotatorId)) {
                    Set<Long> otherAnnotatorNotes = annotatorToNotesMap.get(otherAnnotatorId);

                    // 计算两个标注员共同标注的文章数
                    Set<Long> intersection = new HashSet<>(annotatorNotes);
                    intersection.retainAll(otherAnnotatorNotes);
                    int overlapCount = intersection.size();

                    // 更新配对信息
                    annotatorPairsMap.get(annotatorId).put(otherAnnotatorId, overlapCount);
                }
            }
        }

        // 为每篇文章创建markRounds个任务
        for (Note note : notes) {
            // 为这篇文章选择最佳的标注员组合
            List<ProjectUser> selectedAnnotators = selectBestAnnotatorCombination(annotators, markRounds, taskCountMap, annotatorPairsMap);

            for (ProjectUser annotator : selectedAnnotators) {
                // 创建新任务
                NoteTask task = new NoteTask();
                task.setProjectId(note.getProjectId());
                task.setBatchId(note.getBatchId());
                task.setNoteId(note.getNoteId());
                task.setStep(NoteStepEnum.noting.getCode());
                task.setAnnotator(annotator.getUserId());
                task.setCreateTime(now);
                task.setUpdateTime(now);
                noteTaskService.save(task);

                // 更新标注员任务数
                taskCountMap.merge(annotator.getUserId(), 1, Integer::sum);

                // 更新标注员已标注文章
                annotatorToNotesMap.get(annotator.getUserId()).add(note.getNoteId());

                // 更新标注员配对信息
                Long annotatorId = annotator.getUserId();
                for (ProjectUser selected : selectedAnnotators) {
                    Long otherAnnotatorId = selected.getUserId();
                    if (!annotatorId.equals(otherAnnotatorId)) {
                        annotatorPairsMap.get(annotatorId).merge(otherAnnotatorId, 1, Integer::sum);
                        annotatorPairsMap.get(otherAnnotatorId).merge(annotatorId, 1, Integer::sum);
                    }
                }
            }

            // 更新文章的拉取次数和状态
            note.setPullCount(markRounds);
            note.setStep(NoteStepEnum.noting.getCode());
            noteService.updateById(note);
        }
    }

    /**
     * 为一篇文章选择最佳的标注员组合
     *
     * @param annotators 所有标注员列表
     * @param markRounds 需要选择的标注员数量
     * @param taskCountMap 每个标注员的任务数
     * @param annotatorPairsMap 标注员之间的配对频率
     * @return 选定的标注员列表
     */
    private List<ProjectUser> selectBestAnnotatorCombination(
            List<ProjectUser> annotators,
            int markRounds,
            Map<Long, Integer> taskCountMap,
            Map<Long, Map<Long, Integer>> annotatorPairsMap) {

        // 如果标注员数量小于或等于需要的轮数，直接返回所有标注员
        if (annotators.size() <= markRounds) {
            return new ArrayList<>(annotators);
        }

        List<ProjectUser> result = new ArrayList<>(markRounds);
        Set<Long> selectedIds = new HashSet<>();

        // 第一步：选择任务量最少的标注员作为第一个
        ProjectUser firstAnnotator = annotators.stream()
                .min(Comparator.comparingInt(a -> taskCountMap.getOrDefault(a.getUserId(), 0)))
                .orElseThrow(() -> new RRException("没有可用的标注员"));

        result.add(firstAnnotator);
        selectedIds.add(firstAnnotator.getUserId());

        // 第二步：依次选择与已选标注员重合度最低且任务量较少的标注员
        while (result.size() < markRounds) {
            // 找出候选标注员列表
            List<ProjectUser> candidates = annotators.stream()
                    .filter(a -> !selectedIds.contains(a.getUserId()))
                    .collect(Collectors.toList());

            if (candidates.isEmpty()) {
                break;
            }

            // 计算每个候选标注员与已选标注员的总重合度分数
            Map<Long, Integer> overlapScores = new HashMap<>();
            for (ProjectUser candidate : candidates) {
                int totalOverlap = 0;
                for (Long selectedId : selectedIds) {
                    totalOverlap += annotatorPairsMap.get(candidate.getUserId()).getOrDefault(selectedId, 0);
                }
                overlapScores.put(candidate.getUserId(), totalOverlap);
            }

            // 从所有候选标注员中先选出重合度最低的前3名作为"候选池",避免贪心算法的局限性：如果每次只选择一个最优解（重合度最低的那一个），可能导致整体分配不够优化。通过建立候选池，可以有更多选择空间。
            List<ProjectUser> lowOverlapCandidates = candidates.stream()
                    .sorted(Comparator.comparingInt(a -> overlapScores.get(a.getUserId())))
                    .limit(Math.min(3, candidates.size()))
                    .collect(Collectors.toList());

            // 在重合度最低的标注员中，选择任务量最少的
            ProjectUser nextAnnotator = lowOverlapCandidates.stream()
                    .min(Comparator.comparingInt(a -> taskCountMap.getOrDefault(a.getUserId(), 0)))
                    .orElse(lowOverlapCandidates.get(0));

            result.add(nextAnnotator);
            selectedIds.add(nextAnnotator.getUserId());
        }

        return result;
    }

    @Override
    public List<LoadDocument> loadArticleResult(Long importLogId, Long userId) {
        List<LoadDocument> list = loadDocumentService.list(Wrappers.<LoadDocument>lambdaQuery().eq(LoadDocument::getImportLogId, importLogId));


        long waitCount = list.stream().filter(it -> it.getStatus() == ImportResultEnum.wait.getValue()).count();
        if (waitCount > 0) {
            reloadArticle(importLogId, userId);
        }
        return list;
    }

    @Override
    public void reloadArticle(Long importLogId, Long userId) {
        List<LoadDocument> fails = loadDocumentService.list(Wrappers.<LoadDocument>lambdaQuery().eq(LoadDocument::getImportLogId, importLogId).eq(LoadDocument::getStatus, ImportResultEnum.fail.getValue()));

        List<LoadDocument> waits = fails.stream().peek(it -> {
            it.setMsg(null);
            it.setStatus(ImportResultEnum.wait.getValue());
        }).collect(Collectors.toList());

        loadDocumentService.update(Wrappers.<LoadDocument>lambdaUpdate().eq(LoadDocument::getImportLogId, importLogId).eq(LoadDocument::getStatus, ImportResultEnum.fail.getValue()).set(LoadDocument::getMsg, null).set(LoadDocument::getStatus, ImportResultEnum.wait.getValue()));
        ImportLog importLog = importLogService.getById(importLogId);
        importLog.setStatus(ImportStatusEnum.processing.getValue());
        importLogService.saveOrUpdate(importLog);
        CompletableFuture.runAsync(() -> {
            articleApiService.importArticles(waits);
        }).thenRun(() -> {
            importLog.setFinishTime(new Date());
            importLog.setStatus(ImportStatusEnum.finish.getValue());
            importLogService.saveOrUpdate(importLog);
        });
    }

    @Override
    public void exportArticleResult(Long importLogId, Long userId, HttpServletResponse response) throws IOException {
        List<LoadDocument> result = loadArticleResult(importLogId, userId);

        File dir = Constant.getHomeDir(Constant.DirectoryEnum.temp);
        File file = new File(dir, UUID.randomUUID() + ".csv");
        try (CsvWriter writer = CsvUtil.getWriter(file, CharsetUtil.CHARSET_UTF_8)) {
            List<String[]> collect = new ArrayList<>();
            collect.add(new String[]{"编号", "状态", "备注"});
            collect.addAll(result.stream().map(it -> {
                String status = null;
                if (it.getStatus() != null) {
                    if (it.getStatus() == ImportResultEnum.success.getValue()) {
                        status = "成功";
                    } else if (it.getStatus() == ImportResultEnum.fail.getValue()) {
                        status = "失败";
                    } else if (it.getStatus() == ImportResultEnum.repeat.getValue()) {
                        status = "重复";
                    } else if (it.getStatus() == ImportResultEnum.wait.getValue()) {
                        status = "待导入";
                    }
                }
                return new String[]{it.getNo(), status, it.getMsg()};
            }).collect(Collectors.toList()));
            writer.write(collect);
            writer.flush();
            writer.close();
            String exportName = "文章导入情况.csv";
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(exportName, "UTF-8"));
            ServletOutputStream out = response.getOutputStream();
            out.write(FileUtils.readFileToByteArray(file));
            IoUtil.close(out);
        }

    }

    @Override
    public PageUtils loadArticleList(LoadArticleDTO dto, Long userId) {
        if (dto == null || dto.getBatchId() == null) {
            throw new RRException("请选择批次");
        }

        IPage<LoadDocument> page = loadDocumentService.page(new Query<LoadDocument>().getPage(dto), Wrappers.<LoadDocument>lambdaQuery().eq(LoadDocument::getBatchId, dto.getBatchId()).like(dto.getNo() != null, LoadDocument::getNo, dto.getNo()).like(dto.getArticleId() != null, LoadDocument::getArticleId, dto.getArticleId()).eq(dto.getImportStatus() != null, LoadDocument::getStatus, dto.getImportStatus()));

        page.setRecords(page.getRecords().stream().peek(it -> {
            if (it.getArticleId() == null || it.getStatus() != ImportResultEnum.success.getValue()) {
                return;
            }
            Note note = noteService.getOne(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, it.getBatchId()).eq(Note::getArticleId, it.getArticleId()).last(LIMIT_ONE));
            it.setNote(note);
        }).collect(Collectors.toList()));

        return new PageUtils(page);
    }

    @Override
    public void changeNoteArticleStatus(Long batchId, String articleId, Long userId) {
        //    verificationAuthority(batchId, userId);

        Note note = noteService.getOne(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, batchId).eq(Note::getArticleId, articleId).last(LIMIT_ONE));
        if (note == null) {
            throw new RRException("文章不存在");
        }
        if (NoteInvalidEnum.invalid.getCode().equals(note.getInvalid())) {
            note.setInvalid(NoteInvalidEnum.normal.getCode());
        } else {
            note.setInvalid(NoteInvalidEnum.invalid.getCode());
        }
        noteService.saveOrUpdate(note);
    }

    /**
     * 重置此篇文章下的所有标注并将文章标注状态重置
     *
     * @param noteId 标注文章内容的唯一ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetNoteArticle(Long noteId) {
        // 删除关系标注
        relationshipRepository.deleteAllByNoteId(noteId);
        // 删除实体标注
        entityRepository.deleteAllByNoteId(noteId);
        // 删除操作记录
        opertionService.remove(Wrappers.<Operation>lambdaQuery().eq(Operation::getTaskId, noteId));
        // 删除讨论区记录
        commentService.deleteCommentsByNoteId(noteId);

        // 重置note信息
        noteService.update(Wrappers.<Note>lambdaUpdate().eq(Note::getNoteId, noteId).set(Note::getStep, NoteStepEnum.unmarked.getCode()).set(Note::getInvalid, NoteInvalidEnum.normal.getCode()));
    }

    @Override
    public PageUtils queryPage(BatchDTO dto) {
        if (dto == null || dto.getProjectId() == null) {
            throw new RRException("请选择项目");
        }
        final Long projectId = dto.getProjectId();
        IPage<Batch> page = this.page(new Query<Batch>().getPage(dto), Wrappers.<Batch>lambdaQuery().eq(Batch::getProjectId, projectId)
                .like(StrUtil.isNotBlank(dto.getName()), Batch::getName, dto.getName()).like(StrUtil.isNotBlank(dto.getMaterialSource()), Batch::getMaterialSource, dto.getMaterialSource()).eq(dto.getStatus() != null, Batch::getStatus, dto.getStatus()).ge(dto.getStartDate() != null, Batch::getCreateTime, dto.getStartDate()).le(dto.getEndDate() != null, Batch::getCreateTime, DateUtil.format(dto.getEndDate(), "yyyy-MM-dd 23:59:59")));
        final List<Batch> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            final Project project = projectService.getById(projectId);
            if (project == null) {
                throw new RRException("该项目不存在");
            }
            for (Batch record : records) {
                record.setProjectName(project.getName());
            }
        }
        return new PageUtils(page);
    }

    @Override
    public List<Batch> findAllByProjectId(Long projectId) {
        LambdaQueryWrapper<Batch> batchLambdaQueryWrapper = Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, projectId)
                .eq(Batch::getStatus, StatusEnum.enable.getValue());
        return this.list(batchLambdaQueryWrapper);
    }

    private void verificationAuthority(Long batchId, Long userId) {
        if (batchId == null) {
            throw new RRException("该批次不存在");
        }
        Batch batch = this.getById(batchId);
        if (batch == null) {
            throw new RRException("该批次不存在");
        }
        Project project = projectService.getById(batch.getProjectId());
        if (project == null) {
            throw new RRException("该项目不存在");
        }
        if (!project.getCreatorId().equals(userId)) {
            throw new RRException("没有权限进行此操作");
        }
    }
}
