package org.biosino.nlp.modules.project.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.modules.project.service.StatisticsService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 统计的定时任务
 *
 * <AUTHOR>
 * @date 2021-01-05 15:45
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StatisticsTask {

    private final StatisticsService statisticsService;

    /**
     * 每隔6个小时执行一次
     */
    @Scheduled(cron = "0 0 */6 * * ?")
    public void execute() {
        statisticsService.task();
    }

    /**
     * 每天4点触发
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void annotatedStatisticsJob() {
        statisticsService.annotatedStatisticsJob();
    }

}
