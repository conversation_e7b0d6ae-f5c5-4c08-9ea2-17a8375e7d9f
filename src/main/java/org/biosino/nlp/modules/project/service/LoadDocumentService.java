package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.project.entity.LoadDocument;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-15 09:56
 */
public interface LoadDocumentService extends IService<LoadDocument> {
    List<LoadDocument> findAllByImportIdIn(Collection<Long> importLogIds);

    void deletedByArticleId(Long batchId, String articleId);

    void deletedByBatchId(Long batchId);
}
