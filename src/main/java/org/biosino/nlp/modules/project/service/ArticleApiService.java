package org.biosino.nlp.modules.project.service;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.nlp.common.enums.ImportResultEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.HttpUtils;
import org.biosino.nlp.modules.api.service.impl.ArticleServiceImpl;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.project.dao.BatchDao;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.LoadDocument;
import org.biosino.nlp.modules.project.entity.Project;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-09 17:17
 */
@Service
public class ArticleApiService {

    @Autowired
    private NoteService noteService;
    @Lazy
    @Autowired
    private BatchDao batchDao;
    @Autowired
    private LoadDocumentService loadDocumentService;
    @Autowired
    private MongoOperations mongoOperations;
    @Autowired
    private PreAnnotationService preAnnotationService;
    @Autowired
    private ProjectDao projectDao;

    /**
     * 查询 Id
     */
    public String getArticleConvertId(String no) {
        try {
            String url = Constant.bfmsApi + "/getArticleId?articleId=" + URLUtil.encodeQuery(no);
            return HttpUtils.httpGet(url);
        } catch (Exception e) {
            return null;
        }
    }

    public Document getArticle(String articleId) {
        try {
            String url = Constant.bfmsApi + "/getArticleById?articleId=" + URLUtil.encodeQuery(articleId);
            String resp = HttpUtils.httpGet(url);
            return JSONObject.parseObject(resp, Document.class);
        } catch (Exception e) {
            return null;
        }
    }

    public Document getArticleByVersion(String articleId, Integer version) {
        try {
            String url = Constant.bfmsApi + "/getArticleByIdAndVersion?articleId=" + URLUtil.encodeQuery(articleId) + "&version=" + version;
            String resp = HttpUtils.httpGet(url);
            return JSONObject.parseObject(resp, Document.class);
        } catch (Exception e) {
            return null;
        }
    }

    public String getArticleMd5(String articleId) {
        try {
            String url = Constant.bfmsApi + "/getArticleMd5?articleId=" + URLUtil.encodeQuery(articleId);
            return HttpUtils.httpGet(url);
        } catch (Exception e) {
            return null;
        }
    }

    public String getArticleMd5ByVersion(String articleId, Integer version) {
        try {
            String url = Constant.bfmsApi + "/getArticleMd5ByVersion?articleId=" + URLUtil.encodeQuery(articleId) + "&version=" + version;
            return HttpUtils.httpGet(url);
        } catch (Exception e) {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void importArticle(LoadDocument loadDocument) {
        try {
            String articleId = loadDocument.getNo();

            // 解析版本号
            Integer version = null;
            if (articleId != null && articleId.contains("#")) {
                try {
                    int index = articleId.indexOf("#");
                    version = Integer.parseInt(articleId.substring(index + 1).trim());
                    articleId = articleId.substring(0, index);
                } catch (Exception e) {
                    loadDocument.setStatus(ImportResultEnum.fail.getValue());
                    loadDocument.setMsg("文章的版本输入格式解析出错，正确格式：{文章ID}#{版本号}");
                    return;
                }
            }
            // 转内部 id
            articleId = getArticleConvertId(articleId);

            if (articleId == null) {
                loadDocument.setStatus(ImportResultEnum.fail.getValue());
                loadDocument.setMsg("未查询到该文献");
                return;
            }

            loadDocument.setArticleId(articleId);

            String key;
            if (version != null) {
                key = getArticleMd5ByVersion(articleId, version);
            } else {
                key = getArticleMd5(articleId);
            }
            if (key == null) {
                loadDocument.setStatus(ImportResultEnum.fail.getValue());
                loadDocument.setMsg("未查询到该文献");
                return;
            }

            Note note = noteService.getOne(Wrappers.<Note>lambdaQuery()
                    .eq(Note::getProjectId, loadDocument.getProjectId())
                    .eq(Note::getBatchId, loadDocument.getBatchId())
                    .eq(Note::getArticleId, articleId).orderByDesc(Note::getCreateTime));

            if (note != null) {
                loadDocument.setDocumentId(note.getDocumentId());
                Batch batch = batchDao.selectById(note.getBatchId());
                loadDocument.setStatus(ImportResultEnum.repeat.getValue());
                loadDocument.setMsg("该文献已经在" + batch.getName() + "批次中存在");
                return;
            }

            // 如果指定有特定版本，则只拉特定版本的文章
            if (version != null) {
                Query query = Query.query(Criteria.where("article_id").is(articleId)
                        .and("version").is(version)).limit(1);
                query.fields().include("id").include("key");
                Document document = mongoOperations.findOne(query, Document.class);
                if (document != null && key.equals(document.getKey())) {
                    loadDocument.setStatus(ImportResultEnum.success.getValue());
                    loadDocument.setDocumentId(document.getId());
                    return;
                }
                // 本地没有指定版本的文章，到BFMS拉取
                document = getArticleByVersion(loadDocument.getArticleId(), version);
                if (document == null) {
                    loadDocument.setStatus(ImportResultEnum.fail.getValue());
                    loadDocument.setMsg("未查询到该文献");
                    return;
                }
                String documentId = String.join("",
                        document.getArticleId(),
                        document.getSource().toUpperCase(),
                        document.getVersion().toString());
                document.setId(documentId);
                mongoOperations.save(document);

                loadDocument.setDocumentId(documentId);
                loadDocument.setStatus(ImportResultEnum.success.getValue());
            }

            // 没有指定特定版本
            Query query = Query.query(Criteria.where("article_id").is(articleId))
                    .with(Sort.by(Sort.Direction.DESC, "version")).limit(1);
            query.fields().include("id").include("key");

            Document document = mongoOperations.findOne(query, Document.class);

            if (document != null && key.equals(document.getKey())) {
                loadDocument.setStatus(ImportResultEnum.success.getValue());
                loadDocument.setDocumentId(document.getId());
                return;
            }

            document = getArticle(loadDocument.getArticleId());
            if (document == null) {
                loadDocument.setStatus(ImportResultEnum.fail.getValue());
                loadDocument.setMsg("未查询到该文献");
                return;
            }
            String documentId = String.join("",
                    document.getArticleId(),
                    document.getSource().toUpperCase(),
                    document.getVersion().toString());
            document.setId(documentId);
            mongoOperations.save(document);

            loadDocument.setDocumentId(documentId);
            loadDocument.setStatus(ImportResultEnum.success.getValue());
        } catch (Exception e) {
            e.printStackTrace();
            loadDocument.setStatus(ImportResultEnum.fail.getValue());
            loadDocument.setMsg("未知错误");
        } finally {
            Date currentDate = new Date();
            loadDocument.setUpdateTime(currentDate);
            loadDocumentService.saveOrUpdate(loadDocument);

            if (loadDocument.getStatus() == ImportResultEnum.success.getValue()
                    && loadDocument.getDocumentId() != null) {

                Document document = mongoOperations.findById(loadDocument.getDocumentId(), Document.class);
                if (document != null) {
                    Note note = new Note();
                    note.setProjectId(loadDocument.getProjectId());
                    note.setBatchId(loadDocument.getBatchId());
                    note.setDocumentId(loadDocument.getDocumentId());
                    note.setArticleName(document.getTitle().getContent());
                    note.setArticleId(document.getArticleId());
                    note.setStep(NoteStepEnum.unmarked.getCode());
                    note.setInvalid(NoteInvalidEnum.normal.getCode());
                    note.setCreateTime(currentDate);
                    note.setUpdateTime(currentDate);

                    ArticleServiceImpl.statWordsCount(note, document);
                    noteService.save(note);

                    int countNote = noteService.count(Wrappers.<Note>lambdaQuery()
                            .eq(Note::getBatchId, loadDocument.getBatchId()));
                    Batch batch = batchDao.selectById(loadDocument.getBatchId());
                    batch.setTotalArticle(countNote);
                    batchDao.updateById(batch);
                }
            }
        }
    }

    public void importArticles(List<LoadDocument> loadDocuments) {

        if (loadDocuments.isEmpty()) {
            return;
        }
        Long batchId = loadDocuments.get(0).getBatchId();
        Batch batch = batchDao.selectById(batchId);

        // 导入文章
        loadDocuments.forEach(this::importArticle);

        // 导入预标注信息
        Project project = projectDao.selectById(batch.getProjectId());
        if (project.getPreSources() != null) {
            Arrays.stream(project.getPreSources().split(","))
                    .map(PreSourceEnum::valueOf)
                    .parallel()
                    .forEach(source -> preAnnotationService.savePreAnnotation(batch, source));
        }
        // 更新批次的条数
        int countNote = noteService.count(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, batchId));
        batch.setTotalArticle(countNote);
        batchDao.updateById(batch);
    }
}
