package org.biosino.nlp.modules.project.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * 标注员标注一致性统计
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@Document(collection = "m_anno_kappa_alpha")
public class AnnoKappaAlpha implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @Indexed
    private String createDate;

    @Indexed
    private Long projectId;
    @Indexed
    private Long batchId;

    private List<Long> userIds;
    // Fleiss' Kappa (κ)
    private Double entityKappa = 0D;
    private Double attrKappa = 0D;

    // <PERSON><PERSON><PERSON><PERSON><PERSON>'s Alpha（α）
    private Double entityAlpha = 0D;
    private Double attrAlpha = 0D;
}
