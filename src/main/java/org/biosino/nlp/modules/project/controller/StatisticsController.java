package org.biosino.nlp.modules.project.controller;

import cn.hutool.core.thread.ThreadUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.project.entity.Statistics;
import org.biosino.nlp.modules.project.service.StatisticsService;
import org.biosino.nlp.modules.project.vo.*;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-5-24
 */
@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
public class StatisticsController extends AbstractController {

    private final StatisticsService statisticsService;

    /**
     * 查询当前用户参与的项目
     */
    @RequestMapping("/getProjects")
    public R getProjects(Long roleId) {
        List<ProjectVO> projects = statisticsService.getProjects(roleId, getUserId());
        return R.success(projects);
    }

    /**
     * 项目完成情况简要统计
     */
    @RequestMapping("/overview")
    public R overview(Long projectId, Long roleId) {
        StatisticsVO overview = statisticsService.overview(projectId, roleId, getUserId());
        return R.success(overview);
    }

    /**
     * 标注工作量排行榜统计
     */
    @RequestMapping("/annoStatistics")
    public R annotationStatistics(Long projectId) {
        List<AnnoStatisticsVO> list = statisticsService.annotationStatistics(projectId);
        return R.success(list);
    }

    /**
     * 标注员和审核员标注统计（前端表格）
     */
    @RequestMapping("/roleStatistics")
    public R roleStatistics(@RequestParam(required = false) Long projectId, @RequestParam(required = false) Long batchId, Long roleId, boolean refresh) {
        List<UserStatisticsVO> vo = statisticsService.roleStatistics(projectId, batchId, roleId, refresh);
        return R.success(vo);
    }

    /**
     * 标注员标注准确率图表
     */
    @RequestMapping("/accuracyChart")
    public R accuracyChart(@RequestParam(required = false) Long projectId, @RequestParam(required = false) Long batchId) {
        Map<String, Object> vo = statisticsService.accuracyChart(projectId, batchId);
        return R.success(vo);
    }

    /**
     * 标注/审核详情（导出详情）
     */
    @RequestMapping("/annoDetails")
    public R annoDetails(Long projectId, Long roleId, Long userId) {
        List<AnnoDetailsVO> vo = statisticsService.annoDetails(projectId, roleId, userId);
        return R.success(vo);
    }

    /**
     * 标注/审核工作进度统计
     */
    @RequestMapping("/workProgressStatistics")
    public R workProgressStatistics(Long projectId, Long roleId) {
        List<WorkProgressVO> list = statisticsService.workProgressStatistics(projectId, roleId);
        return R.success(list);
    }

    /**
     * 批次标注情况图表
     */
    @RequestMapping("/batchesStatistics")
    public R batchesStatistics(Long projectId) {
        List<BatchStatisticsVO> list = statisticsService.batchesStatistics(projectId);
        return R.success(list);
    }

    /**
     * 批次 正确率和打回率
     */
    @RequestMapping("/batchCorrectChart")
    public R batchCorrectChart(Long projectId) {
        List<BatchVO> list = statisticsService.batchCorrectChart(projectId);
        return R.success(list);
    }

    /**
     * 项目标注进度
     */
    @RequestMapping("/dateStatistics")
    public R dateStatistics(Long projectId) {
        List<Statistics> list = statisticsService.dateStatistics(projectId);
        return R.success(list);
    }

    /**
     * 计算数据标注一致性
     */
    @RequestMapping("/calculationConsistency")
    public R calculationConsistency(Long projectId, @RequestParam(required = false) Long batchId, boolean refresh) {
        List<KappaAlphaVO> list = statisticsService.calculationConsistency(projectId, batchId, refresh);
        return R.success(list);
    }

    /**
     * 导出实体标注一致性
     */
    @RequestMapping("/exportEntityConsistency")
    public void exportEntityConsistency(Long projectId, @RequestParam(required = false) Long batchId, String email) {
        ThreadUtil.execAsync(() -> {
            statisticsService.exportConsistencyToExcel(projectId, batchId, email);
        });
    }

    /**
     * 标注一致性统计图表
     */
    @RequestMapping("/consistencyChart")
    public R consistencyChart(@RequestParam(required = false) Long projectId, @RequestParam(required = false) Long batchId) {
        Map<String, Object> vo = statisticsService.consistencyChart(projectId, batchId);
        return R.success(vo);
    }

    /**
     * 聚类树图
     */
    @RequestMapping("/drawDendrogram")
    public R drawDendrogram(Long projectId, boolean refresh) {
        return statisticsService.drawDendrogram(projectId, refresh);
    }
}
