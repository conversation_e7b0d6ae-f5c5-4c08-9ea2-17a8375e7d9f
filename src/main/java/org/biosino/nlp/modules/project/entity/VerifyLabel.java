package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 每个标签具体的实验验证情况
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
@TableName("t_verify_label")
public class VerifyLabel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long verifyTaskId;

    private Integer type;
    private Long labelId;
    private String labelName;

    // 导入实体数
    private Long entityTotal = 0L;
    // 应标实体数
    private Long needTotal = 0L;
    // 标对数TP
    private Long correctTotal = 0L;
    // 标错数FP
    private Long errorTotal = 0L;
    // 漏标数FN
    private Long missTotal = 0L;


    /**
     * 准确率 标注正确数 / 标注正确数 + 漏标数 + 标错数
     */
    @TableField(exist = false)
    private Double correctRate = 0.00d;
    /**
     * 精确率 P=TP/(TP+ FP)
     */
    @TableField(exist = false)
    private Double precision = 0.00d;
    /**
     * 召回率 R= TP/(TP+ FN)
     */
    @TableField(exist = false)
    private Double recall = 0.00d;
    /**
     * F1值 F = 2/(1/P + 1/R) = 2 * P * R/(P+ R)
     */
    @TableField(exist = false)
    private Double f1Score = 0.00d;

    @TableField(exist = false)
    private String taskName;

    /**
     * 是否删除[1.已删除值  0.未删除]
     */
    @TableLogic
    private Integer deleted;


}
