package org.biosino.nlp.modules.project.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据验证任务
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
public class VerifyTaskVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long projectId;

    private Long batchId;

    private String batchName;
    private String preSourceName;

    private String taskName;

    private String description;

    private Integer articleCount;

    private Integer status;
    private Integer method;

    private Date createTime;
}
