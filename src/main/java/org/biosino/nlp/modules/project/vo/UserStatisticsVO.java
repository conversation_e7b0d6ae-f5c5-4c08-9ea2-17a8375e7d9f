package org.biosino.nlp.modules.project.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 标注员、审核员统计
 *
 * <AUTHOR>
 * @date 2021-01-04 14:01
 */
@Data
public class UserStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long userId;
    private String username;

    /**
     * 已标注/已审核的文章数
     */
    private Integer count = 0;

    private Long entityCount = 0L;
    private Long correctEntityCount = 0L;

    private Long attrCount = 0L;
    private Long correctAttrCount = 0L;

    private Long entityAttrCount = 0L;
    private Long correctEntityAttr = 0L;

    private Long relationGroupCount = 0L;
    private Long correctRelationGroupCount = 0L;

    private Long relationCount = 0L;
    private Long correctRelationCount = 0L;

    // 应标数
    private Long needTotal = 0L;
    // 标对数 TP
    private Long correctTotal = 0L;
    // 标错数 FP
    private Long errorTotal = 0L;
    // 漏标数 FN
    private Long missTotal = 0L;

    /**
     * 返工率，百分制，不保留小数
     */
    private Double rework = 0.00d;
    /**
     * 准确率 标注正确数 / 标注正确数 + 漏标数 + 标错数
     */
    private Double correctRate = 0.00d;
    /**
     * 精确率 P=TP/(TP+ FP)
     */
    private Double precision = 0.00d;
    /**
     * 召回率 R= TP/(TP+ FN)
     */
    private Double recall = 0.00d;
    /**
     * F1值 F = 2/(1/P + 1/R) = 2 * P * R/(P+ R)
     */
    private Double f1Score = 0.00d;
    /**
     * 平均耗时
     */
    private Integer avgTime = 0;
}
