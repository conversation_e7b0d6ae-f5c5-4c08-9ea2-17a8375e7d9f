package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.project.dao.ProjectFileDao;
import org.biosino.nlp.modules.project.entity.Attachment;
import org.biosino.nlp.modules.project.service.AttachmentService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021-01-09 17:02
 */
@Service
public class AttachmentServiceImpl extends ServiceImpl<ProjectFileDao, Attachment> implements AttachmentService {


    @Override
    public Attachment upload(MultipartFile multipartFile) throws IOException {
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        if (!"pdf".equalsIgnoreCase(suffix)) {
            throw new RRException("请上传 PDF 格式文件");
        }
        String fileName = UUID.randomUUID() + "." + suffix;
        File file = new File(DIR, fileName);
        multipartFile.transferTo(file);

        if (!file.exists()) {
            throw new RRException("文件保存失败");
        }

        Attachment attachment = new Attachment();
        attachment.setFileName(fileName);
        attachment.setOriginalName(multipartFile.getOriginalFilename());
        attachment.setCreateTime(new Date());
        this.saveOrUpdate(attachment);

        return attachment;
    }

    @Override
    public void deleteFile(Long docId) {
        Attachment attachment = this.getById(docId);
        if (attachment == null) {
            return;
        }
        this.removeById(docId);
        File file = new File(DIR, attachment.getFileName());
        FileUtil.del(file);
    }

}
