package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 批次
 *
 * <AUTHOR>
 * @date 2020-12-4 10:04
 */
@Data
@TableName("t_batch")
public class Batch implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long batchId;
    private Long projectId;
    private String name;
    private String materialSource;
    private String searchCriteria;
    private String description;
    private Date createTime;
    private Date updateTime;
    private Integer totalArticle;
    private Integer free;

    /**
     * 0 按劳分配 1 均衡分配
     */
    private Integer mode;
    private Integer status;
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private String projectName;

}
