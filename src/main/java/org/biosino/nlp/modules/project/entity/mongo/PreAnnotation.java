package org.biosino.nlp.modules.project.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-01-18 16:07
 */
@Data
//@Document(collection = "m_pre_annotation")
public class PreAnnotation implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

//    @Indexed
    @Field(name = "note_id")
    private Long noteId;

//    @Indexed
    @Field(name = "project_id")
    private Long projectId;

//    @Indexed
    @Field(name = "batch_id")
    private Long batchId;

//    @Indexed
    @Field(name = "article_id")
    private String articleId;

    @Field(name = "text_id")
    private String textId;

    private Integer start;

    private Integer end;

    private String text;

    /**
     * 预标注的一级标签代码
     */
    private String label;

    /**
     * 预标注的二级标签代码
     */
    private String code;

    @Indexed
    private String source;

    private String annotate;

    @Indexed
    private Integer version;

    @Field(name = "create_time")
    private Date createTime;

    private transient int order;

    /**
     * 自定义 ID， 实现覆盖
     */
    public void generateId() {
        this.id = String.join("_", String.valueOf(noteId), textId, String.valueOf(start), String.valueOf(end), String.valueOf(source));
    }
}
