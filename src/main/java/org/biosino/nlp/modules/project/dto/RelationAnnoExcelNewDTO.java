package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class RelationAnnoExcelNewDTO implements Serializable {

    private static final long serialVersionUID = -6824736049215385142L;
    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "关系ID", order = 2)
    private String id;

    @ExcelProperty(value = "序号", order = 3)
    private Integer order;

    @ExcelProperty(value = "文本", order = 4)
    private String text;

    @ExcelProperty(value = "主语类型", order = 5)
    private String subjectType;

    @ExcelProperty(value = "主语", order = 6)
    private String subjectName;

    @ExcelProperty(value = "主语消歧名称", order = 7)
    private String subjectConceptText;

    @ExcelProperty(value = "主语消歧ID", order = 8)
    private String subjectConceptId;

    @ExcelProperty(value = "主语消歧来源", order = 9)
    private String subjectConceptSource;

    @ExcelProperty(value = "主语属性", order = 10)
    private String subjectAttr;

    @ExcelProperty(value = "主语属性详情", order = 11)
    private String subjectAttrInfo;

    @ExcelProperty(value = "主语起始位", order = 12)
    private String subjectStart;

    @ExcelProperty(value = "主语结束位", order = 13)
    private String subjectEnd;

    @ExcelProperty(value = "关系ID", order = 14)
    private Long relationId;

    @ExcelProperty(value = "关系", order = 15)
    private String relation;

    @ExcelProperty(value = "宾语类型", order = 16)
    private String objectType;

    @ExcelProperty(value = "宾语", order = 17)
    private String objectName;

    @ExcelProperty(value = "宾语消歧名称", order = 18)
    private String objectConceptText;

    @ExcelProperty(value = "宾语消歧ID", order = 19)
    private String objectConceptId;

    @ExcelProperty(value = "宾语消歧来源", order = 20)
    private String objectConceptSource;

    @ExcelProperty(value = "宾语属性", order = 21)
    private String objectAttr;

    @ExcelProperty(value = "宾语属性详情", order = 22)
    private String objectAttrInfo;

    @ExcelProperty(value = "宾语起始位", order = 23)
    private String objectStart;

    @ExcelProperty(value = "宾语结束位", order = 24)
    private String objectEnd;

    @ExcelProperty(value = "否定", order = 25)
    private Boolean negation;

    @ExcelProperty(value = "关系批注信息", order = 26)
    private String annotations;

    @ExcelProperty(value = "标注人", order = 27)
    private String annotatorName;

    @ExcelProperty(value = "审核人", order = 28)
    private String auditorName;

    @ExcelProperty(value = "标注时间", order = 29)
    private Date createTime;

    @Data
    public static class AttrInfo {
        @JSONField(ordinal = 0)
        private String name;
        @JSONField(ordinal = 1)
        private String field;
        @JSONField(ordinal = 2)
        private String start;
        @JSONField(ordinal = 3)
        private String end;
        @JSONField(ordinal = 4)
        private String content;
    }
}
