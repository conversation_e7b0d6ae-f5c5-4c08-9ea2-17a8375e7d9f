package org.biosino.nlp.modules.project.dto;

import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class RelationAnnoExcelDTO implements Serializable {
    private static final long serialVersionUID = -4212809305217485728L;

    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "关系ID", order = 2)
    private String id;

    @ExcelProperty(value = "序号", order = 3)
    private Integer order;

    @ExcelProperty(value = "主语", order = 4)
    private String subject;

    @ExcelProperty(value = "关系", order = 5)
    private String relation;

    @ExcelProperty(value = "宾语", order = 6)
    private String objects;

    @ExcelProperty(value = "主语实体ID", order = 7)
    private List<String> subjectIds;

    @ExcelProperty(value = "宾语实体ID", order = 8)
    private List<String> objectIds;

    @ExcelProperty(value = "否定", order = 9)
    private Boolean negation;

    @ExcelProperty(value = "关系批注信息", order = 10)
    private String annotations;

    @ExcelProperty(value = "same_text_id", order = 11)
    private Boolean sameTextId;

    @ExcelProperty(value = "标注人", order = 12)
    private String userName;

    @ExcelProperty(value = "标注时间", order = 13)
    private Date createTime;
}
