package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
@Data
@TableName("t_project_user")
public class ProjectUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long projectId;
    private Long userId;
    private Long roleId;
//    @TableLogic
//    private Integer deleted;

    @TableField(exist = false)
    private Integer markRounds;
    @TableField(exist = false)
    private Boolean autoReview;
}
