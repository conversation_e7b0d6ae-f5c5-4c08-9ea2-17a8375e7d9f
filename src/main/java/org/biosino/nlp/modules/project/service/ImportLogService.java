package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.project.dto.ImportLogDTO;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ImportLogService extends IService<ImportLog> {
    /**
     * 分页查询 导入日志
     */
    PageUtils queryPage(ImportLogDTO dto, Long userId);

    ResponseEntity<Resource> download(String type, Long id);

    Boolean existByProjectIdAndName(Long projectId, String filename);

    void changeEnabled(ImportLogDTO dto);

    Boolean existByIdAndType(Long id, Integer type);

    List<ImportLog> getPreSourceList(Long projectId);

    ImportLog findById(Long logId);
}
