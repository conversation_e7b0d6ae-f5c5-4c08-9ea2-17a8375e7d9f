package org.biosino.nlp.modules.project.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.project.dto.AIPreAnnoTaskDTO;
import org.biosino.nlp.modules.project.dto.AIPreAnnoTaskQueryDTO;
import org.biosino.nlp.modules.project.dto.EntityAnnoCleanJsonDTO;
import org.biosino.nlp.modules.project.entity.AIPreAnnoTask;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Set;

/**
 * AI预标注任务服务接口
 *
 * @date 2023/11/28
 */
public interface AIPreAnnoTaskService extends IService<AIPreAnnoTask> {

    /**
     * 创建AI预标注任务
     *
     * @param dto    任务参数
     * @param userId 用户ID
     * @return 任务ID
     */
    Long createTask(AIPreAnnoTaskDTO dto, Long userId);

    List<EntityAnnoCleanJsonDTO> getCleanEntityAnnoData(AIPreAnnoTaskDTO dto);

    /**
     * 分页查询AI预标注任务
     *
     * @param dto    查询参数
     * @param userId 用户ID
     * @return 分页数据
     */
    PageUtils queryPage(AIPreAnnoTaskQueryDTO dto, Long userId);

    ResponseEntity<Resource> download(Long id, String type);

    void applyPreAnno(Long id);

    List<Pair<String, Set<String>>> getLabelToEntitiesInfo(AIPreAnnoTaskDTO dto);

    /**
     * 暂停AI预标注任务
     *
     * @param id 任务ID
     */
    void pauseTask(Long id);

    /**
     * 恢复AI预标注任务
     *
     * @param id 任务ID
     */
    void resumeTask(Long id);

    Integer countTiktoken(String body);
}
