package org.biosino.nlp.modules.project.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.AttrAnnoEnum;
import org.biosino.nlp.common.enums.ImportStatusEnum;
import org.biosino.nlp.common.enums.ImportTypeEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.*;
import org.biosino.nlp.modules.labels.dto.SchemaDataDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.labels.service.RelationPatternService;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.biosino.nlp.modules.note.service.impl.RelationshipServiceImpl;
import org.biosino.nlp.modules.project.dto.EntityAttrPreAnnoDTO;
import org.biosino.nlp.modules.project.dto.NoteDTO;
import org.biosino.nlp.modules.project.dto.PreAnnoDTO;
import org.biosino.nlp.modules.project.dto.RelationPreAnnoDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.mongo.PreAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.nlp.common.utils.BnlpUtils.BATCH_IGNORE_FIELDS;
import static org.biosino.nlp.common.utils.BnlpUtils.CONTENT;

/**
 * <AUTHOR>
 * @date 2021-01-18 16:06
 */
@Slf4j
@Service
public class PreAnnotationService {

    @Autowired
    private NoteService noteService;
    @Autowired
    private BatchService batchService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ImportLogService importLogService;
    @Autowired
    private MongoOperations mongoOperations;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private AttributesRepository attributeRepository;
    @Autowired
    private EntityLabelService entityLabelService;
    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private RelationLabelService relationLabelService;
    @Autowired
    private RelationPatternService relationPatternService;
    @Autowired
    private RelationshipRepository relationshipRepository;
    @Autowired
    private DocumentService documentService;

    /**
     * 1.拉取和保存预标注信息
     * <p>
     * 修改项目预标注来源和批次导入文献时要使用
     */
    @Async
    public void savePreAnnotation(Long projectId, PreSourceEnum preSourceEnum) {
        if (preSourceEnum == null || preSourceEnum.getApi() == null) {
            return;
        }

        List<Batch> batches = batchService.list(Wrappers.<Batch>lambdaQuery().eq(Batch::getProjectId, projectId));

        batches.parallelStream().forEach(batch -> savePreAnnotation(batch, preSourceEnum));
    }

    /**
     * 2
     *
     * @param batch  批次
     * @param source 来源
     */
    @Async
    public void savePreAnnotation(Batch batch, PreSourceEnum source) {
        if (source == null || source.getApi() == null) {
            return;
        }
        // 当该批次拉取过这个来源的预标注信息时，不再拉取
        boolean exists = mongoOperations.exists(Query.query(Criteria.where("batch_id").is(batch.getBatchId())), PreAnnotation.class);
        if (exists) {
            return;
        }

        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, batch.getBatchId()));
        notes.forEach(note -> {
            Query query = Query.query(Criteria.where("id").is(note.getDocumentId()));
            query.fields().include("version");
            Document document = mongoOperations.findOne(query, Document.class);

            if (document == null) {
                return;
            }

            // 如果是PMID获取方式则调用此API
            List<PreAnnotation> preAnnotations = this.getPreAnnotationByPmid(note.getArticleId(), document.getVersion(), source);
            if (CollUtil.isNotEmpty(preAnnotations)) {
                preAnnotations.forEach(it -> {
                    it.setProjectId(batch.getProjectId());
                    it.setBatchId(note.getBatchId());
                    it.setNoteId(note.getNoteId());
                    it.setSource(source.name());
                    it.setCreateTime(new Date());
                    it.generateId();

                    mongoOperations.save(it);
                });
            }

            // 如果是用段落去解析
            List<PreAnnoDTO> preAnnoList = this.getPreAnnotationByContent(document, source);
            if (CollUtil.isNotEmpty(preAnnoList)) {
                List<PreAnnotation> preAnnotationList = new ArrayList<>();
                for (PreAnnoDTO preAnnoDTO : preAnnoList) {
                    PreAnnotation preAnnotation = new PreAnnotation();
                    preAnnotation.setNoteId(note.getNoteId());
                    preAnnotation.setProjectId(batch.getProjectId());
                    preAnnotation.setBatchId(note.getBatchId());
                    preAnnotation.setArticleId(note.getArticleId());
                    preAnnotation.setTextId(preAnnoDTO.getTextId());
                    preAnnotation.setStart(preAnnoDTO.getStart());
                    preAnnotation.setEnd(preAnnoDTO.getEnd());
                    preAnnotation.setLabel(preAnnoDTO.getLabel());
                    preAnnotation.setText(preAnnoDTO.getTerm());
                    preAnnotation.setSource(source.name());
                    preAnnotation.setVersion(Integer.parseInt(Objects.requireNonNull(parseVersion(note.getDocumentId()))));
                    preAnnotation.setCreateTime(new Date());
                    preAnnotation.generateId();
                    preAnnotationList.add(preAnnotation);
                }
                mongoOperations.save(preAnnotationList);
            }
        });
    }

    /**
     * 通过 API传PMID 获取预标注信息
     */
    private List<PreAnnotation> getPreAnnotationByPmid(String articleId, Integer version, PreSourceEnum preSourceEnum) {
        String url = String.join("/", preSourceEnum.getApi(), articleId, version.toString());
        String resp = HttpUtils.httpGet(url);
        return JSONArray.parseArray(resp, PreAnnotation.class);
    }

    /**
     * 通过 API解析文本 获取预标注信息
     */
    private List<PreAnnoDTO> getPreAnnotationByContent(Document document, PreSourceEnum preSourceEnum) {
        return findContent(document, preSourceEnum);
    }

    /**
     * 查找文档JSON串中的content内容
     */
    private List<PreAnnoDTO> findContent(Object obj, PreSourceEnum preSourceEnum) {
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        List<PreAnnoDTO> preAnnoDtoList = new ArrayList<>();

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不是标注文本
                if (BATCH_IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (CONTENT.equals(name)) {
                    String textId = (String) ReflectUtil.getFieldValue(currentObj, "id");
                    getPreAnnotationByContent(textId, value.toString(), preAnnoDtoList, preSourceEnum);
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
        return preAnnoDtoList;
    }

    /**
     * 拿出content的内容去远程解析
     */
    private void getPreAnnotationByContent(String textId, String content, List<PreAnnoDTO> dtoList, PreSourceEnum preSourceEnum) {
        String url = String.join("/", preSourceEnum.getApi(), content);
        String resp = HttpUtils.httpGet(url);
        List<PreAnnoDTO> list = JSONArray.parseArray(resp, PreAnnoDTO.class);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (PreAnnoDTO preAnnoDTO : list) {
            preAnnoDTO.setTextId(textId);
        }
        dtoList.addAll(list);
    }

    public R saveLoadPreAnnotation(Long projectId, File file) {
        if (!file.exists()) {
            return R.error("文件上传失败！");
        }
        if (!FileUtils.isUTF8(file)) {
            return R.error("文件必须为UTF-8编码");
        }

        CsvReader reader = null;
        List<PreAnnotation> preAnnotationList = new ArrayList<>();
        try {
            CsvReadConfig config = CsvReadConfig.defaultConfig();
            config.setContainsHeader(true);
            config.setSkipEmptyRows(true);
            reader = CsvUtil.getReader(config);

            CsvData csvData = reader.read(file, StandardCharsets.UTF_8);

            List<String> header = csvData.getHeader().stream().map(x -> x.toLowerCase().trim().replace("\ufeff", "")).collect(Collectors.toList());

            List<String> titleColumn = CollUtil.newArrayList("articleId", "start", "end", "textId", "text", "label", "code", "annotate");

            // 校验表头
            for (String column : titleColumn) {
                if (!CollUtil.contains(header, column.toLowerCase())) {
                    return R.error(column + " 列不能为空");
                }
            }


            for (CsvRow row : csvData) {
                PreAnnotation preAnnotation = new PreAnnotation();
                String articleId = row.get(getColIndex(header, "articleId"));
                Integer start = Integer.parseInt(row.get(getColIndex(header, "start")));
                Integer end = Integer.parseInt(row.get(getColIndex(header, "end")));
                String textId = row.get(getColIndex(header, "textId"));
                String text = row.get(getColIndex(header, "text"));
                String label = row.get(getColIndex(header, "label"));
                String code = row.get(getColIndex(header, "code"));
                String annotate = row.get(getColIndex(header, "annotate"));
                if (StrUtil.isBlank(articleId)) {
                    return R.error("articleId列不能为空");
                }
                if (StrUtil.isBlank(textId)) {
                    return R.error("textId列不能为空");
                }
                if (StrUtil.isBlank(text)) {
                    return R.error("text列不能为空");
                }
                if (StrUtil.isBlank(label)) {
                    return R.error("label列不能为空");
                }
//                if (StrUtil.isBlank(annotate)) {
//                    return R.error("annotate列不能为空");
//                }
                preAnnotation.setArticleId(articleId);
                preAnnotation.setStart(start);
                preAnnotation.setEnd(end);
                preAnnotation.setTextId(textId);
                preAnnotation.setText(text);
                preAnnotation.setLabel(label);
                preAnnotation.setCode(code);
                preAnnotation.setAnnotate(annotate);
                preAnnotationList.add(preAnnotation);
            }
        } catch (NumberFormatException e) {
            return R.error("start或end解析出错，请检查是否含全部为整数型数据！");
        } catch (Exception e) {
            return R.error("文件数据解析出错，请核实数据格式与类型！");
        } finally {
            IoUtil.close(reader);
            FileUtil.del(file);
        }

        ThreadUtil.execAsync(() -> {
            importPreAnnotation(projectId, preAnnotationList);
        });
        return R.ok();
    }

    private Integer getColIndex(List<String> header, String target) {
        return header.indexOf(target.toLowerCase().trim());
    }

    public void importPreAnnotation(Long projectId, List<PreAnnotation> preAnnotations) {
        Map<String, NoteDTO> noteMap = noteService.findAIdAndDIdByProjectId(projectId);

        for (PreAnnotation preAnnotation : preAnnotations) {
            NoteDTO noteDTO = noteMap.get(preAnnotation.getArticleId());
            if (noteDTO != null) {
                preAnnotation.setProjectId(projectId);
                preAnnotation.setBatchId(noteDTO.getBatchId());
                preAnnotation.setNoteId(noteDTO.getNoteId());
                preAnnotation.setCreateTime(new Date());
                preAnnotation.setSource(PreSourceEnum.CUSTOMIZE.name());
                preAnnotation.setVersion(Integer.parseInt(Objects.requireNonNull(parseVersion(noteDTO.getDocumentId()))));
                preAnnotation.generateId();
                mongoOperations.save(preAnnotation);

                // TODO: 修改掉错误编码引起的乱码数据，该代码只保留一段时间，记得删除
                String entityId = preAnnotation.getId().replace("_CUSTOMIZE", "");
                Entity entity = mongoOperations.findOne(Query.query(Criteria.where("_id").is(entityId)), Entity.class);
                if (entity != null) {
                    // TODO sw 预标注
//                    entity.setText(preAnnotation.getText());
                    mongoOperations.save(entity);
                }
                log.debug("预标注数据导入成功,ID: {}", preAnnotation.getId());
            }
        }
    }

    /**
     * 从documentId解析获取到版本号
     */
    private String parseVersion(String str) {
        char[] array = str.toCharArray();
        for (int i = array.length - 1; i > -1; i--) {
            int ascii = array[i];
            if ((ascii >= 65 && ascii <= 90) || (ascii >= 97 && ascii <= 122)) {
                return str.substring(i + 1);
            }
        }
        return null;
    }

    public void mergeEntity(Long projectId) {
        // TODO:entity修改
//        log.debug("开始导入所有预标注实体到标注原文，ProjectId:{}", projectId);
//        Query query = Query.query(Criteria.where("project_id").is(projectId)
//                .and("source").is(PreSourceEnum.CUSTOMIZE.name()));
//        List<PreAnnotation> preAnnotations = mongoOperations.find(query, PreAnnotation.class);
//
//        // 查询标注是否和人工标注的重叠
//        Query entityQuery = Query.query(Criteria.where("project_id").is(projectId));
//        List<Entity> entities = mongoOperations.find(entityQuery, Entity.class);
//        Map<String, Entity> entityMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(entities)) {
//            entityMap = entities.stream().collect(Collectors.toMap(Entity::getId, x -> x));
//        }
//
//        List<EntityLabel> entityLabels = entityLabelService.getFirstLabelByProjectId(projectId);
//        Map<String, Long> labelMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(entityLabels)) {
//            labelMap = entityLabels.stream().collect(Collectors.toMap(EntityLabel::getValue, EntityLabel::getId));
//        }
//        List<EntityLabel> secondLabels = entityLabelService.getSecondLabelByProjectId(projectId);
//        if (CollUtil.isNotEmpty(secondLabels)) {
//            labelMap.putAll(secondLabels.stream().collect(Collectors.toMap(EntityLabel::getValue, EntityLabel::getId)));
//        }
//
//        for (PreAnnotation preAnnotation : preAnnotations) {
//            String entityId = getEntityId(preAnnotation);
//            // 存在相同的实体ID则跳过
//            if (entityMap.containsKey(entityId)) {
//                continue;
//            }
//            Entity entity = new Entity();
//            entity.setProjectId(preAnnotation.getProjectId());
//            entity.setBatchId(preAnnotation.getBatchId());
//            entity.setNoteId(preAnnotation.getNoteId());
//            entity.setArticleId(preAnnotation.getArticleId());
//            entity.setTextId(preAnnotation.getTextId());
//            entity.setText(preAnnotation.getText());
//            entity.setStart(preAnnotation.getStart());
//            entity.setEnd(preAnnotation.getEnd());
//            entity.setAnnotate(preAnnotation.getAnnotate());
//
//            entity.setId(entityId);
//            if (labelMap.containsKey(preAnnotation.getLabel())) {
//                entity.setLabelId(labelMap.get(preAnnotation.getLabel()));
//            }
//            if (labelMap.containsKey(preAnnotation.getCode())) {
//                entity.setSubLabels(labelMap.get(preAnnotation.getCode()));
//            }
//            entity.setCreateTime(DateUtils.getMongoDate(new Date()));
//            entityRepository.save(entity);
//        }
//        log.debug("完成导入所有预标注实体到标注原文，ProjectId:{}", projectId);
    }

    public String getEntityId(PreAnnotation preAnnotation) {
        if (preAnnotation.getNoteId() != null && StrUtil.isNotBlank(preAnnotation.getTextId()) && preAnnotation.getStart() != null && preAnnotation.getEnd() != null) {
            return preAnnotation.getNoteId() + "_" + preAnnotation.getTextId() + "_" + preAnnotation.getStart() + "_" + preAnnotation.getEnd();
        }
        return null;
    }

    @SneakyThrows
    @CacheEvict(cacheNames = "existSource_cache", allEntries = true)
    public void uploadPreAnno(String type, Long projectId, String originalFilename, File file, Long userId) {

        if (StrUtil.length(originalFilename) > 50) {
            throw new RRException("文件名不能超过50个字符");
        }

        // 判断文件内容是否合法的JSON字符串
        String content = FileUtil.readUtf8String(file);
        if (!JsonValidator.isJsonValid(content)) {
            throw new RRException("文件内容不是合法的json字符串");
        }

        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new RRException("项目未找到");
        }

        // 查询是否存在已经导入成功的
        Boolean exist = importLogService.existByProjectIdAndName(projectId, originalFilename);
        if (exist) {
            throw new RRException("该项目下已存在同名的导入记录");
        }

        // 导入实体预标注数据， 生成导入记录
        ImportLog importLog = new ImportLog();
        importLog.setName(originalFilename);
        importLog.setProjectId(project.getProjectId());
        importLog.setStatus(ImportStatusEnum.wait.getValue());
        importLog.setCreateTime(new Date());
        importLog.setUserId(userId);

        importLog.setImportFilePath(file.getAbsolutePath());
        if ("entity".equals(type)) {
            importLog.setType(ImportTypeEnum.pre_anno_entity.getValue());
        } else if ("attr".equals(type)) {
            importLog.setType(ImportTypeEnum.pre_anno_attr.getValue());
        } else {
            importLog.setType(ImportTypeEnum.pre_anno_relation.getValue());
        }

        importLogService.save(importLog);

        // 校验数据并导入
        if ("relation".equals(type)) {
            ThreadUtil.execAsync(() -> importRelationPreAnno(file, importLog, project));
        } else {
            ThreadUtil.execAsync(() -> importEntityAttrPreAnno(file, importLog, project));
        }
    }

    @SneakyThrows
    public void uploadPreAnno(String entity, Long projectId, MultipartFile multipartFile, Long creator) {
        // 将上传的文件落盘
        File homeDir = Constant.getHomeDir(Constant.DirectoryEnum.attachment);
        String currentDateStr = DateUtil.date().toString("yyyy-MM-dd");

        File file = FileUtil.file(homeDir, currentDateStr, System.currentTimeMillis()
                + RandomUtil.randomString(6)
                + ".json");
        FileUtil.mkParentDirs(file);
        FileUtil.touch(file);
        multipartFile.transferTo(file);

        String originalFilename = multipartFile.getOriginalFilename();
        uploadPreAnno(entity, projectId, originalFilename, file, creator);
    }

    public void importRelationPreAnno(File file, ImportLog importLog, Project project) {
        Set<String> allErrors = new LinkedHashSet<>();
        List<RelationPreAnnoDTO> list = new ArrayList<>();
        String jsonString = FileUtil.readUtf8String(file);

        // 查询此项目下所有开启的实体标签
        Map<String, Long> entityLabelMap = entityLabelService.findAllEnableByProjectId(project.getProjectId())
                .stream().collect(Collectors.toMap(EntityLabel::getName, EntityLabel::getId));
        // 查询实体标签下的属性标签
        Map<Long, Map<String, Long>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.values())
                .stream().collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getName, AttributeLabel::getId)));
        // 查询此项目下所有开启的关系标签
        Map<String, Long> relationLabelMap = relationLabelService.findAllEnableByProjectId(project.getProjectId())
                .stream().collect(Collectors.toMap(RelationLabel::getName, RelationLabel::getId));
        // 查询此项目下所有开启的关系模板
        Map<String, RelationPattern> relationPatternMap = relationPatternService.findAllEnableByProjectId(project.getProjectId())
                .stream().collect(Collectors.toMap(RelationPattern::getName, x -> x));

        try {
            list = JSON.parseArray(jsonString, RelationPreAnnoDTO.class);
            // 校验json文件内容是否合法
            validRelationPreAnnoDTO(list, project, importLog, entityLabelMap, entityIdAttrLabelMap, relationLabelMap, relationPatternMap, allErrors);
        } catch (JSONException e) {
            allErrors.add("json格式错误");
            e.printStackTrace();
        } catch (Exception e) {
            allErrors.add("导入出错");
            e.printStackTrace();
        } finally {
            // 如果allErrors不为空证明导入有异常,不导入此次
            if (!allErrors.isEmpty()) {
                // 将错误日志落盘
                syncErrorLog(importLog, allErrors);
            } else {
                // 校验通过，入库
                try {
                    saveRelationPreAnno(list, project, entityLabelMap, entityIdAttrLabelMap, relationLabelMap, relationPatternMap, importLog);
                } catch (Exception e) {
                    allErrors.add("保存时出错");
                    e.printStackTrace();
                } finally {
                    if (!allErrors.isEmpty()) {
                        // 将错误日志落盘
                        syncErrorLog(importLog, allErrors);
                    } else {
                        // 更新 ”导入记录“ 的状态
                        importLog.setStatus(ImportStatusEnum.finish.getValue());
                        importLog.setFinishTime(new Date());
                    }
                }
            }
            importLogService.saveOrUpdate(importLog);
        }
    }

    public static void syncErrorLog(ImportLog importLog, Set<String> allErrors) {
        File logFile = new File(Constant.getHomeDir(Constant.DirectoryEnum.temp),
                System.currentTimeMillis() + RandomUtil.randomString(6) + ".log");
        FileUtil.writeLines(allErrors, logFile, Charset.defaultCharset());
        importLog.setLogFilePath(logFile.getAbsolutePath());
        importLog.setStatus(ImportStatusEnum.fail.getValue());
        importLog.setFinishTime(new Date());
    }

    private void validRelationPreAnnoDTO(List<RelationPreAnnoDTO> list, Project project, ImportLog importLog, Map<String, Long> entityLabelMap,
                                         Map<Long, Map<String, Long>> entityIdAttrLabelMap, Map<String, Long> relationLabelMap,
                                         Map<String, RelationPattern> relationPatternMap, Set<String> allErrors) {

        for (int i = 0; i < list.size(); i++) {
            int index = i + 1;
            RelationPreAnnoDTO dto = list.get(i);

            String articleId = dto.getArticleId();
            if (StrUtil.isBlank(articleId)) {
                allErrors.add(StrUtil.format("第 {} 条数据，article_id不能为空", index));
            } else {
                Note note = noteService.findFirstByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());
                // 校验article_id在此项目中是否存在
                if (note == null) {
                    allErrors.add(StrUtil.format("第 {} 条数据，article_id在此项目中不存在", index));
                    continue;
                }

                // 查出正文
                Document document = documentService.getDocumentById(note.getDocumentId());
                // 将正文中所有id字段的值找出来
                List<String> ids = new ArrayList<>();
                flatAllIds(document, ids);

                // 获取关系模板的数据，用于校验导入的关系标签
                RelationPattern pattern = relationPatternMap.get(dto.getPattern());
                // 查询pattern_id在项目中是否存在
                if (pattern == null) {
                    allErrors.add(StrUtil.format("第 {} 条数据，pattern:{} 在此项目中不存在", index, dto.getPattern()));
                    break;
                }
                // 校验items的内容，先对items根据order排序
                List<RelationPreAnnoDTO.RelationItem> items = dto.getItems();

                // 关系模板
                List<SchemaDataDTO> schemas = JSON.parseArray(pattern.getSchemaData(), SchemaDataDTO.class);
                Map<Integer, Boolean> orderRequiredMap = new HashMap<>();
                schemas.forEach(x -> orderRequiredMap.put(x.getOrder(), x.getRequired()));
                HashSet<Integer> orderNumSet = new HashSet<>();
                boolean flag = false;
                for (RelationPreAnnoDTO.RelationItem x : items) {
                    Integer order = x.getOrder();
                    if (order == null) {
                        allErrors.add(StrUtil.format("第 {} 条数据，order不能为空", index));
                    }
                    if (!orderNumSet.add(order)) {
                        allErrors.add(StrUtil.format("第 {} 条数据，order: {} 不能重复", index, order));
                        flag = true;
                    }
                    if (!orderRequiredMap.containsKey(order)) {
                        allErrors.add(StrUtil.format("第 {} 条数据，order: {} 在模板中不存在", index, order));
                        flag = true;
                    }
                }
                if (flag) {
                    break;
                }
                for (SchemaDataDTO schema : schemas) {
                    for (int k = 0; k < items.size(); k++) {
                        RelationPreAnnoDTO.RelationItem item = items.get(k);
                        int j = k + 1;
                        // 双循环通过order找到关系对应的模板
                        if (schema.getOrder().equals(item.getOrder())) {
                            // 开始校验主（subject）谓（关系标签）宾（object）
                            List<EntityAttrPreAnnoDTO.Info> subject = item.getSubject();
                            // 校验subject是否符合要求
                            // 如果外键不为空就需要精确指向order
                            if (schema.getSubject().getForeign() != null) {
                                if (!schema.getSubject().getForeign().equals(item.getSubjectForeign())) {
                                    allErrors.add(StrUtil.format("第 {} 条数据,items 第 {} 条,subjectForeign：{} 不符合模板", index, j, item.getSubjectForeign()));
                                }
                            } else if (schema.getSubject().getMultiple() != null) {
                                if (schema.getSubject().getMultiple().equals(false)
                                        && subject.size() > 1) {
                                    allErrors.add(StrUtil.format("第 {} 条数据,items 第 {} 条,subject实体数量只能为1", index, j));
                                } else {
                                    validSubjectOrObjects(entityLabelMap, entityIdAttrLabelMap, allErrors, index, ids, j, subject);
                                }
                            }
                            // 校验关系标签
                            if (!relationLabelMap.containsKey(item.getRelation())) {
                                allErrors.add(StrUtil.format("第 {} 条数据,items 第 {} 条,关系 {} 在此项目中不存在", index, j, item.getRelation()));
                            }
                            // 校验subject是否符合要求
                            // 如果外键不为空就需要精确指向order
                            List<EntityAttrPreAnnoDTO.Info> objects = item.getObjects();
                            // 如果外键不是空证明指向的外键不正确
                            if (schema.getObjects().getForeign() != null) {
                                if (!schema.getObjects().getForeign().equals(item.getObjectsForeign())) {
                                    allErrors.add(StrUtil.format("第 {} 条数据,items 第 {} 条,objectsForeign：{} 不符合模板", index, j, item.getObjectsForeign()));
                                }
                            } else if (schema.getObjects().getMultiple() != null) {
                                if (schema.getObjects().getMultiple().equals(false)
                                        && objects.size() > 1) {
                                    allErrors.add(StrUtil.format("第 {} 条数据,items 第 {} 条,objects实体数量只能为1", index, j));
                                } else {
                                    validSubjectOrObjects(entityLabelMap, entityIdAttrLabelMap, allErrors, index, ids, j, objects);
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    private void validSubjectOrObjects(Map<String, Long> entityLabelMap, Map<Long, Map<String, Long>> entityIdAttrLabelMap, Set<String> allErrors, int index, List<String> ids, int j, List<EntityAttrPreAnnoDTO.Info> subject) {
        for (EntityAttrPreAnnoDTO.Info info : subject) {
            if (!entityLabelMap.containsKey(info.getLabel())) {
                allErrors.add(StrUtil.format("第 {} 条数据下,items第 {} 条，实体标签（label） {} 在此项目中不存在", index, j, info.getLabel()));
            } else {
                Long entityLabelId = entityLabelMap.get(info.getLabel());
                Map<String, Long> attrLabelMap = entityIdAttrLabelMap.get(entityLabelId);
                if (info.getAttribute() != null) {
                    for (EntityAttrPreAnnoDTO.AttrInfo attrInfo : info.getAttribute()) {
                        String attrName = attrInfo.getAttrName();
                        if (!attrLabelMap.containsKey(attrName)) {
                            allErrors.add(StrUtil.format("第 {} 条数据下,items第 {} 条，关系标签（attr_name） {} 在此项目中不存在", index, j, attrName));
                        }
                        for (EntityAttrPreAnnoDTO.Attribute attr : attrInfo.getAttrs()) {
                            if (attr.getEntity() == null && attr.getContent() == null) {
                                allErrors.add(StrUtil.format("第 {} 条数据下,items第 {} 条，attrs填写错误", index, j));
                            }
                            if (attr.getEntity() != null) {
                                for (AnnoDTO.EntityInfo entityInfo : attr.getEntity()) {
                                    String textId = entityInfo.getTextId();
                                    if (!ids.contains(textId)) {
                                        allErrors.add(StrUtil.format("第 {} 条数据下,items第 {} 条， text_id 在此项目中不存在", index, j, textId));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRelationPreAnno(List<RelationPreAnnoDTO> list, Project project, Map<String, Long> entityLabelMap,
                                    Map<Long, Map<String, Long>> entityIdAttrLabelMap, Map<String, Long> relationLabelMap,
                                    Map<String, RelationPattern> relationPatternMap, ImportLog importLog) {
        List<Relationship> relationList = new ArrayList<>();
        List<Entity> entityList = new ArrayList<>();
        List<Attributes> attributesList = new ArrayList<>();
        Map<String, String> md5EntityIdMap = new HashMap<>();

        for (RelationPreAnnoDTO dto : list) {
            // 20240103 修改，一个预标注可以导入到多篇note里面
            // Note note = noteService.findFirstByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());
            List<Note> noteList = noteService.findByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());
            Set<String> md5Set = new HashSet<>();
            for (Note note : noteList) {

                String articleId = note.getArticleId();
                Long projectId = note.getProjectId();
                Long batchId = note.getBatchId();
                Long noteId = note.getNoteId();

                // RelationItem 先根据order排序
                List<RelationPreAnnoDTO.RelationItem> preAnnoItems = dto.getItems().stream().sorted(Comparator.comparing(RelationPreAnnoDTO.RelationItem::getOrder)).collect(Collectors.toList());

                // 将json的 RelationPreAnnoDTO.RelationItem 转换为 Relationship.RelationItem
                List<Relationship.RelationItem> items = new ArrayList<>();
                for (RelationPreAnnoDTO.RelationItem x : preAnnoItems) {
                    Relationship.RelationItem item = new Relationship.RelationItem();
                    item.setOrder(x.getOrder());
                    item.setAnnotations(x.getAnnotations());
                    item.setNegation(x.getNegation());
                    // 将关系标签name转为id
                    item.setRelation(relationLabelMap.get(x.getRelation()));
                    if (x.getSubjectForeign() != null) {
                        item.setSubject(CollUtil.newArrayList(x.getSubjectForeign()));
                    } else {
                        List<String> subjectArr = new ArrayList<>();
                        for (EntityAttrPreAnnoDTO.Info info : x.getSubject()) {
                            genEntity(md5EntityIdMap, entityLabelMap, entityIdAttrLabelMap, importLog, entityList, attributesList, note, subjectArr, info);
                        }
                        item.setSubject(subjectArr);
                    }
                    if (x.getObjectsForeign() != null) {
                        item.setObjects(CollUtil.newArrayList(x.getObjectsForeign()));
                    } else {
                        List<String> objectsArr = new ArrayList<>();
                        for (EntityAttrPreAnnoDTO.Info info : x.getObjects()) {
                            genEntity(md5EntityIdMap, entityLabelMap, entityIdAttrLabelMap, importLog, entityList, attributesList, note, objectsArr, info);
                        }
                        item.setObjects(objectsArr);
                    }
                    items.add(item);
                }

                // 关系标签的属性设置
                Relationship relationship = new Relationship();
                relationship.setId(IdUtil.simpleUUID());
                relationship.setNoteId(noteId);
                relationship.setArticleId(articleId);
                relationship.setProjectId(projectId);
                relationship.setBatchId(batchId);

                // 获取关系模板id
                Long patternId = relationPatternMap.get(dto.getPattern()).getId();
                // 计算md5的同时顺带排序
                String relationMd5 = RelationshipServiceImpl.genRelationStrMd5(items, patternId, entityList);
                int preSource = PreSourceEnum.CUSTOMIZE.getId();
                // if (relationshipRepository.existsByTaskIdAndNoteIdAndAnnotatorIdAndAuditorIdAndSourceAndMd5AndDeleted(null, noteId, null, null, PreSourceEnum.CUSTOMIZE.getId(), relationMd5, false)) {
                //     continue;
                // }
                if (md5Set.contains(relationMd5)) {
                    continue;
                } else {
                    md5Set.add(relationMd5);
                }
                relationship.setMd5(relationMd5);
                relationship.setItems(items);
                relationship.setPatternId(patternId);
                Date current = new Date();
                relationship.setCreateTime(current);
                relationship.setUpdateTime(current);
                relationship.setImportLogId(importLog.getId());
                relationship.setSource(preSource);
                relationList.add(relationship);
            }
        }
        // 保存数据
        relationshipRepository.saveAll(relationList);
        entityRepository.saveAll(entityList);
        attributeRepository.saveAll(attributesList);
    }

    private void genEntity(Map<String, String> md5EntityIdMap, Map<String, Long> entityLabelMap, Map<Long, Map<String, Long>> entityIdAttrLabelMap, ImportLog importLog, List<Entity> entityList, List<Attributes> attributesList, Note note, List<String> subjectArr, EntityAttrPreAnnoDTO.Info info) {

        String articleId = note.getArticleId();
        Long projectId = note.getProjectId();
        Long batchId = note.getBatchId();
        Long noteId = note.getNoteId();
        // 获取实体标签id
        Long labelId = entityLabelMap.get(info.getLabel());
        Map<String, Long> attrLabelMap = entityIdAttrLabelMap.get(labelId);
        // 给entityInfo补充uniqueId
        List<AnnoDTO.EntityInfo> entityInfos = info.getEntity().stream().distinct().peek(x -> {
            x.setUniqueid(IdUtil.simpleUUID());
        }).collect(Collectors.toList());
        // 计算此实体的md5信息
        String entityMd5 = NoteServiceImpl.genEntityStrMd5(entityInfos, labelId, AttrAnnoEnum.not_attr.getCode());
        // 预计标注来源是人工预标注
        int preSource = PreSourceEnum.CUSTOMIZE.getId();

        // 划词实体的id
        String entityId = IdUtil.simpleUUID();

        // 如果这个md5有记录，则这个实体已经生成过了，直接返回id
        if (md5EntityIdMap.containsKey(entityMd5)) {
            subjectArr.add(md5EntityIdMap.get(entityMd5));
            return;
        } else {
            // 计算出md5查询数据库，如果数据库中存在，说明已经导入过则需要跳过
            // Optional<Entity> optional = entityRepository.findFirstByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(entityMd5,
            //         noteId, note.getArticleId(), AttrAnnoEnum.not_attr.getCode(), preSource);
            // if (optional.isPresent()) {
            //     Entity entity = optional.get();
            //     String id = entity.getId();
            //     md5EntityIdMap.put(entityMd5, id);
            //     subjectArr.add(id);
            //     entityList.add(entity);
            //     return;
            // }
            // 否者把这个一个新的实体记录下来
            md5EntityIdMap.put(entityMd5, entityId);
            subjectArr.add(entityId);
        }
        // 构建此实体
        Entity entity = new Entity();
        entity.setId(entityId);
        entity.setNoteId(noteId);
        entity.setProjectId(projectId);
        entity.setBatchId(batchId);
        entity.setArticleId(articleId);
        entity.setLabelId(labelId);
        entity.setMd5(entityMd5);
        entity.setAnnotate(info.getAnnotation());
        entity.setIsAttr(AttrAnnoEnum.not_attr.getCode());
        entity.setEntityInfos(entityInfos);
        entity.setSource(preSource);
        entity.setImportLogId(importLog.getId());
        Date currentDate = new Date();
        entity.setCreateTime(currentDate);
        entity.setUpdateTime(currentDate);
        entity.setDeleted(false);
        // 添加到entityList
        entityList.add(entity);
        // 开始生成实体下的划词属性和自定义属性
        if (CollUtil.isNotEmpty(info.getAttribute())) {
            for (EntityAttrPreAnnoDTO.AttrInfo attribute : info.getAttribute()) {
                // 属性标签id
                Long attrLabelId = attrLabelMap.get(attribute.getAttrName());
                for (EntityAttrPreAnnoDTO.Attribute attr : attribute.getAttrs()) {

                    Attributes attributes = new Attributes();
                    attributes.setId(IdUtil.simpleUUID());
                    attributes.setProjectId(projectId);
                    attributes.setBatchId(batchId);
                    attributes.setArticleId(articleId);
                    attributes.setEntityId(entityId);
                    attributes.setAttrLabelId(attrLabelId);
                    attributes.setCreateTime(currentDate);
                    attributes.setUpdateTime(currentDate);
                    attributes.setEntityMd5(entityMd5);
                    // 如果是attr.getEntity()不为空则是划词属性

                    if (CollUtil.isNotEmpty(attr.getEntity())) {
                        String attributesId = IdUtil.simpleUUID();

                        Entity attrEntity = new Entity();
                        attrEntity.setId(attributesId);
                        attrEntity.setNoteId(noteId);
                        attrEntity.setProjectId(projectId);
                        attrEntity.setBatchId(batchId);
                        attrEntity.setArticleId(articleId);
                        // 给info的uniqueid设置新的uuid
                        List<AnnoDTO.EntityInfo> collect = attr.getEntity().stream().distinct().peek(x -> x.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                        String attrEntityMd5 = NoteServiceImpl.genEntityStrMd5(collect, null, AttrAnnoEnum.is_attr.getCode());
                        attrEntity.setMd5(attrEntityMd5);

                        attributes.setAttrMd5(attrEntityMd5);

                        attrEntity.setEntityInfos(collect);
                        attrEntity.setSource(preSource);
                        attrEntity.setIsAttr(AttrAnnoEnum.is_attr.getCode());
                        attrEntity.setImportLogId(importLog.getId());
                        attrEntity.setCreateTime(currentDate);
                        attrEntity.setUpdateTime(currentDate);
                        attrEntity.setEntityInfos(attr.getEntity());
                        entityList.add(attrEntity);
                        attributes.setAttributeId(attributesId);
                        attributes.setContent(collect.stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")));
                    } else {
                        // 是自定义属性
                        attributes.setContent(attr.getContent());
                        attributes.setAttrMd5(SecureUtil.md5(StrUtil.trimToEmpty(attr.getContent())));
                    }
                    attributesList.add(attributes);
                }
            }
        }
    }


    private void importEntityAttrPreAnno(File file, ImportLog importLog, Project project) {
        Set<String> allErrors = new LinkedHashSet<>();

        Map<String, Long> entityLabelMap = entityLabelService.findAllEnableByProjectId(project.getProjectId()).stream().collect(Collectors.toMap(EntityLabel::getName, EntityLabel::getId));

        Map<Long, Map<String, Long>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.values())
                .stream()
                .collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getName, AttributeLabel::getId)));

        List<EntityAttrPreAnnoDTO> list = new ArrayList<>();
        try {
            String jsonString = FileUtil.readUtf8String(file);

            list = JSON.parseArray(jsonString, EntityAttrPreAnnoDTO.class);
            // 校验json文件内容是否合法
            validEntityAttrPreAnnoDTO(list, entityLabelMap, entityIdAttrLabelMap, project, importLog, allErrors);
        } catch (JSONException e) {
            allErrors.add("json格式错误");
        } catch (Exception e) {
            allErrors.add("导入出错");
        } finally {
            // 如果allErrors不为空证明导入有异常,不导入此次
            if (!allErrors.isEmpty()) {
                // 将错误日志落盘
                syncErrorLog(importLog, allErrors);
            } else {
                try {
                    saveEntityAttrPreAnno(list, entityLabelMap, entityIdAttrLabelMap, project, importLog);
                } catch (Exception e) {
                    allErrors.add("保存时出错");
                } finally {
                    if (!allErrors.isEmpty()) {
                        syncErrorLog(importLog, allErrors);
                    } else {
                        // 更新 ”导入记录“ 的状态
                        importLog.setStatus(ImportStatusEnum.finish.getValue());
                        importLog.setFinishTime(new Date());
                    }
                }
            }
            importLogService.saveOrUpdate(importLog);
        }
    }


    private void validEntityAttrPreAnnoDTO(List<EntityAttrPreAnnoDTO> list, Map<String, Long> entityLabelMap, Map<Long, Map<String, Long>> entityIdAttrLabelMap, Project project, ImportLog importLog, Set<String> allErrors) {
        int index = 1;
        for (EntityAttrPreAnnoDTO dto : list) {
            // 判断article_id是否合法
            if (StrUtil.isBlank(dto.getArticleId())) {
                allErrors.add(StrUtil.format("第 {} 条数据，article_id不能为空", index));
            } else {
                // 查询article_id对应的note在此项目中是否存在
                Note note = noteService.findFirstByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());
                if (note == null) {
                    allErrors.add(StrUtil.format("第 {} 条数据，article_id在项目中不存在", dto.getArticleId()));
                } else {
                    // 查出正文
                    Document document = documentService.getDocumentById(note.getDocumentId());
                    // 将正文中所有id字段的值找出来
                    List<String> ids = new ArrayList<>();
                    flatAllIds(document, ids);

                    List<EntityAttrPreAnnoDTO.Info> entities = dto.getEntities();
                    if (CollUtil.isEmpty(entities)) {
                        // 跳过
                        continue;
                    } else {
                        int j = 1;
                        for (EntityAttrPreAnnoDTO.Info entity : entities) {
                            // 如果上传的数预标注实体，就必须得有实体标签
                            if (importLog.getType().equals(ImportTypeEnum.pre_anno_entity.getValue())) {
                                if (!entityLabelMap.containsKey(entity.getLabel())) {
                                    allErrors.add(StrUtil.format("第 {} 条数据下entities第 {} 条，实体标签（label） {} 在此项目中不存在", index, j, entity.getLabel()));
                                } else {
                                    int k = 1;
                                    Long entityLabelId = entityLabelMap.get(entity.getLabel());
                                    Map<String, Long> attrLabelMap = entityIdAttrLabelMap.get(entityLabelId);
                                    for (EntityAttrPreAnnoDTO.AttrInfo attrInfo : entity.getAttribute()) {
                                        String attrName = attrInfo.getAttrName();
                                        if (!attrLabelMap.containsKey(attrName)) {
                                            allErrors.add(StrUtil.format("第 {} 条数据下entities第 {} 条下attribute第 {} 条，关系标签（attr_name） {} 在此项目中不存在", index, j, k, attrName));
                                        }
                                        for (EntityAttrPreAnnoDTO.Attribute attr : attrInfo.getAttrs()) {
                                            if (attr.getEntity() == null && attr.getContent() == null) {
                                                allErrors.add(StrUtil.format("第 {} 条数据下entities第 {} 条下attribute第 {} 条，attrs填写错误", index, j, k));
                                            }
                                            if (attr.getEntity() != null) {
                                                for (AnnoDTO.EntityInfo entityInfo : attr.getEntity()) {
                                                    String textId = entityInfo.getTextId();
                                                    if (!ids.contains(textId)) {
                                                        // ai预计标注的id是带有body-的，如果文章是上传导入了，id是没有body-的，如果是bmfs的，id是带有body-的
                                                        String newTextId = StrUtil.replace(textId, "body-", "");
                                                        if (!ids.contains(newTextId)) {
                                                            allErrors.add(StrUtil.format("第 {} 条数据下entities第 {} 条下attribute第 {} 条， text_id 在此项目中不存在", index, j, k, textId));
                                                        } else {
                                                            entityInfo.setTextId(newTextId);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        k++;
                                    }
                                }
                            }
                            // 校验text_id需要在全文信息中存在
                            for (AnnoDTO.EntityInfo entityInfo : entity.getEntity()) {
                                String textId = entityInfo.getTextId();
                                if (!ids.contains(textId)) {
                                    // ai预计标注的id是带有body-的，如果文章是上传导入了，id是没有body-的，如果是bmfs的，id是带有body-的
                                    String newTextId = StrUtil.replace(textId, "body-", "");
                                    if (!ids.contains(newTextId)) {
                                        allErrors.add(StrUtil.format("第 {} 条数据下entities第 {} 条， text_id 在此项目中不存在", index, j, textId));
                                    } else {
                                        entityInfo.setTextId(newTextId);
                                    }
                                }
                            }
                            j++;
                        }
                    }
                }
            }
            index++;
        }
    }

    public void saveEntityAttrPreAnno(List<EntityAttrPreAnnoDTO> list, Map<String, Long> entityLabelMap, Map<Long, Map<String, Long>> entityIdAttrLabelMap, Project project, ImportLog importLog) {
        List<Entity> data = new ArrayList<>();
        List<Attributes> attributesData = new ArrayList<>();
        for (EntityAttrPreAnnoDTO dto : list) {
            // 20240103修改，同一个项目下文章可能多次修改
            // Note note = noteService.findFirstByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());
            // 找到需要导入预标注的note
            List<Note> noteList = noteService.findByArticleIdAndProjectId(dto.getArticleId(), project.getProjectId());

            Set<String> md5Set = new HashSet<>();

            for (Note note : noteList) {
                String articleId = note.getArticleId();

                for (EntityAttrPreAnnoDTO.Info info : dto.getEntities()) {
                    Long labelId = null;
                    Map<String, Long> attrLabelMap = new HashMap<>();
                    if (importLog.getType().equals(ImportTypeEnum.pre_anno_entity.getValue())) {
                        labelId = entityLabelMap.get(info.getLabel());
                        attrLabelMap = entityIdAttrLabelMap.get(labelId);
                    }
                    // 获取该实体下的属性标签,20240103修改，循环中设置uuid会出现重复的情况
                    // List<AnnoDTO.EntityInfo> entityInfos = info.getEntity().stream().distinct().peek(x -> {
                    //     x.setUniqueid(IdUtil.simpleUUID());
                    // }).collect(Collectors.toList());
                    // 将entityinfo 设置uuid
                    List<AnnoDTO.EntityInfo> entityInfos = info.getEntity().stream().distinct().map(x -> {
                        AnnoDTO.EntityInfo ei = new AnnoDTO.EntityInfo();
                        BeanUtil.copyProperties(x, ei);
                        ei.setUniqueid(IdUtil.simpleUUID());
                        return ei;
                    }).collect(Collectors.toList());

                    // 不同的标注类型计算的md5的值不同
                    String md5;
                    boolean skipIteration = false;
                    int preSource = PreSourceEnum.CUSTOMIZE.getId();
                    if (importLog.getType().equals(ImportTypeEnum.pre_anno_entity.getValue())) {
                        md5 = NoteServiceImpl.genEntityStrMd5(entityInfos, labelId, AttrAnnoEnum.not_attr.getCode());
                        // 计算出md5查询数据库，如果数据库中存在，说明已经导入过则需要跳过
                        // if (entityRepository.existsByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(md5, note.getNoteId(), articleId, AttrAnnoEnum.not_attr.getCode(), preSource)) {
                        //     skipIteration = true;
                        // }
                    } else {
                        md5 = NoteServiceImpl.genEntityStrMd5(entityInfos, null, AttrAnnoEnum.is_attr.getCode());
                        // 计算出md5查询数据库，如果数据库中存在，说明已经导入过则需要跳过
                        // if (entityRepository.existsByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(md5, note.getNoteId(), articleId, AttrAnnoEnum.is_attr.getCode(), preSource)) {
                        //     skipIteration = true;
                        // }
                    }
                    if (md5Set.contains(md5)) {
                        skipIteration = true;
                    } else {
                        md5Set.add(md5);
                    }
                    if (skipIteration) {
                        continue;
                    }
                    Long noteId = note.getNoteId();
                    Long projectId = note.getProjectId();
                    Long batchId = note.getBatchId();
                    Entity entity = new Entity();
                    String entityId = IdUtil.simpleUUID();
                    entity.setId(entityId);
                    entity.setNoteId(noteId);
                    entity.setProjectId(projectId);
                    entity.setBatchId(batchId);
                    entity.setArticleId(articleId);

                    // 如果导入的是实体与标注则需指定labelId，annotate
                    if (importLog.getType().equals(ImportTypeEnum.pre_anno_entity.getValue())) {
                        entity.setLabelId(labelId);
                        entity.setAnnotate(info.getAnnotation());
                        entity.setIsAttr(AttrAnnoEnum.not_attr.getCode());
                    } else {
                        entity.setIsAttr(AttrAnnoEnum.is_attr.getCode());
                    }

                    entity.setEntityInfos(entityInfos);
                    entity.setMd5(md5);
                    entity.setSource(preSource);
                    entity.setImportLogId(importLog.getId());
                    Date currentDate = new Date();
                    entity.setCreateTime(currentDate);
                    entity.setUpdateTime(currentDate);
                    entity.setDeleted(false);
                    if (importLog.getType().equals(ImportTypeEnum.pre_anno_entity.getValue())) {
                        for (EntityAttrPreAnnoDTO.AttrInfo attribute : info.getAttribute()) {
                            // 获取属性标签
                            Long attrLabelId = attrLabelMap.get(attribute.getAttrName());
                            for (EntityAttrPreAnnoDTO.Attribute attr : attribute.getAttrs()) {
                                Attributes attributes = new Attributes();
                                attributes.setId(IdUtil.simpleUUID());
                                attributes.setProjectId(projectId);
                                attributes.setBatchId(batchId);
                                attributes.setArticleId(articleId);
                                attributes.setEntityId(entityId);
                                attributes.setAttrLabelId(attrLabelId);
                                attributes.setCreateTime(currentDate);
                                attributes.setUpdateTime(currentDate);
                                attributes.setEntityMd5(md5);
                                // 如果是划词属性 就要新增一个划词实体 放在m_entity中
                                if (CollUtil.isNotEmpty(attr.getEntity())) {
                                    String attributesId = IdUtil.simpleUUID();

                                    Entity attrEntity = new Entity();
                                    attrEntity.setId(attributesId);
                                    attrEntity.setNoteId(noteId);
                                    attrEntity.setProjectId(projectId);
                                    attrEntity.setBatchId(batchId);
                                    attrEntity.setArticleId(articleId);
                                    List<AnnoDTO.EntityInfo> collect = attr.getEntity().stream().distinct().peek(x -> x.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                                    String attrEntityMd5 = NoteServiceImpl.genEntityStrMd5(collect, null, AttrAnnoEnum.is_attr.getCode());
                                    attrEntity.setMd5(attrEntityMd5);

                                    attributes.setAttrMd5(attrEntityMd5);

                                    attrEntity.setEntityInfos(collect);
                                    attrEntity.setSource(preSource);
                                    attrEntity.setIsAttr(AttrAnnoEnum.is_attr.getCode());
                                    attrEntity.setImportLogId(importLog.getId());
                                    attrEntity.setCreateTime(currentDate);
                                    attrEntity.setUpdateTime(currentDate);
                                    data.add(attrEntity);
                                    attributes.setAttributeId(attributesId);
                                    attributes.setContent(collect.stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")));
                                } else {
                                    attributes.setContent(attr.getContent());
                                    attributes.setAttrMd5(SecureUtil.md5(StrUtil.trimToEmpty(attr.getContent())));
                                }
                                attributesData.add(attributes);

                            }
                        }
                    }

                    data.add(entity);
                }
            }
        }
        entityRepository.saveAll(data);
        attributeRepository.saveAll(attributesData);
    }


    void flatAllIds(Object obj, List<String> ids) {
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不是标注文本
                if (BATCH_IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (CONTENT.equals(name)) {
                    ids.add((String) ReflectUtil.getFieldValue(currentObj, "id"));
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
    }

    @CacheEvict(cacheNames = "existSource_cache", allEntries = true)
    public void deletePreAnno(String type, Long id) {
        Integer typeId = null;
        if ("entity".equals(type)) {
            typeId = ImportTypeEnum.pre_anno_entity.getValue();
        } else if ("attr".equals(type)) {
            typeId = ImportTypeEnum.pre_anno_attr.getValue();
        } else {
            typeId = ImportTypeEnum.pre_anno_relation.getValue();
        }

        Boolean exist = importLogService.existByIdAndType(id, typeId);
        if (!exist) {
            throw new RRException("没有找到预标注导入记录");
        }
        // 删除数据
        entityRepository.deleteByImportLogId(id);
        attributeRepository.deleteByImportLogId(id);
        relationshipRepository.deleteByImportLogId(id);
        importLogService.removeById(id);
    }


}
