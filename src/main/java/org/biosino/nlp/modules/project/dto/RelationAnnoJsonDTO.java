package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Data
public class RelationAnnoJsonDTO implements Serializable {

    private static final long serialVersionUID = 3881139370025526808L;

    @JSONField(ordinal = 1)
    private String articleId;

    @JSONField(ordinal = 2)
    private String pattern;

    @JSONField(ordinal = 3)
    private List<RelationItem> items;

    @Data
    public static class RelationItem implements Serializable {

        private static final long serialVersionUID = -5359425803926258776L;

        @JSONField(ordinal = 1)
        private Integer order;
        @JSONField(name = "same_text_id", ordinal = 2)
        private Boolean sameTextId;
        @JSONField(ordinal = 3)
        private List<EntityAnnoJsonDTO.Info> subject;
        @JSONField(ordinal = 4)
        private String subjectForeign;
        @JSONField(ordinal = 5)
        private Boolean negation;
        @JSONField(ordinal = 6)
        private List<String> annotations;
        @JSONField(ordinal = 7)
        private String relation;
        @JSONField(ordinal = 8)
        private List<EntityAnnoJsonDTO.Info> objects;
        @JSONField(ordinal = 9)
        private String objectsForeign;

    }

}
