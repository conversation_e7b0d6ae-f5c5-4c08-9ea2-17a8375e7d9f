package org.biosino.nlp.modules.project.controller;

import cn.hutool.core.lang.Pair;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.project.dto.AIPreAnnoTaskDTO;
import org.biosino.nlp.modules.project.dto.AIPreAnnoTaskQueryDTO;
import org.biosino.nlp.modules.project.dto.EntityAnnoCleanJsonDTO;
import org.biosino.nlp.modules.project.service.AIPreAnnoTaskService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * AI预标注任务控制器
 *
 * @date 2023/11/28
 */
@RestController
@RequestMapping("/aiPreAnnoTask")
public class AIPreAnnoTaskController extends AbstractController {

    @Autowired
    private AIPreAnnoTaskService aiPreAnnoTaskService;

    @Autowired
    private EntityLabelService entityLabelService;

    /**
     * 创建AI预标注任务
     *
     * @param dto 任务参数
     * @return 任务ID
     */
    @SysLog("创建AI预标注任务")
    @RequestMapping("/create")
    public R create(@RequestBody AIPreAnnoTaskDTO dto) {
        Long taskId = aiPreAnnoTaskService.createTask(dto, getUserId());
        return R.ok().put("id", taskId);
    }

    /**
     * 分页查询AI预标注任务
     *
     * @param dto 查询参数
     * @return 分页数据
     */
    @RequestMapping("/list")
    public R list(AIPreAnnoTaskQueryDTO dto) {
        PageUtils page = aiPreAnnoTaskService.queryPage(dto, getUserId());
        return R.ok().put("page", page);
    }

    /**
     * 获取金标准得标注信息
     */
    @RequestMapping("/getCleanEntityAnnoData")
    public R getCleanEntityAnnoData(@RequestBody AIPreAnnoTaskDTO dto) {
        // 查询勾选的金标准数据的标注信息
        List<EntityAnnoCleanJsonDTO> result = aiPreAnnoTaskService.getCleanEntityAnnoData(dto);
        // 查询batch下标注数据实体信息
        List<Pair<String, Set<String>>> entityToSample = aiPreAnnoTaskService.getLabelToEntitiesInfo(dto);
        List<EntityLabel> entityLabels = entityLabelService.findAllEnableByProjectId(dto.getProjectId());
        return R.success(result).put("entityToSample", entityToSample).put("entityLabels", entityLabels);
    }

    /**
     * 下载结果或者错误日志
     * @param id 任务ID
     * @param type 下载类型，json或excel，默认为json
     */
    @RequestMapping("/download")
    public ResponseEntity<Resource> download(Long id, @RequestParam(required = false, defaultValue = "json") String type) {
        return aiPreAnnoTaskService.download(id, type);
    }

    /**
     * 应用ai预标注结果
     */
    @SysLog("应用AI预标注结果")
    @RequestMapping("/applyPreAnno")
    public R applyPreAnno(Long id) {
        aiPreAnnoTaskService.applyPreAnno(id);
        return R.ok();
    }

    /**
     * 暂停AI预标注任务
     */
    @SysLog("暂停AI预标注任务")
    @RequestMapping("/pause")
    public R pauseTask(Long id) {
        aiPreAnnoTaskService.pauseTask(id);
        return R.ok("任务已暂停");
    }

    /**
     * 恢复AI预标注任务
     */
    @SysLog("恢复AI预标注任务")
    @RequestMapping("/resume")
    public R resumeTask(Long id) {
        aiPreAnnoTaskService.resumeTask(id);
        return R.ok("任务已恢复");
    }

    /**
     * 计算token
     */
    @RequestMapping("/countTiktoken")
    public R countTiktoken(@RequestBody String body) {
        Integer tokenNum = aiPreAnnoTaskService.countTiktoken(body);
        return R.success(tokenNum);
    }
}
