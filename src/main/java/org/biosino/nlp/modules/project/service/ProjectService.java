package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.project.dto.ProjectDTO;
import org.biosino.nlp.modules.project.entity.Project;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
public interface ProjectService extends IService<Project> {

    PageUtils queryPage(ProjectDTO projectDTO, Long userId);

    void validateProjectName(String projectName, Long projectId, Long userId);

    void saveOrUpdate(Project project, Long userId);

    /**
     * 查询用户参与的启用的项目
     */
    List<Project> participateProject(Long userId, Long roleId);

    List<EntityLabel> queryLabels(Long projectId);

    Project findByToken(String token);

    /**
     * 重置项目token
     */
    void resetToken(Long projectId, Long userId);

    Map<Long, String> findAllProject(Long userId);

}

