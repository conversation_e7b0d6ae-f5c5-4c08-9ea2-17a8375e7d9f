package org.biosino.nlp.modules.project.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * AI预标注任务表
 *
 * @date 2023/11/28
 */
@Data
@TableName("t_ai_pre_anno_task")
public class AIPreAnnoTask implements Serializable {

    @TableId
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 金标准批次ID
     */
    private Long sourceBatchId;

    private String sourceArticleIdsStr;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务状态: 0-运行中, 1-成功, 2-失败
     */
    private Integer status;

    /**
     * 用户输入的提示词
     */
    private String prompt;

    private String aiModel;

    private String endpoint;

    private String apiKey;

    private Integer chunkSize;

    /**
     * 待标注文献批次ID
     */
    private Long batchId;

    private String articleIdsStr;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建人ID
     */
    private Long creator;

    /**
     * Prompt消耗的token数
     */
    private Long promptTokens;

    /**
     * Completion消耗的token数
     */
    private Long completionTokens;

    /**
     * 总token消耗数
     */
    private Long totalTokens;

    @TableLogic
    private Integer deleted;

    /**
     * 金标准文献ID列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<String> sourceArticleIds;

    /**
     * 待标注文献ID列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<String> articleIds;


    public void setSourceArticleIdsStr(String sourceArticleIdsStr) {
        if (StrUtil.isNotBlank(sourceArticleIdsStr) && CollUtil.isEmpty(this.sourceArticleIds)) {
            this.sourceArticleIds = JSON.parseArray(sourceArticleIdsStr, String.class);
        }
        this.sourceArticleIdsStr = sourceArticleIdsStr;
    }

    public void setArticleIdsStr(String articleIdsStr) {
        if (StrUtil.isNotBlank(articleIdsStr) && CollUtil.isEmpty(this.articleIds)) {
            this.articleIds = JSON.parseArray(articleIdsStr, String.class);
        }
        this.articleIdsStr = articleIdsStr;
    }

    public void setSourceArticleIds(List<String> sourceArticleIds) {
        if (CollUtil.isNotEmpty(sourceArticleIds) && StrUtil.isBlank(this.sourceArticleIdsStr)) {
            this.sourceArticleIdsStr = JSON.toJSONString(sourceArticleIds);
        }
        this.sourceArticleIds = sourceArticleIds;
    }

    public void setArticleIds(List<String> articleIds) {
        if (CollUtil.isNotEmpty(articleIds) && StrUtil.isBlank(this.articleIdsStr)) {
            this.articleIdsStr = JSON.toJSONString(articleIds);
        }
        this.articleIds = articleIds;
    }
}
