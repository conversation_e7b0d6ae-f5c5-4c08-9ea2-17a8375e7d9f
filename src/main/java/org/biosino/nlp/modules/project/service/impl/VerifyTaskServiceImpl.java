package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import org.biosino.nlp.common.enums.AttrAnnoEnum;
import org.biosino.nlp.common.enums.ImportStatusEnum;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.JsonValidator;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.biosino.nlp.modules.project.dao.BatchDao;
import org.biosino.nlp.modules.project.dao.VerifyLabelDao;
import org.biosino.nlp.modules.project.dao.VerifyTaskDao;
import org.biosino.nlp.modules.project.dto.EntityAttrPreAnnoDTO;
import org.biosino.nlp.modules.project.dto.VerifyTaskDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.entity.VerifyLabel;
import org.biosino.nlp.modules.project.entity.VerifyTask;
import org.biosino.nlp.modules.project.service.ImportLogService;
import org.biosino.nlp.modules.project.service.VerifyTaskService;
import org.biosino.nlp.modules.project.vo.VerifyTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.biosino.nlp.common.utils.BnlpUtils.BATCH_IGNORE_FIELDS;
import static org.biosino.nlp.common.utils.BnlpUtils.CONTENT;
import static org.biosino.nlp.common.utils.ShiroUtils.getUserId;

/**
 * 数据验证任务
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Service
public class VerifyTaskServiceImpl extends ServiceImpl<VerifyTaskDao, VerifyTask> implements VerifyTaskService {

    @Autowired
    private VerifyTaskDao verifyTaskDao;
    @Autowired
    private VerifyLabelDao verifyLabelDao;
    @Autowired
    private NoteService noteService;
    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private BatchDao batchDao;
    @Autowired
    private EntityLabelService entityLabelService;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private ImportLogService importLogService;

    @Override
    public PageUtils queryPage(VerifyTaskDTO dto) {
        if (dto == null || dto.getProjectId() == null) {
            throw new RRException("请选择项目");
        }

        IPage<VerifyTask> page = this.page(new Query<VerifyTask>().getPage(dto), Wrappers.<VerifyTask>lambdaQuery()
                .eq(VerifyTask::getProjectId, dto.getProjectId())
                .like(StrUtil.isNotBlank(dto.getTaskName()), VerifyTask::getTaskName, dto.getTaskName())
                .eq(dto.getStatus() != null, VerifyTask::getStatus, dto.getStatus())
                .ge(dto.getStartDate() != null, VerifyTask::getCreateTime, dto.getStartDate())
                .le(dto.getEndDate() != null, VerifyTask::getCreateTime, DateUtil.format(dto.getEndDate(), "yyyy-MM-dd 23:59:59"))
                .orderByDesc(VerifyTask::getCreateTime));

        List<VerifyTaskVO> result = new ArrayList<>();

        List<VerifyTask> verifyTaskList = page.getRecords();
        for (VerifyTask verifyTask : verifyTaskList) {
            VerifyTaskVO vo = new VerifyTaskVO();

            BeanUtil.copyProperties(verifyTask, vo);

            if (verifyTask.getBatchId() != null) {
                Batch batch = batchDao.selectById(verifyTask.getBatchId());
                if (batch != null) {
                    vo.setBatchName(batch.getName());
                }
            }

            if (verifyTask.getPreSourceId() != null) {
                ImportLog importLog = importLogService.findById(verifyTask.getPreSourceId());
                if (importLog != null) {
                    vo.setPreSourceName(StrUtil.replace(importLog.getName(), ".json", ""));
                }
            }

            result.add(vo);
        }

        return new PageUtils(page, result);
    }

    @Override
    public void saveOrUpdate(VerifyTask task, Long creatorId) {
        if (task == null || task.getProjectId() == null) {
            throw new RRException("请先选择项目");
        }

        int hasTask = this.count(Wrappers.<VerifyTask>lambdaQuery().eq(VerifyTask::getTaskName, task.getTaskName())
                .ne(task.getId() != null, VerifyTask::getId, task.getId()).eq(VerifyTask::getProjectId, task.getProjectId()));

        if (hasTask != 0) {
            throw new RRException("任务已存在");
        }

        Batch batch = batchDao.selectById(task.getBatchId());
        if (batch == null) {
            throw new RRException("批次未找到");
        }

        Date currentDate = new Date();
        task.setUpdateTime(currentDate);

        // 编辑
        if (task.getId() != null) {
            VerifyTask oldTask = this.getById(task.getId());
            oldTask.setTaskName(task.getTaskName());
            oldTask.setBatchId(task.getBatchId());
            oldTask.setDescription(task.getDescription());
            this.saveOrUpdate(oldTask);
        } else {
            // 新增
            task.setCreator(getUserId());
            task.setCreateTime(currentDate);
            task.setStatus(ImportStatusEnum.processing.getValue());
            this.saveOrUpdate(task);

            // 清空历史统计的数据
            LambdaQueryWrapper<VerifyLabel> verifyLabelWrapper = Wrappers.<VerifyLabel>lambdaQuery()
                    .eq(VerifyLabel::getVerifyTaskId, task.getId());
            verifyLabelDao.delete(verifyLabelWrapper);

            // 校验数据并导入
            ThreadUtil.execAsync(() -> {
                try {
                    // 计算
                    if (task.getMethod().equals(2)) {
                        // 基于文本计算
                        calculationByText(batch, task);
                    } else {
                        // 基于位置计算
                        calculation(batch, task);
                    }
                    task.setStatus(ImportStatusEnum.finish.getValue());
                } catch (Exception e) {
                    log.error("数据验证任务计算失败", e);
                    task.setStatus(ImportStatusEnum.fail.getValue());
                } finally {
                    task.setFinishTime(new Date());
                    this.saveOrUpdate(task);
                }
            });
        }
    }

    @Override
    public VerifyTask getTaskById(Long taskId) {
        return verifyTaskDao.selectById(taskId);
    }

    @Override
    public void deleteById(Long taskId) {
        verifyTaskDao.deleteById(taskId);

        LambdaQueryWrapper<VerifyLabel> masterTaskWrapper = Wrappers.<VerifyLabel>lambdaQuery()
                .eq(VerifyLabel::getVerifyTaskId, taskId);
        verifyLabelDao.delete(masterTaskWrapper);
    }

    @Override
    public List<VerifyLabel> queryLabel(Long verifyTaskId, Integer type) {

        List<VerifyLabel> verifyLabelList = verifyLabelDao.selectList(Wrappers.<VerifyLabel>lambdaQuery()
                .eq(VerifyLabel::getVerifyTaskId, verifyTaskId)
                .eq(type != null && type != 0, VerifyLabel::getType, type));

        if (CollUtil.isEmpty(verifyLabelList)) {
            return null;
        }

        for (VerifyLabel verifyLabel : verifyLabelList) {
            statistics(verifyLabel);
        }

        VerifyLabel vo = new VerifyLabel();
        vo.setEntityTotal(verifyLabelList.stream().mapToLong(VerifyLabel::getEntityTotal).sum());
        vo.setNeedTotal(verifyLabelList.stream().mapToLong(VerifyLabel::getNeedTotal).sum());
        vo.setCorrectTotal(verifyLabelList.stream().mapToLong(VerifyLabel::getCorrectTotal).sum());
        vo.setErrorTotal(verifyLabelList.stream().mapToLong(VerifyLabel::getErrorTotal).sum());
        vo.setMissTotal(verifyLabelList.stream().mapToLong(VerifyLabel::getMissTotal).sum());

        statistics(vo);

        vo.setLabelName("合计");

        verifyLabelList.add(vo);

        return verifyLabelList;
    }

    private void statistics(VerifyLabel vo) {
        // 准确率
        int n = (int) (vo.getCorrectTotal() + vo.getErrorTotal() + vo.getMissTotal());
        if (n != 0) {
            vo.setCorrectRate(NumberUtil.div(vo.getCorrectTotal().intValue(), n, 4) * 100);
        }
        // 精确率
        int p = vo.getCorrectTotal().intValue() + vo.getErrorTotal().intValue();
        if (p != 0) {
            vo.setPrecision(NumberUtil.div(vo.getCorrectTotal().intValue(), p, 4) * 100);
        }

        // 召回率
        int r = vo.getCorrectTotal().intValue() + vo.getMissTotal().intValue();
        if (r != 0) {
            vo.setRecall(NumberUtil.div(vo.getCorrectTotal().intValue(), r, 4) * 100);
        }

        // f1值
        if (vo.getPrecision() != null && vo.getRecall() != null && vo.getPrecision() + vo.getRecall() != 0d) {
            vo.setF1Score(NumberUtil.div(2 * vo.getPrecision() * vo.getRecall(), vo.getPrecision() + vo.getRecall(), 4));
        }
    }

    /**
     * 已废弃
     */
    @Override
    @SneakyThrows
    public void uploadData(MultipartFile multipartFile, Long taskId) {
        // 判断文件内容是否合法的JSON字符串
        String content = StrUtil.utf8Str(multipartFile.getBytes());
        if (!JsonValidator.isJsonValid(content)) {
            throw new RRException("文件内容不是合法的JSON字符串");
        }

        VerifyTask verifyTask = verifyTaskDao.selectById(taskId);
        if (verifyTask == null) {
            throw new RRException("任务未找到");
        }

        Batch batch = batchDao.selectById(verifyTask.getBatchId());
        if (batch == null) {
            throw new RRException("批次未找到");
        }

        // 导入实体预标注数据， 生成导入记录
        verifyTask.setStatus(ImportStatusEnum.processing.getValue());

        // 将上传的文件落盘
        File homeDir = Constant.getHomeDir(Constant.DirectoryEnum.temp);
        String relativePath = DateUtil.date().toString("yyyy-MM-dd") + System.currentTimeMillis() + RandomUtil.randomString(6) + ".json";

        File file = FileUtil.file(homeDir, relativePath);
        FileUtil.mkParentDirs(file);
        FileUtil.touch(file);
        multipartFile.transferTo(file);

    }

    private void validEntityAnnoDTO(List<EntityAttrPreAnnoDTO> list, Map<String, Long> entityLabelMap, Batch batch, Set<String> allErrors) {
        Set<String> articleIdSet = new HashSet<>();
        int index = 0;
        for (EntityAttrPreAnnoDTO dto : list) {
            index++;
            // 判断article_id是否合法
            if (StrUtil.isBlank(dto.getArticleId())) {
                allErrors.add(StrUtil.format("第{}条文章，article_id不能为空", index));
                continue;
            }
            // 判断article_id是否重复
            if (articleIdSet.contains(dto.getArticleId())) {
                allErrors.add(StrUtil.format("第{}条文章，article_id已存在", index));
                continue;
            }
            articleIdSet.add(dto.getArticleId());

            // 查询article_id对应的note在此项目中是否存在
            Note note = noteService.findReviewedByArticleIdAndBatchId(dto.getArticleId(), batch.getBatchId());
            if (note == null) {
                allErrors.add(StrUtil.format("第{}条文章，{}在批次中不存在 或 处于未验收状态", index, dto.getArticleId()));
                continue;
            }
            // 查出正文
            Document document = documentService.getDocumentById(note.getDocumentId());
            // 将正文中所有id字段的值找出来
            List<String> ids = new ArrayList<>();
            flatAllIds(document, ids);

            List<EntityAttrPreAnnoDTO.Info> entities = dto.getEntities();
            if (CollUtil.isEmpty(entities)) {
                allErrors.add(StrUtil.format("第{}条文章，entities 不能为空", index));
                continue;
            }

            for (int j = 1; j <= entities.size(); j++) {
                EntityAttrPreAnnoDTO.Info entity = entities.get(j);

                // 如果上传实体，就必须得有实体标签
                if (!entityLabelMap.containsKey(entity.getLabel())) {
                    allErrors.add(StrUtil.format("第{}条文章entities中第{}条，实体标签【{}】在此项目中不存在", index, j, entity.getLabel()));
                }

                // 校验text_id需要在全文信息中存在
                for (AnnoDTO.EntityInfo entityInfo : entity.getEntity()) {
                    String textId = entityInfo.getTextId();
                    if (!ids.contains(textId)) {
                        allErrors.add(StrUtil.format("第{}条文章entities中第{}条， text_id 在此项目中不存在", index, j, textId));
                    }
                }
            }
        }
    }

    /**
     * 只获取实体的MD5和标签ID
     */
    public List<Entity> getEntityMd5AndLabel(List<EntityAttrPreAnnoDTO> list, Map<String, Long> entityLabelMap) {
        List<Entity> data = new ArrayList<>();

        for (EntityAttrPreAnnoDTO dto : list) {
            for (EntityAttrPreAnnoDTO.Info info : dto.getEntities()) {

                Long labelId = entityLabelMap.get(info.getLabel());

                // 将entity info 设置uuid
                List<AnnoDTO.EntityInfo> entityInfos = info.getEntity().stream().distinct().map(x -> {
                    AnnoDTO.EntityInfo ei = new AnnoDTO.EntityInfo();
                    BeanUtil.copyProperties(x, ei);
                    ei.setUniqueid(IdUtil.simpleUUID());
                    return ei;
                }).collect(Collectors.toList());

                String md5 = NoteServiceImpl.genEntityStrMd5(entityInfos, labelId, AttrAnnoEnum.not_attr.getCode());
                Entity entity = new Entity();

                entity.setLabelId(labelId);
                entity.setMd5(md5);

                data.add(entity);
            }
        }
        return data;
    }

    public void calculation(Batch batch, VerifyTask verifyTask) {
        // 找出所有蓝本的TaskID
        LambdaQueryWrapper<NoteTask> masterTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getBatchId, batch.getBatchId())
                .eq(NoteTask::getMaster, MasterEnum.master.getValue())
                .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode()).select(NoteTask::getTaskId);

        List<NoteTask> tasks = noteTaskDao.selectList(masterTaskWrapper);
        List<Long> masterTaskIds = tasks.stream().map(NoteTask::getTaskId).collect(Collectors.toList());

        // 一级标签ID   实体md5
        Map<String, Set<String>> masterLableMap = new HashMap<>();
        // 属性标签ID   属性md5
        Map<String, Set<String>> masterAttrLableMap = new HashMap<>();

        // 构建所有蓝本的数据
        for (Long masterTaskId : masterTaskIds) {
            List<Entity> masterEntityList = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(masterTaskId, AttrAnnoEnum.not_attr.getCode(), false);
            calculationLabelMap(masterEntityList, masterLableMap, masterAttrLableMap);
        }

        Map<String, Set<String>> preLabelMap = new HashMap<>();
        Map<String, Set<String>> preAttrLabelMap = new HashMap<>();

        Long preSourceId = verifyTask.getPreSourceId();
        List<Entity> preEntityList = entityRepository.findByImportLogId(preSourceId);
        calculationLabelMap(preEntityList, preLabelMap, preAttrLabelMap);

        Map<Long, EntityLabel> entityLabelMap = entityLabelService.findAllEnableByProjectId(batch.getProjectId()).stream().collect(Collectors.toMap(EntityLabel::getId, Function.identity()));

        entityLabelMap.forEach((labelId, entityLabel) -> {
            VerifyLabel verifyLabel = createVerifyLabel(entityLabel.getName(), verifyTask.getId(), labelId, 1, masterLableMap, preLabelMap);
            if (verifyLabel != null) {
                verifyLabelDao.insert(verifyLabel);
            }

            List<AttributeLabel> attributeLabelList = attributeLabelService.findAllByEntityLabelId(labelId);
            if (CollUtil.isNotEmpty(attributeLabelList)) {
                for (AttributeLabel attributeLabel : attributeLabelList) {
                    VerifyLabel verifyAttrLabel = createVerifyLabel(entityLabel.getName() + ":" + attributeLabel.getName(),
                            verifyTask.getId(), attributeLabel.getId(), 2, masterAttrLableMap, preAttrLabelMap);
                    if (verifyAttrLabel != null) {
                        verifyLabelDao.insert(verifyAttrLabel);
                    }
                }
            }
        });
    }

    public void calculationByText(Batch batch, VerifyTask verifyTask) {
        // 找出所有蓝本的TaskID
        LambdaQueryWrapper<NoteTask> masterTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getBatchId, batch.getBatchId())
                .eq(NoteTask::getMaster, MasterEnum.master.getValue())
                .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode()).select(NoteTask::getTaskId);

        List<NoteTask> tasks = noteTaskDao.selectList(masterTaskWrapper);
        List<Long> masterTaskIds = tasks.stream().map(NoteTask::getTaskId).collect(Collectors.toList());

        // 一级标签ID   实体md5
        Map<String, Set<String>> masterLableMap = new HashMap<>();
        // 属性标签ID   属性md5
        Map<String, Set<String>> masterAttrLableMap = new HashMap<>();

        // 构建所有蓝本的数据
        for (Long masterTaskId : masterTaskIds) {
            List<Entity> masterEntityList = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(masterTaskId, AttrAnnoEnum.not_attr.getCode(), false);
            calculationLabelMapByText(masterEntityList, masterLableMap, masterAttrLableMap);
        }

        Map<String, Set<String>> preLabelMap = new HashMap<>();
        Map<String, Set<String>> preAttrLabelMap = new HashMap<>();

        Long preSourceId = verifyTask.getPreSourceId();
        List<Entity> preEntityList = entityRepository.findByImportLogId(preSourceId);
        calculationLabelMapByText(preEntityList, preLabelMap, preAttrLabelMap);

        Map<Long, EntityLabel> entityLabelMap = entityLabelService.findAllEnableByProjectId(batch.getProjectId()).stream().collect(Collectors.toMap(EntityLabel::getId, Function.identity()));

        entityLabelMap.forEach((labelId, entityLabel) -> {
            VerifyLabel verifyLabel = createVerifyLabel(entityLabel.getName(), verifyTask.getId(), labelId, 1, masterLableMap, preLabelMap);
            if (verifyLabel != null) {
                verifyLabelDao.insert(verifyLabel);
            }

            List<AttributeLabel> attributeLabelList = attributeLabelService.findAllByEntityLabelId(labelId);
            if (CollUtil.isNotEmpty(attributeLabelList)) {
                for (AttributeLabel attributeLabel : attributeLabelList) {
                    VerifyLabel verifyAttrLabel = createVerifyLabel(entityLabel.getName() + ":" + attributeLabel.getName(),
                            verifyTask.getId(), attributeLabel.getId(), 2, masterAttrLableMap, preAttrLabelMap);
                    if (verifyAttrLabel != null) {
                        verifyLabelDao.insert(verifyAttrLabel);
                    }
                }
            }
        });
    }

    @Override
    public List<VerifyLabel> queryLabelByOther(Long verifyLabelId) {
        VerifyLabel masterLabel = verifyLabelDao.selectById(verifyLabelId);
        if (masterLabel == null) {
            return null;
        }

        List<VerifyLabel> verifyLabelList = verifyLabelDao.selectList(Wrappers.<VerifyLabel>lambdaQuery()
                .eq(VerifyLabel::getType, masterLabel.getType())
                .eq(VerifyLabel::getLabelId, masterLabel.getLabelId())
                .orderByDesc(VerifyLabel::getId));

        List<VerifyLabel> verifyLabels = getVerifyLabels(verifyLabelList);

        if (CollUtil.isEmpty(verifyLabels)) {
            return null;
        }

        for (VerifyLabel verifyLabel : verifyLabels) {
            Long verifyTaskId = verifyLabel.getVerifyTaskId();
            VerifyTask verifyTask = verifyTaskDao.selectById(verifyTaskId);
            verifyLabel.setTaskName(verifyTask.getTaskName());

            if (Objects.equals(verifyLabelId, verifyLabel.getId())) {
                verifyLabel.setTaskName(verifyLabel.getTaskName() + (" (当前任务)"));
            }
        }
        return verifyLabels;
    }

    private List<VerifyLabel> getVerifyLabels(List<VerifyLabel> verifyLabelList) {


        return verifyLabelList;
    }

    /**
     * @param name           标签名称
     * @param verifyTaskId   任务ID
     * @param labelId        标签ID
     * @param type           1 实体， 2 属性
     * @param masterLableMap 金标准数据
     * @param preLabelMap    预标注数据
     */
    private VerifyLabel createVerifyLabel(String name, Long verifyTaskId, Long labelId, int type, Map<String, Set<String>> masterLableMap, Map<String, Set<String>> preLabelMap) {
        Set<String> masterEntityMd5 = new HashSet<>();
        if (masterLableMap.containsKey(labelId.toString())) {
            masterEntityMd5 = masterLableMap.get(labelId.toString());
        }
        Set<String> preEntityMd5 = new HashSet<>();
        if (preLabelMap.containsKey(labelId.toString())) {
            preEntityMd5 = preLabelMap.get(labelId.toString());
        }
        VerifyLabel verifyLabel = new VerifyLabel();
        verifyLabel.setVerifyTaskId(verifyTaskId);
        verifyLabel.setLabelName(name);
        verifyLabel.setLabelId(labelId);
        verifyLabel.setType(type);
        // 实体数
        verifyLabel.setEntityTotal((long) preEntityMd5.size());
        // 应标实体数
        verifyLabel.setNeedTotal((long) masterEntityMd5.size());
        // 标对数TP
        if (CollUtil.isNotEmpty(masterEntityMd5)) {
            verifyLabel.setCorrectTotal((long) CollUtil.intersectionDistinct(preEntityMd5, masterEntityMd5).size());
        }
        // 标错数FP
        if (CollUtil.isNotEmpty(preEntityMd5)) {
            verifyLabel.setErrorTotal((long) CollUtil.subtract(preEntityMd5, masterEntityMd5).size());
        }
        // 漏标数FN
        if (CollUtil.isNotEmpty(masterEntityMd5)) {
            verifyLabel.setMissTotal((long) CollUtil.subtract(masterEntityMd5, preEntityMd5).size());
        }

        // 剔除掉无用数据
        if (verifyLabel.getNeedTotal() == 0 && verifyLabel.getCorrectTotal() == 0 && verifyLabel.getErrorTotal() == 0 && verifyLabel.getMissTotal() == 0) {
            return null;
        }
        return verifyLabel;
    }

    private void calculationLabelMap(List<Entity> entityList, Map<String, Set<String>> labelMap, Map<String, Set<String>> attrLabelMap) {
        if (CollUtil.isEmpty(entityList)) {
            return;
        }
        for (Entity entity : entityList) {
            String entityId = entity.getId();
            Long labelId = entity.getLabelId();

            if (labelId == null) {
                continue;
            }

            String md5 = entity.getNoteId() + "_" + entity.getMd5();
            String entityKey = labelId.toString();

            if (labelMap.containsKey(entityKey)) {
                Set<String> set = labelMap.get(entityKey);
                set.add(md5);
                labelMap.put(entityKey, set);
            } else {
                labelMap.put(entityKey, CollUtil.newHashSet(md5));
            }

            List<Attributes> attributesList = attributesRepository.findByEntityId(entityId);

            if (CollUtil.isNotEmpty(attributesList)) {

                for (Attributes attributes : attributesList) {
                    Long attrLabelId = attributes.getAttrLabelId();
                    String attrMd5 = entity.getNoteId() + "_" + attributes.getAttrMd5();
                    String attrKey = attrLabelId.toString();

                    if (attrLabelMap.containsKey(attrKey)) {
                        Set<String> set = attrLabelMap.get(attrKey);
                        set.add(attrMd5);
                        attrLabelMap.put(attrKey, set);
                    } else {
                        attrLabelMap.put(attrKey, CollUtil.newHashSet(attrMd5));
                    }
                }
            }

        }
    }

    private void calculationLabelMapByText(List<Entity> entityList, Map<String, Set<String>> labelMap, Map<String, Set<String>> attrLabelMap) {
        if (CollUtil.isEmpty(entityList)) {
            return;
        }
        Map<String, Integer> entityMap = new HashMap<>();
        Map<String, Integer> attrMap = new HashMap<>();
        for (Entity entity : entityList) {
            String entityId = entity.getId();
            Long labelId = entity.getLabelId();

            if (labelId == null) {
                continue;
            }

            String entityKey = labelId.toString();
            String content = entity.getNoteId() + "_" + entityKey + "_" + getContent(entity);

            if (labelMap.containsKey(entityKey)) {
                int count = 1;
                if (entityMap.containsKey(content)) {
                    count = entityMap.get(content) + 1;
                }
                entityMap.put(content, count);
                content += "_" + count;
                Set<String> set = labelMap.get(entityKey);
                set.add(content);
                labelMap.put(entityKey, set);
            } else {
                entityMap.put(content, 1);
                labelMap.put(entityKey, CollUtil.newHashSet(content + "_1"));
            }

            List<Attributes> attributesList = attributesRepository.findByEntityId(entityId);

            if (CollUtil.isNotEmpty(attributesList)) {

                for (Attributes attributes : attributesList) {
                    Long attrLabelId = attributes.getAttrLabelId();
                    String attrContent = entity.getNoteId() + "_" + attrLabelId + "_" + attributes.getContent();
                    String attrKey = attrLabelId.toString();

                    if (attrLabelMap.containsKey(attrKey)) {
                        int count = 1;
                        if (attrMap.containsKey(attrContent)) {
                            count = attrMap.get(attrContent) + 1;
                        }
                        attrMap.put(attrContent, count);
                        attrContent += "_" + count;
                        Set<String> set = attrLabelMap.get(attrKey);
                        set.add(attrContent);
                        attrLabelMap.put(attrKey, set);
                    } else {
                        attrMap.put(attrContent, 1);
                        attrLabelMap.put(attrKey, CollUtil.newHashSet(attrContent + "_1"));
                    }
                }
            }

        }
    }

    private String getContent(Entity entity) {
        List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();

        StringBuilder content = new StringBuilder();
        for (AnnoDTO.EntityInfo entityInfo : entityInfos) {
            content.append(entityInfo.getContent());
        }
        return content.toString();
    }

    void flatAllIds(Object obj, List<String> ids) {
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不是标注文本
                if (BATCH_IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (CONTENT.equals(name)) {
                    ids.add((String) ReflectUtil.getFieldValue(currentObj, "id"));
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
    }

    @Override
    public List<ImportLog> getPreSourceList(Long projectId) {
        List<ImportLog> preSourceList = importLogService.getPreSourceList(projectId);
        if (preSourceList == null) {
            return null;
        }
        for (ImportLog importLog : preSourceList) {
            importLog.setName(StrUtil.replace(importLog.getName(), ".json", ""));
        }
        return preSourceList;
    }

}
