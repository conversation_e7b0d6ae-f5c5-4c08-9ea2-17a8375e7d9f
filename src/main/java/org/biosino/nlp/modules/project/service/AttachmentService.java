package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.modules.project.entity.Attachment;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021-01-09 17:01
 */
public interface AttachmentService extends IService<Attachment> {

    File DIR = Constant.getHomeDir(Constant.DirectoryEnum.project_pdf);

    Attachment upload(MultipartFile file) throws IOException;

    void deleteFile(Long docId);

}
