package org.biosino.nlp.modules.project.vo;

import java.util.*;

public class FleissKappaMatrix {

    public static double[][] convertToMatrix(Map<String, Map<String, Integer>> kMatrix, int annotatorCount) {
        // 1. 获取所有列的并集，添加"未知"列
        Set<String> allColumns = new HashSet<>();
        for (Map<String, Integer> innerMap : kMatrix.values()) {
            allColumns.addAll(innerMap.keySet());
        }
        allColumns.add("未知"); // 添加未知列

        // 2. 构建列名与索引的映射
        List<String> columnList = new ArrayList<>(allColumns);
        int colCount = columnList.size();
        Map<String, Integer> colIndexMap = new HashMap<>();
        for (int i = 0; i < colCount; i++) {
            colIndexMap.put(columnList.get(i), i);
        }

        // 3. 构建结果矩阵，矩阵行数等于外层Map的size，列数等于所有列的并集
        int rowCount = kMatrix.size();
        double[][] resultMatrix = new double[rowCount][colCount];

        // 4. 填充矩阵，同时处理标注结果数超出annotatorCount的情况
        int rowIndex = 0;
        for (Map.Entry<String, Map<String, Integer>> outerEntry : kMatrix.entrySet()) {
            Map<String, Integer> innerMap = outerEntry.getValue();
            double sum = 0;

            // 计算当前行的标注结果总数
            for (int count : innerMap.values()) {
                sum += count;
            }

            // 如果标注结果数超过了annotatorCount，按比例缩放
            double scale = sum > annotatorCount ? annotatorCount / sum : 1.0;

            // 填充当前行的矩阵
            for (Map.Entry<String, Integer> innerEntry : innerMap.entrySet()) {
                String key = innerEntry.getKey();
                int colIndex = colIndexMap.get(key);
                resultMatrix[rowIndex][colIndex] = innerEntry.getValue() * scale;
            }

            // 如果某些列没有标注结果，设置"未知"列的值
            if (sum < annotatorCount) {
                int unknownColIndex = colIndexMap.get("未知");
                resultMatrix[rowIndex][unknownColIndex] = annotatorCount - sum;
            }

            rowIndex++;
        }

        return resultMatrix;
    }

    public static void main(String[] args) {
        // 模拟数据
        Map<String, Map<String, Integer>> kMatrix = new HashMap<>();

        Map<String, Integer> row1 = new HashMap<>();
        row1.put("A", 3);
        row1.put("B", 5);
        kMatrix.put("8492", row1);

        Map<String, Integer> row2 = new HashMap<>();
        row2.put("A", 7);
        row2.put("C", 6);
        kMatrix.put("8493", row2);

        // 假设有10个标注员
        int annotatorCount = 10;

        // 转换为二维矩阵
        double[][] resultMatrix = convertToMatrix(kMatrix, annotatorCount);

        // 打印结果矩阵
        for (double[] row : resultMatrix) {
            System.out.println(Arrays.toString(row));
        }
    }
}
