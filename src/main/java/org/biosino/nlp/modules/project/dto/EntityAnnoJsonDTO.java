package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Data
public class EntityAnnoJsonDTO implements Serializable {

    private static final long serialVersionUID = 7970456296975573200L;

    @JSONField(name = "article_id", ordinal = 1)
    private String articleId;

    @JSONField(ordinal = 2)
    private List<Info> entities;

    @Data
    public static class Info implements Serializable {

        private static final long serialVersionUID = -8903266159953418953L;

        @JSONField(ordinal = 0)
        private String id;

        @JSONField(ordinal = 1)
        private Long labelId;

        @JSONField(ordinal = 2)
        private String label;

        @JSONField(ordinal = 3)
        private List<EntityInfo> entity;

        @JSONField(ordinal = 4)
        private List<AttrInfo> attribute;

        @JSONField(ordinal = 5)
        private String annotation;

        @JSONField(ordinal = 6)
        private String conceptId;

        @JSONField(ordinal = 7)
        private String conceptText;
    }

    @Data
    public static class AttrInfo implements Serializable {

        private static final long serialVersionUID = -8842889913582821962L;

        // @JsonProperty("attr_field")
        @JSONField(name = "attr_field")
        private String attrField;

        @JSONField(name = "attr_name", ordinal = 1)
        private String attrName;

        @JSONField(ordinal = 2)
        private List<Attribute> attrs;

    }

    @Data
    public static class Attribute implements Serializable {

        private static final long serialVersionUID = -858753204580479795L;

        @JSONField(ordinal = 1)
        private List<EntityInfo> entity;

        @JSONField(ordinal = 2)
        private String content;

    }

    @Data
    public static class EntityInfo implements Serializable {

        private static final long serialVersionUID = -3946422986041230641L;

        @JSONField(name = "text_id", ordinal = 1)
        private String textId;

        @JSONField(ordinal = 2)
        private Integer start;

        @JSONField(ordinal = 3)
        private Integer end;

        @JSONField(ordinal = 4)
        private String content;

    }
}
