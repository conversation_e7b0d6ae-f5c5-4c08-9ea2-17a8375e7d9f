package org.biosino.nlp.modules.project.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.vo.ProjectVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
@Mapper
public interface ProjectDao extends BaseMapper<Project> {

    /**
     * 查询用户参与的项目
     */
    List<Project> queryProjectParticipate(@Param("userId") Long userId, @Param("roleId") Long roleId);

    List<ProjectVO> queryProjects(@Param("roleId") Long roleId, @Param("userId") Long userId);

    /**
     * 查询多人标注审核员id
     */
    List<Long> findMultiAuditUserIdByPrjAndRoleId(@Param("projectId") Long projectId, @Param("roleId") Long roleId);

}
