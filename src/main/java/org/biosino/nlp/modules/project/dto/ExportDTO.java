package org.biosino.nlp.modules.project.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 数据导出条件DTO
 *
 * <AUTHOR>
 * @date 2023/5/23
 */
@Data
public class ExportDTO {
    @NotNull(message = "projectId不能为空")
    private Long projectId;

    private Long batchId;

    @NotNull(message = "step不能为空")
    private Integer step;

    @NotNull(message = "module不能为空")
    @DecimalMin(value = "1", message = "module只能是1或2")
    @DecimalMax(value = "2", message = "module只能是1或2")
    private Integer module;

    /**
     * 1是json 2是excel
     */
    @NotNull(message = "type不能为空")
    @DecimalMin(value = "1", message = "module只能是1或2")
    @DecimalMax(value = "2", message = "module只能是1或2")
    private Integer type;

    @Pattern(regexp = "\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}", message = "不是合法的email")
    private String email;

    private Long userId;
}
