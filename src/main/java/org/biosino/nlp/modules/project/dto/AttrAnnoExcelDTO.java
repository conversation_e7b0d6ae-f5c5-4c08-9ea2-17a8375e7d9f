package org.biosino.nlp.modules.project.dto;

import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 属性标注sheet
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class AttrAnnoExcelDTO implements Serializable {
    private static final long serialVersionUID = -1772248510638281991L;

    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "属性ID", order = 2)
    private String id;

    @ExcelProperty(value = "属性名", order = 3)
    private String attrName;

    @ExcelProperty(value = "属性字段", order = 4)
    private String attrField;

    @ExcelProperty(value = "属性内容", order = 5)
    private String content;

    @ExcelProperty(value = "属性类型", order = 6)
    private String type;

    @ExcelProperty(value = "标注人", order = 7)
    private String userName;

    @ExcelProperty(value = "标注时间", order = 8)
    private Date createTime;
}

