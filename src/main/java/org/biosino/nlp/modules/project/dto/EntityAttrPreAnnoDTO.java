package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.nlp.modules.note.dto.AnnoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
public class EntityAttrPreAnnoDTO {
    @JSONField(name = "article_id")
    private String articleId;

    private List<Info> entities = new ArrayList<>();

    @Data
    public static class Info {
        private String label;
        private List<AnnoDTO.EntityInfo> entity = new ArrayList<>();
        private List<AttrInfo> attribute = new ArrayList<>();
        private String annotation;
    }

    @Data
    public static class AttrInfo {
        @JSONField(name = "attr_name")
        private String attrName;
        private List<Attribute> attrs = new ArrayList<>();
    }

    @Data
    public static class Attribute {
        private List<AnnoDTO.EntityInfo> entity = new ArrayList<>();
        private String content;
    }
}
