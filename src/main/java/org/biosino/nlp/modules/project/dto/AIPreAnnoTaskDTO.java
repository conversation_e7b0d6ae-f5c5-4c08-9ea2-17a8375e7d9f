package org.biosino.nlp.modules.project.dto;

import lombok.Data;

import java.util.List;

/**
 * AI预标注任务DTO
 *
 * @date 2023/11/28
 */
@Data
public class AIPreAnnoTaskDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    private String projectName;

    private Integer chunkSize;

    /**
     * 金标准批次ID
     */
    private Long sourceBatchId;

    private String sourceBatchName;

    /**
     * 金标准文献ID列表
     */
    private List<String> sourceArticleIds;

    private List<String> sourceDocumentIds;

    private Boolean sourceSelectAllPage = false;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 用户输入的提示词
     */
    private String prompt;

    private String aiModel;

    private String endpoint;

    private String apiKey;

    /**
     * 待标注文献批次ID
     */
    private Long batchId;

    private String batchName;

    private String activeStep;

    /**
     * 待标注文献ID列表
     */
    private List<String> articleIds;

    private List<String> documentIds;

    private Boolean selectAllPage = false;
}
