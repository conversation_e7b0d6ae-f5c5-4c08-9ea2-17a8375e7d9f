package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.ImportResultEnum;
import org.biosino.nlp.common.enums.ImportStatusEnum;
import org.biosino.nlp.common.enums.ImportTypeEnum;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.DownloadUtil;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.project.dao.ImportLogDao;
import org.biosino.nlp.modules.project.dto.ImportLogDTO;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.entity.LoadDocument;
import org.biosino.nlp.modules.project.service.ImportLogService;
import org.biosino.nlp.modules.project.service.LoadDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ImportLog业务层
 *
 * <AUTHOR> Li
 * @date 2023/4/20
 */
@Service
@Slf4j
public class ImportLogServiceImpl extends ServiceImpl<ImportLogDao, ImportLog> implements ImportLogService {

    @Autowired
    private LoadDocumentService loadDocumentService;
    @Autowired
    private ImportLogDao importLogDao;

    @Override
    public PageUtils queryPage(ImportLogDTO dto, Long userId) {
        if (dto.getType() == null) {
            throw new RRException("参数错误");
        }
        Wrapper<ImportLog> queryWrapper = Wrappers.<ImportLog>lambdaQuery()
                .eq(dto.getProjectId() != null, ImportLog::getProjectId, dto.getProjectId())
                .eq(dto.getBatchId() != null, ImportLog::getBatchId, dto.getBatchId())
                .eq(dto.getType() != null, ImportLog::getType, dto.getType())
                .orderByDesc(ImportLog::getCreateTime);
        IPage<ImportLog> page = this.page(new Query<ImportLog>().getPage(dto), queryWrapper);
        List<ImportLog> list = page.getRecords();
        // 当查询通过bfms的导入的记录时需要统计
        if (dto.getType().equals(ImportTypeEnum.article_bfms.getValue())) {
            List<Long> ids = list.stream().map(ImportLog::getId).collect(Collectors.toList());
            Map<Long, List<LoadDocument>> map =
                    loadDocumentService.findAllByImportIdIn(ids).stream().collect(Collectors.groupingBy(LoadDocument::getImportLogId));
                for (ImportLog item : list) {
                    Map<String, Long> dataMap = new LinkedHashMap<>();
                    if (map.size() > 0) {
                        List<LoadDocument> loadDocuments = map.get(item.getId());
                        dataMap.put("total", Optional.ofNullable(loadDocuments).isPresent() ? (long) loadDocuments.size() : 0L);

                        long success = Optional.ofNullable(loadDocuments).isPresent() ? loadDocuments.stream().filter(x -> x.getStatus().equals(ImportResultEnum.success.getValue())).count() : 0L;
                        long fail = Optional.ofNullable(loadDocuments).isPresent() ? loadDocuments.stream().filter(x -> x.getStatus().equals(ImportResultEnum.fail.getValue())).count() : 0L;
                        long repeat = Optional.ofNullable(loadDocuments).isPresent() ? loadDocuments.stream().filter(x -> x.getStatus().equals(ImportResultEnum.repeat.getValue())).count() : 0L;
                        dataMap.put("success", success);
                        dataMap.put("fail", fail);
                        dataMap.put("repeat", repeat);
                    }else {
                        dataMap.put("total",  0L);
                        dataMap.put("success", 0L);
                        dataMap.put("fail", 0L);
                        dataMap.put("repeat", 0L);
                    }
                    item.setData(dataMap);
                }
        }
        // 当查询自有导入的文章时需要统计导入量
        else {
            for (ImportLog item : list) {
                item.setData(loadDocumentService.count(Wrappers.<LoadDocument>lambdaQuery().eq(LoadDocument::getImportLogId, item.getId())));
            }
        }
        return new PageUtils(page, list);
    }

    @Override
    @SneakyThrows
    public ResponseEntity<Resource> download(String type, Long id) {
        ImportLog importLog = this.getById(id);
        if (!CollUtil.newArrayList("source", "log").contains(type)) {
            throw new RRException("下载类型错误");
        }
        File file = null;
        String fileName = null;
        if (type.equals("source")) {
            file = new File(importLog.getImportFilePath());
            fileName = importLog.getName();

        }
        if (type.equals("log")) {
            file = new File(importLog.getLogFilePath());
            fileName = importLog.getId() + "-" +
                    importLog.getName().substring(0, importLog.getName().lastIndexOf(".json")) + ".json" + "错误日志" + ".log";

        }
        if (StrUtil.isBlank(fileName)) {
            throw new RRException("参数错误，文件名不能为空");
        }
        if (!FileUtil.exist(file)) {
            throw new RRException("没有找到错误日志");
        }


        return DownloadUtil.downLoadWithResource(file, fileName);
    }

    @Override
    public Boolean existByProjectIdAndName(Long projectId, String name) {
        return this.count(Wrappers.<ImportLog>lambdaQuery()
                .eq(ImportLog::getProjectId, projectId)
                .eq(ImportLog::getName, name)
                .ne(ImportLog::getStatus, ImportStatusEnum.fail.getValue())) > 0;
    }

    @Override
    @CacheEvict(cacheNames = "existSource_cache", allEntries = true)
    public void changeEnabled(ImportLogDTO dto) {
        ImportLog importLog = this.getById(dto.getId());
        if (importLog == null) {
            throw new RRException("未找到导入记录");
        }
        importLog.setEnabled(dto.getEnabled());
        this.updateById(importLog);
    }

    @Override
    public Boolean existByIdAndType(Long id, Integer type) {
        return this.count(Wrappers.<ImportLog>lambdaQuery()
                .eq(ImportLog::getId, id)
                .eq(ImportLog::getType, type)) > 0;
    }

    @Override
    public List<ImportLog> getPreSourceList(Long projectId) {
        LambdaQueryWrapper<ImportLog> importLogQuery = Wrappers.lambdaQuery(ImportLog.class);
        importLogQuery.eq(ImportLog::getProjectId, projectId);
        importLogQuery.eq(ImportLog::getType, ImportTypeEnum.pre_anno_entity.getValue());
        importLogQuery.eq(ImportLog::getEnabled, StatusEnum.enable.getValue());
        importLogQuery.orderByDesc(ImportLog::getCreateTime);
        return this.list(importLogQuery);
    }

    @Override
    public ImportLog findById(Long logId) {
        if (logId == null) {
            return null;
        }
       return importLogDao.findById(logId);
    }
}
