package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.project.dto.ArticleListDTO;
import org.biosino.nlp.modules.project.dto.BatchDTO;
import org.biosino.nlp.modules.project.dto.LoadArticleDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.LoadDocument;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-04 10:54
 */
public interface BatchService extends IService<Batch> {

    PageUtils queryPage(BatchDTO dto);

    List<Batch> findAllByProjectId(Long projectId);

    void saveOrUpdate(Batch batch, Long creatorId);

    Batch queryBatch(Long batchId);

    void loadArticles(MultipartFile file, Long batchId, Long userId) throws IOException;

    void validateBatchName(String batchName, Long projectId, Long batchId);

    /**
     * 文章导入结果
     */
    List<LoadDocument> loadArticleResult(Long importLogId, Long userId);

    /**
     * 重新导入失败的文章
     */
    void reloadArticle(Long importLogId, Long userId);

    /**
     * 导出文章导入结果
     */
    void exportArticleResult(Long importLogId, Long userId, HttpServletResponse response) throws IOException;

    /**
     * 文章导入列表
     */
    PageUtils loadArticleList(LoadArticleDTO dto, Long userId);

    /**
     * 修改文章状态 （启用/弃用）
     */
    void changeNoteArticleStatus(Long batchId, String articleId, Long userId);

    void resetNoteArticle(Long noteId);

    void uploadArticles(MultipartFile file, Long batchId, Long userId);

    PageIdsListVO getArticleList(ArticleListDTO dto);

    void batchSubmit(Long[] ids);

    /**
     * 将批次下的文章均衡分配给标注员
     * @param batchId 批次ID
     */
    void distributeTasksEvenly(Long batchId);
}
