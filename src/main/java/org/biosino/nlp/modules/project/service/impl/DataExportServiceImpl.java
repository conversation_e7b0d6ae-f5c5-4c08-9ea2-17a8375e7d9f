package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import lombok.SneakyThrows;
import org.biosino.nlp.common.enums.*;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.HutoolExcelUtil;
import org.biosino.nlp.modules.api.mapper.EntityMapper;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.labels.service.RelationPatternService;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.BaseMongo;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.project.dto.*;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.ExportLog;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.*;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Li
 * @date 2023/5/23
 */
@Service
public class DataExportServiceImpl implements DataExportService {

    private final static String KEY_SYMBOL = "※";

    @Autowired
    private ExportLogService exportLogService;

    @Autowired
    private EntityLabelService entityLabelService;

    @Autowired
    private AttributeLabelService attributeLabelService;

    @Autowired
    private RelationLabelService relationLabelService;

    @Autowired
    private RelationPatternService relationPatternService;

    @Autowired
    private NoteService noteService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private EntityRepository entityRepository;

    @Autowired
    private AttributesRepository attributesRepository;

    @Autowired
    private RelationshipRepository relationshipRepository;

    @Autowired
    private MailService mailService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private NoteTaskService noteTaskService;

    @Value("${app.download-host}")
    private String downloadHost;


    @Override
    public void generateExportLog(ExportDTO dto) {

        if (exportLogService.existsUnfinishedByProject(dto.getProjectId(), dto.getUserId())) {
            throw new RRException("您当前在此项目已有导出任务，请稍后再试");
        }
        if (!dto.getStep().equals(NoteStepEnum.reviewed.getCode()) && dto.getStep() != -1) {
            throw new RRException("step参数错误，只能为-1或者4");
        }
        LambdaQueryWrapper<Note> noteQuery = Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, dto.getProjectId())
                .eq(Note::getInvalid, NoteInvalidEnum.normal.getCode())
                .ne(Note::getStep, NoteStepEnum.unmarked.getCode())
                .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId());

        // 已验收
        if (dto.getStep().equals(NoteStepEnum.reviewed.getCode())) {
            noteQuery.eq(Note::getStep, NoteStepEnum.reviewed.getCode());
        }

        // 查询项目中符合条件的note
        List<Note> noteList = noteService.list(noteQuery);
        // 查询有无数据导出
        if (CollUtil.isEmpty(noteList)) {
            throw new RRException("暂无数据导出");
        }
        LambdaQueryWrapper<NoteTask> noteTaskQuery = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getProjectId, dto.getProjectId())
                .in(NoteTask::getNoteId, noteList.stream().map(Note::getNoteId).collect(Collectors.toList()))
                .eq(dto.getBatchId() != null, NoteTask::getBatchId, dto.getBatchId())
                .orderByDesc(NoteTask::getUpdateTime);
        // 已验收
        if (dto.getStep().equals(NoteStepEnum.reviewed.getCode())) {
            noteTaskQuery.eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode())
                    .eq(NoteTask::getMaster, MasterEnum.master.getValue());

            List<NoteTask> taskList = noteTaskService.list(noteTaskQuery);

            // 查询有无数据导出
            if (CollUtil.isEmpty(taskList)) {
                throw new RRException("暂无数据导出");
            }
            List<NoteTask> distinctNoteTask = new ArrayList<>(taskList.stream()
                    .collect(Collectors.toMap(NoteTask::getNoteId, Function.identity(), (oldValue, newValue) -> oldValue))
                    .values());
            ExportLog exportLog = createExportLog(dto);

            exportLogService.saveOrUpdate(exportLog);

            // 导出文件的输出目录
            File dir = Constant.createDownloadDir(exportLog.getId());

            // 异步开启导出任务
            ThreadUtil.execAsync(() -> {
                try {
                    exportAnnoData(dto, noteList, distinctNoteTask, null, dir);

                    // 将下载目录压缩
                    ZipUtil.zip(dir, Charset.defaultCharset());
                    // 更新任务状态为成功
                    updateExportLog(exportLog, dir, ExportStatusEnum.finish);
                } catch (Exception e) {
                    // 更新任务状态为失败
                    updateExportLog(exportLog, dir, ExportStatusEnum.fail);
                    // 打印错误信息
                    e.printStackTrace();
                }
                // 发送下载的邮件
                sendDownloadMail(exportLog.getId());
            });
        } else {

            // 查询这个项目下的所有标注员
            List<NoteTask> taskList = noteTaskService.list(noteTaskQuery);

            // 查询有无数据导出
            if (CollUtil.isEmpty(taskList)) {
                throw new RRException("暂无数据导出");
            }

            ExportLog exportLog = createExportLog(dto);

            exportLogService.saveOrUpdate(exportLog);
            // 导出文件的输出目录
            File dir = Constant.createDownloadDir(exportLog.getId());

            ThreadUtil.execAsync(() -> {

                // 根据标注员分组
                Map<Long, List<NoteTask>> noteGroupMap = taskList.stream().collect(Collectors.groupingBy(NoteTask::getAnnotator));

                try {
                    noteGroupMap.forEach((userId, noteTaskList) -> {

                        List<NoteTask> distinctNoteTask = new ArrayList<>(noteTaskList.stream()
                                .collect(Collectors.toMap(NoteTask::getNoteId, Function.identity(), (oldValue, newValue) -> oldValue))
                                .values());

                        exportAnnoData(dto, noteList, distinctNoteTask, userId, dir);
                    });
                    // 将下载目录压缩
                    ZipUtil.zip(dir, Charset.defaultCharset());
                    // 更新任务状态为成功
                    updateExportLog(exportLog, dir, ExportStatusEnum.finish);
                } catch (Exception e) {
                    // 更新任务状态为失败
                    updateExportLog(exportLog, dir, ExportStatusEnum.fail);
                    // 打印错误信息
                    e.printStackTrace();
                }
                // 发送下载的邮件
                sendDownloadMail(exportLog.getId());
            });
        }

    }

    private ExportLog createExportLog(ExportDTO dto) {
        ExportLog exportLog = new ExportLog();
        BeanUtil.copyProperties(dto, exportLog);
        exportLog.setCreateTime(new Date());
        exportLog.setStatus(ExportStatusEnum.wait.getCode());
        return exportLog;
    }

    private void updateExportLog(ExportLog exportLog, File file, ExportStatusEnum status) {
        exportLog.setOutputFilePath(file.getAbsolutePath());
        exportLog.setStatus(status.getCode());
        if (status == ExportStatusEnum.finish) {
            exportLog.setFinishTime(new Date());
        }
        exportLogService.saveOrUpdate(exportLog);
    }

    @Override
    public void exportAnnoData(ExportDTO dto, List<Note> noteList, List<NoteTask> noteTaskList, Long userId, File dir) {
        Map<Long, List<NoteTask>> batchNoteTaskListMap = noteTaskList.stream()
                .collect(Collectors.groupingBy(
                        NoteTask::getBatchId,
                        LinkedHashMap::new,     // 保持 batchId 顺序
                        Collectors.toList()     // 保持每个 batch 对应的 NoteTask 顺序
                ));

        Map<Long, Note> noteIdMap = noteList.stream()
                .collect(Collectors.toMap(
                        Note::getNoteId,
                        Function.identity(),
                        (o, n) -> n,            // 可选：处理 key 冲突，保留新值
                        LinkedHashMap::new      // 保留插入顺序
                ));

        // 加载项目中的标签之类的内容
        // 实体标签
        Map<Long, String> entityLabelMap = entityLabelService.findAllEnableByProjectId(dto.getProjectId())
                .stream()
                .collect(Collectors.toMap(EntityLabel::getId, EntityLabel::getName));
        // 属性标签
        Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.keySet())
                .stream()
                .collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getId, x -> x)));


        // 查出所有用户
        Map<Long, String> userMap = sysUserService.list().stream().collect(Collectors.toMap(SysUserEntity::getUserId,
                SysUserEntity::getUsername));
        userMap.put(2L, "自动审核");
        Project project = projectService.getById(dto.getProjectId());

        // 导出实体
        if (dto.getModule().equals(AnnoModuleEnum.entity.getCode())) {
            // 导出实体标注数据json
            if (dto.getType().equals(ExportFileTypeEnum.json.getCode())) {
                exportEntityJsonData(project.getName(), dto.getBatchId(), userId, noteIdMap, batchNoteTaskListMap, entityLabelMap, entityIdAttrLabelMap, dir);
            }
            // 导出实体标注数据excel
            if (dto.getType().equals(ExportFileTypeEnum.excel.getCode())) {
                exportEntityExcelDataNew(noteIdMap, batchNoteTaskListMap, userId, entityLabelMap, entityIdAttrLabelMap, userMap, dir);
            }
        }
        // 导出关系
        if (dto.getModule().equals(AnnoModuleEnum.relation.getCode())) {
            // 关系标签
            Map<Long, String> relationLabelMap = relationLabelService.findAllEnableByProjectId(dto.getProjectId())
                    .stream()
                    .collect(Collectors.toMap(RelationLabel::getId, RelationLabel::getName));
            // 关系模板
            Map<Long, String> relationPatternMap = relationPatternService.findAllEnableByProjectId(dto.getProjectId())
                    .stream()
                    .collect(Collectors.toMap(RelationPattern::getId, RelationPattern::getName));
            // 导出关系标注数据json
            if (dto.getType().equals(ExportFileTypeEnum.json.getCode())) {
                exportRelationJsonData(project.getName(), dto.getBatchId(), userId, noteIdMap, batchNoteTaskListMap, entityLabelMap, entityIdAttrLabelMap, relationLabelMap, relationPatternMap, dir);
            }
            // 导出关系标注数据excel
            if (dto.getType().equals(ExportFileTypeEnum.excel.getCode())) {
                exportRelationExcelDataNew(batchNoteTaskListMap, entityLabelMap, userId, noteIdMap, entityIdAttrLabelMap, relationLabelMap, userMap, dir);
            }
        }
    }

    private void exportEntityExcelDataNew(Map<Long, Note> noteIdMap,
                                          Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                          Long userId,
                                          Map<Long, String> entityLabelMap,
                                          Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                          Map<Long, String> userMap,
                                          File dir) {
        for (Long batchId : batchNoteTaskListMap.keySet()) {
            Batch batch = batchService.getById(batchId);
            String filename = batch.getName();
            if (userId != null) {
                filename += "_" + sysUserService.findNameById(userId);
            }
            File file = FileUtil.file(dir, filename + ".xlsx");
            List<NoteTask> noteTasks = batchNoteTaskListMap.get(batchId);
            ExcelWriter writer = null;
            try {
                ArrayList<EntityAnnoExcelDTONew> entityExcelDTOs = new ArrayList<>();
                for (NoteTask noteTask : noteTasks) {
                    Note note = noteIdMap.get(noteTask.getNoteId());
                    String articleId = note.getArticleId();
                    Long taskId = noteTask.getTaskId();

                    // 查询文章的段落id和内容
                    Map<String, String> idAndContentMap = noteService.findIdAndContentMap(note.getDocumentId());

                    // 查询项目中的实体标注数据
                    List<Entity> entityList = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.not_attr.getCode(), false);
                    // 如果文章没有内容则跳过此次任务
                    if (CollUtil.isEmpty(entityList)) {
                        continue;
                    }
                    // 找到所有实体的属性 key: 实体的id ， value: 属性list
                    Map<String, List<Attributes>> entityAttrMap = attributesRepository.findAllEntityIdIn(entityList.stream().map(BaseMongo::getId)
                                    .distinct().collect(Collectors.toList()))
                            .stream().collect(Collectors.groupingBy(Attributes::getEntityId));

                    // 查询项目中划词属性数据 key: 实体id value: 实体
                    Map<String, Entity> attrEntityMap = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.is_attr.getCode(), false)
                            .stream()
                            .collect(Collectors.toMap(Entity::getId, x -> x));

                    for (Entity entity : entityList) {
                        EntityAnnoExcelDTONew dto = new EntityAnnoExcelDTONew();
                        // 将所有的实体和划词属性实体的 entityInfo收集，key：实体id value：实体的entityInfo
                        Map<String, List<AnnoDTO.EntityInfo>> allEntityInfo = getAllEntityInfo(entity, entityAttrMap, attrEntityMap);
                        // 获取拼接后的text文本
                        String text = tranText(idAndContentMap, allEntityInfo);
                        dto.setArticleId(articleId);
                        dto.setText(text);
                        dto.setType(entityLabelMap.get(entity.getLabelId()));
                        dto.setName(getContentStr(entity));
                        dto.setConceptText(entity.getConceptText());
                        dto.setConceptId(entity.getConceptId());
                        if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
                            dto.setConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
                        }
                        // 处理entityInfo的偏移量
                        Map<String, List<AnnoDTO.EntityInfo>> newEntityInfoMap = tranEntityInfo(idAndContentMap, allEntityInfo);

                        String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
                        String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
                        dto.setAttr(attr);
                        dto.setAttrInfo(attrInfo);
                        dto.setStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                        dto.setEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));

                        dto.setAnnotate(entity.getAnnotate());
                        dto.setAnnotatorName(userMap.getOrDefault(entity.getAnnotatorId(), null));
                        dto.setAuditorName(userMap.getOrDefault(entity.getAuditorId(), null));
                        dto.setCreateTime(entity.getCreateTime());
                        entityExcelDTOs.add(dto);
                    }

                }
                if (CollUtil.isEmpty(entityExcelDTOs)) {
                    continue;
                }
                writer = ExcelUtil.getWriter(file);
                writer.renameSheet("实体");
                HutoolExcelUtil.setHeaderAlias(writer, EntityAnnoExcelDTONew.class);
                writer.write(objectListToMapListWithJSON(entityExcelDTOs, EntityAnnoExcelDTONew.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), EntityAnnoExcelDTONew.class.getDeclaredFields().length - 1);

            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                IoUtil.close(writer);
            }
        }

    }

    private void exportRelationExcelDataNew(Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                            Map<Long, String> entityLabelMap,
                                            Long userId,
                                            Map<Long, Note> noteIdMap,
                                            Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                            Map<Long, String> relationLabelMap,
                                            Map<Long, String> userMap,
                                            File dir) {
        for (Long batchId : batchNoteTaskListMap.keySet()) {
            Batch batch = batchService.getById(batchId);
            String filename = batch.getName();
            if (userId != null) {
                filename += "_" + sysUserService.findNameById(userId);
            }
            File file = FileUtil.file(dir, filename + ".xlsx");
            List<NoteTask> noteTasks = batchNoteTaskListMap.get(batchId);
            ExcelWriter writer = null;
            try {
                List<RelationAnnoExcelNewDTO> relationExcelDTOs = new ArrayList<>();
                for (NoteTask noteTask : noteTasks) {

                    Note note = noteIdMap.get(noteTask.getNoteId());

                    Map<String, String> idAndContentMap = noteService.findIdAndContentMap(note.getDocumentId());
                    String articleId = note.getArticleId();
                    // 查询项目中的实体标注数据
                    List<Relationship> relationList = relationshipRepository.findAllByTaskIdAndDeleted(noteTask.getTaskId(), false);

                    if (CollUtil.isEmpty(relationList)) {
                        continue;
                    }


                    for (Relationship relationship : relationList) {
                        // 找到这条关系涉及的所有实体id
                        Set<String> entityIdSet = mergeEntityId(relationship);

                        // 找到这条关系涉及的所有实体 key:entityId value:entity
                        Map<String, Entity> entityMap = entityRepository.findAllByIdInAndDeleted(entityIdSet, false)
                                .stream()
                                .distinct()
                                .collect(Collectors.toMap(Entity::getId, x -> x));

                        // 找到所有实体的属性 key:entityId value:属性标注列表
                        List<Attributes> attributes = attributesRepository.findAllEntityIdIn(entityIdSet);
                        Map<String, List<Attributes>> entityAttrMap = attributes
                                .stream().collect(Collectors.groupingBy(Attributes::getEntityId));
                        // 找到实体标注下面的属性标注的划词属性 key:属性实体Id value:属性划词实体
                        Map<String, Entity> attrEntityMap = entityRepository
                                .findAllByIdInAndDeleted(attributes.stream().map(Attributes::getAttributeId).collect(Collectors.toList()), false)
                                .stream().collect(Collectors.toMap(Entity::getId, x -> x));

                        // 将涉及的所有实体的entityInfo提取并处理
                        Map<String, List<AnnoDTO.EntityInfo>> oldEntityInfoMap = new LinkedHashMap<>();
                        entityMap.forEach((k, v) -> {
                            oldEntityInfoMap.putAll(getAllEntityInfo(v, entityAttrMap, attrEntityMap));
                        });
                        String text = tranText(idAndContentMap, oldEntityInfoMap);
                        Map<String, List<AnnoDTO.EntityInfo>> newEntityInfoMap = tranEntityInfo(idAndContentMap, oldEntityInfoMap);


                        for (Relationship.RelationItem item : relationship.getItems()) {
                            List<String> subject = item.getSubject();
                            List<String> objects = item.getObjects();

                            // subject为复合实体
                            if (subject.size() > 1 && objects.size() == 1) {
                                RelationAnnoExcelNewDTO dto = new RelationAnnoExcelNewDTO();
                                dto.setId(relationship.getId());
                                dto.setArticleId(articleId);
                                dto.setOrder(item.getOrder());
                                String combinationId = "C_" + IdUtil.simpleUUID();
                                dto.setSubjectType("【虚拟实体】");
                                dto.setSubjectName(combinationId);
                                setRelationInfo(dto, relationLabelMap, item);

                                boolean allObjectsIsEntity = objects.stream().allMatch(s -> s.length() == 32);
                                if (allObjectsIsEntity) {
                                    String obj = objects.get(0);
                                    Entity entity = entityMap.get(obj);

                                    dto.setObjectType(entityLabelMap.get(entity.getLabelId()));
                                    dto.setObjectName(getContentStr(entity));
                                    dto.setObjectConceptText(entity.getConceptText());
                                    dto.setObjectConceptId(entity.getConceptId());
                                    if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
                                        dto.setObjectConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
                                    }
                                    dto.setText(text);
                                    String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
                                    String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
                                    dto.setObjectAttr(attr);
                                    dto.setObjectAttrInfo(attrInfo);
                                    dto.setObjectStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                                    dto.setObjectEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));
                                } else {
                                    dto.setObjectType("【三元组】");
                                    String[] split = objects.get(0).split("#");
                                    dto.setObjectName(StrUtil.format("引用：{}的{}",
                                            split[0],
                                            split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                    ));
                                    if (split.length == 1) {
                                        dto.setObjectName(StrUtil.format("引用：{}", split[0]));
                                    }
                                }
                                dto.setAnnotatorName(userMap.getOrDefault(relationship.getAnnotatorId(), null));
                                dto.setAuditorName(userMap.getOrDefault(relationship.getAuditorId(), null));
                                dto.setCreateTime(relationship.getCreateTime());
                                relationExcelDTOs.add(dto);
                                for (String s : subject) {
                                    Entity entity = entityMap.get(s);
                                    RelationAnnoExcelNewDTO vDto = createVirtualRelationAnnoExcelDTO(entityLabelMap, entityIdAttrLabelMap, userMap, text, relationship, entityAttrMap, newEntityInfoMap, combinationId, entity);
                                    relationExcelDTOs.add(vDto);
                                }
                            }

                            // object是复合实体
                            if (subject.size() == 1 && objects.size() > 1) {
                                RelationAnnoExcelNewDTO dto = new RelationAnnoExcelNewDTO();
                                dto.setId(relationship.getId());
                                dto.setArticleId(articleId);
                                dto.setOrder(item.getOrder());
                                String combinationId = "C_" + IdUtil.simpleUUID();
                                dto.setObjectType("【虚拟实体】");
                                dto.setObjectName(combinationId);
                                setRelationInfo(dto, relationLabelMap, item);
                                boolean allSubjectIsEntity = subject.stream().allMatch(s -> s.length() == 32);
                                if (allSubjectIsEntity) {
                                    String sub = subject.get(0);
                                    Entity entity = entityMap.get(sub);

                                    dto.setSubjectType(entityLabelMap.get(entity.getLabelId()));
                                    dto.setSubjectName(getContentStr(entity));
                                    dto.setSubjectConceptText(entity.getConceptText());
                                    dto.setSubjectConceptId(entity.getConceptId());
                                    if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
                                        dto.setSubjectConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
                                    }

                                    dto.setText(text);
                                    String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
                                    String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
                                    dto.setSubjectAttr(attr);
                                    dto.setSubjectAttrInfo(attrInfo);
                                    dto.setSubjectStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                                    dto.setSubjectEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));

                                } else {
                                    dto.setSubjectType("【三元组】");
                                    String[] split = subject.get(0).split("#");
                                    dto.setSubjectName(StrUtil.format("引用：{}的{}",
                                            split[0],
                                            split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                    ));
                                    if (split.length == 1) {
                                        dto.setSubjectName(StrUtil.format("引用：{}", split[0]));
                                    }
                                }
                                dto.setAnnotatorName(userMap.getOrDefault(relationship.getAnnotatorId(), null));
                                dto.setAuditorName(userMap.getOrDefault(relationship.getAuditorId(), null));
                                dto.setCreateTime(relationship.getCreateTime());
                                relationExcelDTOs.add(dto);
                                for (String o : objects) {
                                    Entity entity = entityMap.get(o);
                                    RelationAnnoExcelNewDTO vDto = createVirtualRelationAnnoExcelDTO(entityLabelMap, entityIdAttrLabelMap, userMap, text, relationship, entityAttrMap, newEntityInfoMap, combinationId, entity);
                                    relationExcelDTOs.add(vDto);
                                }
                            }

                            // 都不是复合实体
                            if (subject.size() == 1 && objects.size() == 1) {
                                RelationAnnoExcelNewDTO dto = new RelationAnnoExcelNewDTO();
                                dto.setId(relationship.getId());
                                dto.setArticleId(articleId);
                                dto.setOrder(item.getOrder());
                                dto.setText(text);
                                boolean allSubjectIsEntity = subject.stream().allMatch(s -> s.length() == 32);
                                if (allSubjectIsEntity) {
                                    String sub = subject.get(0);
                                    Entity entity = entityMap.get(sub);
                                    dto.setSubjectType(entityLabelMap.get(entity.getLabelId()));
                                    dto.setSubjectName(getContentStr(entity));
                                    dto.setSubjectConceptText(entity.getConceptText());
                                    dto.setSubjectConceptId(entity.getConceptId());
                                    if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
                                        dto.setSubjectConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
                                    }
                                    String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
                                    String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
                                    dto.setSubjectAttr(attr);
                                    dto.setSubjectAttrInfo(attrInfo);
                                    dto.setSubjectStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                                    dto.setSubjectEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));

                                } else {
                                    dto.setSubjectType("【三元组】");
                                    String[] split = subject.get(0).split("#");
                                    dto.setSubjectName(StrUtil.format("引用：{}的{}",
                                            split[0],
                                            split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                    ));
                                    if (split.length == 1) {
                                        dto.setSubjectName(StrUtil.format("引用：{}", split[0]));
                                    }
                                }
                                setRelationInfo(dto, relationLabelMap, item);


                                boolean allObjectIsEntity = objects.stream().allMatch(s -> s.length() == 32);
                                if (allObjectIsEntity) {
                                    String obj = objects.get(0);
                                    Entity entity = entityMap.get(obj);
                                    dto.setObjectType(entityLabelMap.get(entity.getLabelId()));
                                    dto.setObjectName(getContentStr(entity));
                                    dto.setObjectConceptText(entity.getConceptText());
                                    dto.setObjectConceptId(entity.getConceptId());
                                    if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
                                        dto.setObjectConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
                                    }
                                    dto.setObjectStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                                    dto.setObjectEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));
                                    String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
                                    String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
                                    dto.setObjectAttr(attr);
                                    dto.setObjectAttrInfo(attrInfo);
                                } else {
                                    dto.setObjectType("【三元组】");
                                    String[] split = objects.get(0).split("#");
                                    dto.setObjectName(StrUtil.format("引用：{}的{}",
                                            split[0],
                                            split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                    ));
                                    if (split.length == 1) {
                                        dto.setObjectName(StrUtil.format("引用：{}", split[0]));
                                    }
                                }
                                dto.setAnnotatorName(userMap.getOrDefault(relationship.getAnnotatorId(), null));
                                dto.setAuditorName(userMap.getOrDefault(relationship.getAuditorId(), null));
                                dto.setCreateTime(relationship.getCreateTime());
                                relationExcelDTOs.add(dto);
                            }
                        }
                    }
                }
                if (CollUtil.isEmpty(relationExcelDTOs)) {
                    continue;
                }
                // 写入文件
                writer = ExcelUtil.getWriter(file);

                // 写入关系
                writer.renameSheet("关系");
                HutoolExcelUtil.setHeaderAlias(writer, RelationAnnoExcelNewDTO.class);
                writer.write(objectListToMapListWithJSON(relationExcelDTOs, RelationAnnoExcelNewDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), RelationAnnoExcelNewDTO.class.getDeclaredFields().length - 1);


            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                IoUtil.close(writer);
            }
        }
    }

    private RelationAnnoExcelNewDTO createVirtualRelationAnnoExcelDTO(Map<Long, String> entityLabelMap,
                                                                      Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                                                      Map<Long, String> userMap,
                                                                      String text,
                                                                      Relationship relationship,
                                                                      Map<String, List<Attributes>> entityAttrMap,
                                                                      Map<String, List<AnnoDTO.EntityInfo>> newEntityInfoMap,
                                                                      String combinationId,
                                                                      Entity entity) {
        RelationAnnoExcelNewDTO vDto = new RelationAnnoExcelNewDTO();
        vDto.setId(combinationId);
        vDto.setOrder(null);
        vDto.setArticleId(relationship.getArticleId());
        vDto.setText(text);
        vDto.setObjectType(entityLabelMap.get(entity.getLabelId()));
        vDto.setObjectName(getContentStr(entity));
        vDto.setObjectConceptText(entity.getConceptText());
        vDto.setObjectConceptId(entity.getConceptId());
        if (ConceptTypeEnum.fromValue(entity.getConceptType()).isPresent()) {
            vDto.setObjectConceptSource(ConceptTypeEnum.fromValue(entity.getConceptType()).get().name());
        }
        vDto.setObjectStart(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
        vDto.setObjectEnd(newEntityInfoMap.get(entity.getId()).stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));
        vDto.setRelation("is part of");
        String attr = genAttr(entity, entityAttrMap, entityIdAttrLabelMap);
        String attrInfo = genAttrInfo(entity, entityAttrMap, entityIdAttrLabelMap, newEntityInfoMap);
        vDto.setObjectAttr(attr);
        vDto.setObjectAttrInfo(attrInfo);
        vDto.setAnnotatorName(userMap.getOrDefault(relationship.getAnnotatorId(), null));
        vDto.setAuditorName(userMap.getOrDefault(relationship.getAuditorId(), null));
        vDto.setCreateTime(relationship.getCreateTime());
        return vDto;
    }

    private String genAttr(Entity entity, Map<String, List<Attributes>> entityAttrMap, Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap) {
        List<Attributes> attributesList = entityAttrMap.getOrDefault(entity.getId(), new ArrayList<>());
        Map<Long, List<Attributes>> attrMap = attributesList.stream()
                .collect(Collectors.groupingBy(Attributes::getAttrLabelId, LinkedHashMap::new, Collectors.toList()));
        // 属性简要信息
        Map<String, List<String>> attr = new LinkedHashMap<>();
        // 找到实体标签下的属性标签
        Map<Long, AttributeLabel> attrLabelMap = entityIdAttrLabelMap.get(entity.getLabelId());
        for (Long key : attrMap.keySet()) {
            List<Attributes> value = attrMap.get(key);
            AttributeLabel attributeLabel = attrLabelMap.get(key);
            String attributeLabelName = attributeLabel.getName();
            attr.put(attributeLabelName, value.stream().map(Attributes::getContent).collect(Collectors.toList()));
        }
        // 转为字符串
        List<String> strings = new ArrayList<>();
        attr.forEach((k, v) -> strings.add(StrUtil.format("【{}】{}", k, StrUtil.join(KEY_SYMBOL, v))));
        return StrUtil.join(";", strings);
    }

    private String genAttrInfo(Entity entity,
                               Map<String, List<Attributes>> entityAttrMap,
                               Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                               Map<String, List<AnnoDTO.EntityInfo>> newEntityInfoMap) {
        // 找到实体下的所有属性
        List<Attributes> attributesList = entityAttrMap.getOrDefault(entity.getId(), new ArrayList<>());
        Map<Long, List<Attributes>> attrMap = attributesList.stream()
                .collect(Collectors.groupingBy(Attributes::getAttrLabelId, LinkedHashMap::new, Collectors.toList()));

        // 找到实体标签下的属性标签
        Map<Long, AttributeLabel> attrLabelMap = entityIdAttrLabelMap.get(entity.getLabelId());

        // 属性详细信息
        List<RelationAnnoExcelNewDTO.AttrInfo> attrInfos = new ArrayList<>();
        for (Long key : attrMap.keySet()) {
            List<Attributes> value = attrMap.get(key);
            AttributeLabel attributeLabel = attrLabelMap.get(key);
            String attributeLabelName = attributeLabel.getName();

            for (Attributes attributes : value) {
                RelationAnnoExcelNewDTO.AttrInfo attrInfo = new RelationAnnoExcelNewDTO.AttrInfo();
                attrInfo.setName(attributeLabelName);
                attrInfo.setField(attributeLabel.getField());
                attrInfo.setContent(attributes.getContent());
                // 如果是划词实体
                if (StrUtil.isNotBlank(attributes.getAttributeId())) {
                    List<AnnoDTO.EntityInfo> entityInfos = newEntityInfoMap.get(attributes.getAttributeId());
                    attrInfo.setStart(entityInfos.stream().map(x -> x.getStart().toString()).collect(Collectors.joining(",")));
                    attrInfo.setEnd(entityInfos.stream().map(x -> x.getEnd().toString()).collect(Collectors.joining(",")));
                }
                attrInfos.add(attrInfo);
            }
        }
        if (CollUtil.isNotEmpty(attrInfos)) {
            return JSON.toJSONString(attrInfos);
        }
        return null;
    }


    private void setRelationInfo(RelationAnnoExcelNewDTO dto, Map<Long, String> relationLabelMap, Relationship.RelationItem item) {
        dto.setRelationId(item.getRelation());
        dto.setRelation(relationLabelMap.get(item.getRelation()));
        if (CollUtil.isNotEmpty(item.getAnnotations())) {
            dto.setAnnotations(CollUtil.join(item.getAnnotations(), StrPool.COMMA));
        }
        dto.setNegation(item.getNegation());
    }

    private String getContentStr(Entity entity) {
        if (entity.getEntityInfos().size() > 1) {
            return "【复合实体】" + entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(KEY_SYMBOL));
        }
        return entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(KEY_SYMBOL));
    }

    private Map<String, List<AnnoDTO.EntityInfo>> getAllEntityInfo(Entity entity, Map<String, List<Attributes>> entityAttrMap, Map<String, Entity> attrEntityMap) {
        Map<String, List<AnnoDTO.EntityInfo>> map = new LinkedHashMap<>();
        map.put(entity.getId(), entity.getEntityInfos());

        List<String> attrEntityIds = entityAttrMap.getOrDefault(entity.getId(), new ArrayList<>()).stream().map(Attributes::getAttributeId).filter(Objects::nonNull).collect(Collectors.toList());
        attrEntityIds.forEach(x -> {
            Entity attrEntity = attrEntityMap.get(x);
            map.put(attrEntity.getId(), attrEntity.getEntityInfos());
        });
        return map;
    }

    private String tranText(Map<String, String> idAndContentMap, Map<String, List<AnnoDTO.EntityInfo>> entityInfoMap) {
        // 文章所有的段落id ，已经排好序
        List<String> allIds = new ArrayList<>(idAndContentMap.keySet());
        // 保存段落id在list的索引
        Map<String, Integer> indexDict = new LinkedHashMap<>();
        for (int i = 0; i < allIds.size(); i++) {
            indexDict.put(allIds.get(i), i);
        }
        // 提取所有的entityInfos
        List<AnnoDTO.EntityInfo> entityInfos = entityInfoMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        // 提取entityInfo中的textId
        List<String> textIds = entityInfos.stream().map(AnnoDTO.EntityInfo::getTextId).distinct()
                .sorted(Comparator.comparingInt(indexDict::get)).collect(Collectors.toList());

        // 对textIds进行排序，然后将textId对应的文本拼接返回
        return textIds.stream().map(x -> idAndContentMap.getOrDefault(x, "")).collect(Collectors.joining());
    }

    private Map<String, List<AnnoDTO.EntityInfo>> tranEntityInfo(Map<String, String> idAndContentMap, Map<String, List<AnnoDTO.EntityInfo>> entityInfoMap) {
        // 文章所有的段落id ，已经排好序
        List<String> allIds = new ArrayList<>(idAndContentMap.keySet());
        List<AnnoDTO.EntityInfo> entityInfos = entityInfoMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<String> textIds = entityInfos.stream().map(AnnoDTO.EntityInfo::getTextId).distinct().collect(Collectors.toList());
        Map<String, Integer> indexDict = new HashMap<>();
        for (int i = 0; i < allIds.size(); i++) {
            indexDict.put(allIds.get(i), i);
        }
        textIds.sort(Comparator.comparingInt(indexDict::get));

        // 获取存在的textId的文本的长度列表
        List<Integer> lengthList = textIds.stream().map(x -> idAndContentMap.getOrDefault(x, "").length()).collect(Collectors.toList());

        // 计算偏移量
        List<Integer> offsetList = lengthList.stream()
                .reduce(new ArrayList<>(), (acc, x) -> {
                    acc.add(acc.isEmpty() ? x : acc.get(acc.size() - 1) + x);
                    return acc;
                }, (acc1, acc2) -> {
                    acc1.addAll(acc2);
                    return acc1;
                });
        // 加头
        offsetList.add(0, 0);
        // 去尾
        offsetList.remove(offsetList.size() - 1);

        Map<String, List<AnnoDTO.EntityInfo>> map = new LinkedHashMap<>();
        // 找到文本
        for (String key : entityInfoMap.keySet()) {
            List<AnnoDTO.EntityInfo> list = new ArrayList<>();
            for (AnnoDTO.EntityInfo info : entityInfoMap.get(key)) {
                AnnoDTO.EntityInfo entityInfo = new AnnoDTO.EntityInfo();
                BeanUtil.copyProperties(info, entityInfo);
                int i = textIds.indexOf(info.getTextId());
                Integer offset = offsetList.get(i);
                entityInfo.setStart(offset + entityInfo.getStart());
                entityInfo.setEnd(offset + entityInfo.getEnd());
                list.add(entityInfo);
            }
            map.put(key, list);
        }
        return map;
    }


    private static Set<String> mergeEntityId(Relationship relationship) {
        return relationship.getItems().stream()
                .flatMap(item -> Stream.of(item.getSubject(), item.getObjects()))
                .flatMap(Collection::stream)
                .filter(x -> x.length() == 32)
                .collect(Collectors.toSet());
    }

    /**
     * 导出实体标注json数据
     */
    private void exportEntityJsonData(String projectName,
                                      Long batchId,
                                      Long userId,
                                      Map<Long, Note> noteIdMap,
                                      Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                      Map<Long, String> entityLabelMap,
                                      Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                      File dir) {
        // 创建JSONWriter对象
        FileWriter fileWriter = null;
        JSONWriter writer = null;
        try {
            String filename;
            String userName = "";
            if (userId != null) {
                userName = sysUserService.findNameById(userId) + "_";
            }
            if (batchId != null) {
                Batch batch = batchService.getById(batchId);
                filename = StrUtil.format("{}_{}_{}实体标注数据.json", projectName, batch.getName(), userName);
            } else {
                filename = StrUtil.format("{}_{}所有批次_实体标注数据.json", projectName, userName);
            }
            File file = FileUtil.file(dir, filename);
            // 创建文件
            FileUtil.mkParentDirs(file);
            fileWriter = new FileWriter(file);
            writer = new JSONWriter(fileWriter);
            // 设置json美化
            writer.config(SerializerFeature.PrettyFormat, true);
            // 开始json数组
            writer.startArray();
            // 根据批次来循环
            for (Long id : batchNoteTaskListMap.keySet()) {
                // 遍历批次下的文章
                List<NoteTask> noteTasks = batchNoteTaskListMap.get(id);
                for (NoteTask noteTask : noteTasks) {
                    Long taskId = noteTask.getTaskId();
                    Note note = noteIdMap.get(noteTask.getNoteId());
                    String articleId = note.getArticleId();

                    EntityAnnoJsonDTO entityJson = getEntityAnnoJsonDTO(entityLabelMap, entityIdAttrLabelMap, taskId, articleId);
                    if (entityJson == null) continue;

                    // 将数据写入到文件
                    writer.writeObject(entityJson);
                }
            }
            // 结束数组
            writer.endArray();
            writer.flush();
            // 读取 JSON 文件
            String s = FileUtil.readString(file, Charset.defaultCharset());

            // 格式化 JSON 数据
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(s);

            ObjectWriter ow = mapper.writerWithDefaultPrettyPrinter();
            String prettyJson = ow.writeValueAsString(jsonNode);

            // 输出格式化后的 JSON 数据
            FileUtil.writeString(prettyJson, file, Charset.defaultCharset());
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 关闭writer
            IoUtil.close(writer);
            IoUtil.close(fileWriter);
        }
    }

    private EntityAnnoJsonDTO getEntityAnnoJsonDTO(Map<Long, String> entityLabelMap, Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap, Long taskId, String articleId) {
        // 查询项目中的实体标注数据
        List<Entity> entityList = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.not_attr.getCode(), false);
        if (CollUtil.isEmpty(entityList)) {
            return null;
        }
        Map<String, Entity> attrEntityMap = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.is_attr.getCode(), false)
                .stream()
                .collect(Collectors.toMap(Entity::getId, x -> x));

        // 生成导出的单条记录的对象
        EntityAnnoJsonDTO entityJson = new EntityAnnoJsonDTO();
        // 设置articleId
        entityJson.setArticleId(articleId);

        List<EntityAnnoJsonDTO.Info> entities = new ArrayList<>();
        // 遍历所有的实体标注
        for (Entity entity : entityList) {
            // 获取实体的划词信息、属性信息
            EntityAnnoJsonDTO.Info info = getInfo(entity, attrEntityMap, entityLabelMap, entityIdAttrLabelMap);
            entities.add(info);
        }
        entityJson.setEntities(entities);
        return entityJson;
    }

    /**
     * 导出关系标注json数据
     */
    private void exportRelationJsonData(String projectName,
                                        Long batchId,
                                        Long userId,
                                        Map<Long, Note> noteIdMap,
                                        Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                        Map<Long, String> entityLabelMap,
                                        Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                        Map<Long, String> relationLabelMap,
                                        Map<Long, String> relationPatternMap,
                                        File dir) {
        FileWriter fileWriter = null;
        JSONWriter writer = null;
        try {
            String filename;
            String userName = "";
            if (userId != null) {
                userName = sysUserService.findNameById(userId) + "_";
            }
            if (batchId != null) {
                Batch batch = batchService.getById(batchId);
                filename = StrUtil.format("{}_{}_{}关系标注数据.json", projectName, batch.getName(), userName);
            } else {
                filename = StrUtil.format("{}_{}所有批次_关系标注数据.json", projectName, userName);
            }
            File file = FileUtil.file(dir, filename);
            // 创建文件
            FileUtil.mkParentDirs(file);
            FileUtil.touch(file);

            // 创建JSONWriter对象
            fileWriter = new FileWriter(file);
            writer = new JSONWriter(fileWriter);
            // 设置json美化
            writer.config(SerializerFeature.PrettyFormat, true);
            // 开始json数组
            writer.startArray();
            for (Long id : batchNoteTaskListMap.keySet()) {
                // 遍历批次下的文章
                List<NoteTask> noteTasks = batchNoteTaskListMap.get(id);
                for (NoteTask noteTask : noteTasks) {
                    Long taskId = noteTask.getTaskId();
                    Note note = noteIdMap.get(noteTask.getNoteId());
                    String articleId = note.getArticleId();
                    List<Relationship> relationList = relationshipRepository.findAllByTaskIdAndDeleted(taskId, false);
                    // 没有标注数据则跳过
                    if (CollUtil.isEmpty(relationList)) {
                        continue;
                    }

                    for (Relationship relationship : relationList) {
                        // 生成导出的单条记录的对象
                        RelationAnnoJsonDTO relationAnnoExportDTO = getRelationAnnoJsonDTO(entityLabelMap, entityIdAttrLabelMap, relationLabelMap, relationPatternMap, articleId, relationship);

                        // 将数据写入到文件
                        writer.writeObject(relationAnnoExportDTO);
                    }
                }
            }
            // 结束json数组
            writer.endArray();
            writer.flush();
            // 读取 JSON 文件
            String s = FileUtil.readString(file, Charset.defaultCharset());

            // 格式化 JSON 数据
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(s);

            ObjectWriter ow = mapper.writerWithDefaultPrettyPrinter();
            String prettyJson = ow.writeValueAsString(jsonNode);

            // 输出格式化后的 JSON 数据
            FileUtil.writeString(prettyJson, file, Charset.defaultCharset());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IoUtil.close(writer);
            IoUtil.close(fileWriter);
        }
    }

    private RelationAnnoJsonDTO getRelationAnnoJsonDTO(Map<Long, String> entityLabelMap, Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap, Map<Long, String> relationLabelMap, Map<Long, String> relationPatternMap, String articleId, Relationship relationship) {
        RelationAnnoJsonDTO relationAnnoExportDTO = new RelationAnnoJsonDTO();
        relationAnnoExportDTO.setArticleId(articleId);
        relationAnnoExportDTO.setPattern(relationPatternMap.get(relationship.getPatternId()));

        // 找到这条关系涉及的所有实体的id
        Set<String> entityIdSet = mergeEntityId(relationship);

        // 找到这条关系涉及的所有实体
        Map<String, Entity> entityMap = entityRepository.findAllByIdInAndDeleted(entityIdSet, false)
                .stream()
                .collect(Collectors.toMap(Entity::getId, x -> x));
        // 找到实体标注下面的属性标注的划词属性
        Map<String, Entity> attrEntityMap = entityRepository
                .findAllByIdInAndDeleted(attributesRepository.findAllEntityIdIn(entityIdSet).stream().map(Attributes::getAttributeId).collect(Collectors.toList()), false)
                .stream().collect(Collectors.toMap(Entity::getId, x -> x));

        // 遍历RelationItem
        List<RelationAnnoJsonDTO.RelationItem> relationItems = new ArrayList<>();
        for (Relationship.RelationItem item : relationship.getItems()) {
            RelationAnnoJsonDTO.RelationItem relationItem = new RelationAnnoJsonDTO.RelationItem();

            // order
            relationItem.setOrder(item.getOrder());
            // 主语
            boolean allSubjectIsEntity = item.getSubject().stream().allMatch(s -> s.length() == 32);
            if (allSubjectIsEntity) {
                relationItem.setSubject(item.getSubject().stream().map(x ->
                        getInfo(entityMap.get(x), attrEntityMap, entityLabelMap, entityIdAttrLabelMap)).collect(Collectors.toList()));
            } else {
                relationItem.setSubjectForeign(item.getSubject().get(0));
            }

            // 否定
            relationItem.setNegation(item.getNegation());
            // 关系批注
            if (CollUtil.isNotEmpty(item.getAnnotations())) {
                relationItem.setAnnotations(item.getAnnotations());
            }
            // 关系
            relationItem.setRelation(relationLabelMap.get(item.getRelation()));
            // 宾语
            boolean allObjectsIsEntity = item.getObjects().stream().allMatch(s -> s.length() == 32);
            if (allObjectsIsEntity) {
                relationItem.setObjects(item.getObjects().stream().map(x ->
                        getInfo(entityMap.get(x), attrEntityMap, entityLabelMap, entityIdAttrLabelMap)).collect(Collectors.toList()));
            } else {
                relationItem.setObjectsForeign(item.getObjects().get(0));
            }
            // 主宾是否同段落
            if (allSubjectIsEntity && allObjectsIsEntity) {
                List<EntityAnnoJsonDTO.EntityInfo> allEntityInfos = Stream.concat(
                        relationItem.getSubject().stream().flatMap(info -> info.getEntity().stream()),
                        relationItem.getObjects().stream().flatMap(info -> info.getEntity().stream())
                ).collect(Collectors.toList());
                boolean isSameTextId = allEntityInfos.stream().map(EntityAnnoJsonDTO.EntityInfo::getTextId)
                        .allMatch(x -> x.equals(allEntityInfos.get(0).getTextId()));
                relationItem.setSameTextId(isSameTextId);
            }
            relationItems.add(relationItem);
        }
        relationAnnoExportDTO.setItems(relationItems);
        return relationAnnoExportDTO;
    }

    /**
     * 获取标注的详细信息，包括划词信息以及属性信息
     */
    private EntityAnnoJsonDTO.Info getInfo(Entity entity,
                                           Map<String, Entity> attrEntityMap,
                                           Map<Long, String> entityLabelMap,
                                           Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap) {
        EntityMapper entityMapper = EntityMapper.INSTANCE;

        // 生成entityInfo
        EntityAnnoJsonDTO.Info info = new EntityAnnoJsonDTO.Info();
        info.setId(entity.getId());
        info.setLabelId(entity.getLabelId());
        // 设置实体标签名
        info.setLabel(entityLabelMap.get(entity.getLabelId()));
        // 设置实体划词详细
        info.setEntity(entityMapper.copyEntityInfo(entity.getEntityInfos()));

        // 设置annotation
        info.setAnnotation(entity.getAnnotate());
        // 设置conceptId
        info.setConceptId(entity.getConceptId());
        info.setConceptText(entity.getConceptText());

        // 获取这个实体标签下的属性标签
        Map<Long, AttributeLabel> attrLabelMap = entityIdAttrLabelMap.get(entity.getLabelId());

        // 查询当前实体下所有的属性标注数据
        Map<Long, List<Attributes>> attrMap = attributesRepository.findByEntityId(entity.getId())
                .stream()
                .collect(Collectors.groupingBy(Attributes::getAttrLabelId));

        // 生成属性标注数据
        List<EntityAnnoJsonDTO.AttrInfo> attribute = new ArrayList<>();
        for (Long attrLabelId : attrMap.keySet()) {
            EntityAnnoJsonDTO.AttrInfo attrInfo = new EntityAnnoJsonDTO.AttrInfo();
            AttributeLabel attributeLabel = attrLabelMap.get(attrLabelId);
            attrInfo.setAttrName(attributeLabel.getName());
            attrInfo.setAttrField(attributeLabel.getField());

            List<EntityAnnoJsonDTO.Attribute> attrs = new ArrayList<>();
            List<Attributes> list = attrMap.get(attrLabelId);
            for (Attributes attributes : list) {
                EntityAnnoJsonDTO.Attribute attr = new EntityAnnoJsonDTO.Attribute();
                // 如果属性标注是划词实体，则使用划词实体的数据
                if (StrUtil.isNotBlank(attributes.getAttributeId())) {
                    Entity attrEntity = attrEntityMap.getOrDefault(attributes.getAttributeId(), new Entity());
                    attr.setEntity(entityMapper.copyEntityInfo(attrEntity.getEntityInfos()));
                } else {
                    // 如果属性标注是自定义，则使用自定义属性的content
                    attr.setContent(attributes.getContent());
                }
                attrs.add(attr);
            }

            attrInfo.setAttrs(attrs);
            attribute.add(attrInfo);
        }
        if (CollUtil.isNotEmpty(attribute)) {
            info.setAttribute(attribute);
        }
        return info;
    }

    /**
     * 导出实体标注excel数据
     */
    private void exportEntityExcelData(Map<Long, Note> noteIdMap, Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                       Map<Long, String> entityLabelMap,
                                       Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                       Map<Long, String> userMap, File dir) {
        for (Long batchId : batchNoteTaskListMap.keySet()) {
            Batch batch = batchService.getById(batchId);
            File file = FileUtil.file(dir, batch.getName() + ".xlsx");
            List<NoteTask> noteTasks = batchNoteTaskListMap.get(batchId);
            ExcelWriter writer = null;
            try {
                ArrayList<EntityAnnoExcelDTO> entityExcelDTOs = new ArrayList<>();
                ArrayList<AttrAnnoExcelDTO> attrExcelDTOs = new ArrayList<>();
                for (NoteTask noteTask : noteTasks) {
                    Note note = noteIdMap.get(noteTask.getNoteId());
                    String articleId = note.getArticleId();
                    Long taskId = noteTask.getTaskId();
                    // 查询项目中的实体标注数据
                    List<Entity> entityList = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.not_attr.getCode(), false);
                    if (CollUtil.isEmpty(entityList)) {
                        continue;
                    }
                    // 查询项目中划词属性数据
                    Map<String, Entity> attrEntityMap = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(taskId, AttrAnnoEnum.is_attr.getCode(), false)
                            .stream()
                            .collect(Collectors.toMap(Entity::getId, x -> x));

                    for (Entity entity : entityList) {
                        addEntityAnnoExcelDTO(entity, attrEntityMap, entityLabelMap, entityIdAttrLabelMap, userMap, entityExcelDTOs, attrExcelDTOs);
                    }

                }
                writer = ExcelUtil.getWriter(file);
                writer.renameSheet("实体");
                HutoolExcelUtil.setHeaderAlias(writer, EntityAnnoExcelDTO.class);
                writer.write(objectListToMapListWithJSON(entityExcelDTOs, EntityAnnoExcelDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), EntityAnnoExcelDTO.class.getDeclaredFields().length - 1);

                // 写入属性
                writer.setSheet("属性");
                HutoolExcelUtil.setHeaderAlias(writer, AttrAnnoExcelDTO.class);
                writer.write(objectListToMapListWithJSON(attrExcelDTOs, AttrAnnoExcelDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), AttrAnnoExcelDTO.class.getDeclaredFields().length - 1);

            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                IoUtil.close(writer);
            }
        }
    }

    /**
     * 导出关系标注excel数据
     */
    private void exportRelationExcelData(Map<Long, List<NoteTask>> batchNoteTaskListMap,
                                         Map<Long, String> entityLabelMap,
                                         Map<Long, Note> noteIdMap, Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                         Map<Long, String> relationLabelMap, Map<Long, String> relationPatternMap,
                                         Map<Long, String> userMap,
                                         File dir) {
        for (Long batchId : batchNoteTaskListMap.keySet()) {
            Batch batch = batchService.getById(batchId);
            File file = FileUtil.file(dir, batch.getName() + ".xlsx");
            List<NoteTask> noteTasks = batchNoteTaskListMap.get(batchId);
            ExcelWriter writer = null;
            try {
                ArrayList<RelationAnnoExcelDTO> relationExcelDTOs = new ArrayList<>();
                ArrayList<EntityAnnoExcelDTO> entityExcelDTOs = new ArrayList<>();
                ArrayList<AttrAnnoExcelDTO> attrExcelDTOs = new ArrayList<>();
                for (NoteTask noteTask : noteTasks) {

                    Note note = noteIdMap.get(noteTask.getNoteId());
                    String articleId = note.getArticleId();
                    // 查询项目中的实体标注数据
                    List<Relationship> relationList = relationshipRepository.findAllByTaskIdAndDeleted(noteTask.getTaskId(), false);

                    if (CollUtil.isEmpty(relationList)) {
                        continue;
                    }


                    for (Relationship relationship : relationList) {
                        // 找到这条关系涉及的所有实体id
                        Set<String> entityIdSet = mergeEntityId(relationship);

                        // 找到这条关系涉及的所有实体
                        Map<String, Entity> entityMap = entityRepository.findAllByIdInAndDeleted(entityIdSet, false)
                                .stream()
                                .collect(Collectors.toMap(Entity::getId, x -> x));

                        // 找到实体标注下面的属性标注的划词属性
                        Map<String, Entity> attrEntityMap = entityRepository
                                .findAllByIdInAndDeleted(attributesRepository.findAllEntityIdIn(entityIdSet).stream().map(Attributes::getAttributeId).collect(Collectors.toList()), false)
                                .stream().collect(Collectors.toMap(Entity::getId, x -> x));

                        for (Relationship.RelationItem item : relationship.getItems()) {
                            // 关系sheet一行的数据
                            RelationAnnoExcelDTO excelDTO = new RelationAnnoExcelDTO();
                            // 文章编号
                            excelDTO.setArticleId(articleId);
                            // 关系id
                            excelDTO.setId(relationship.getId());
                            // 序号
                            excelDTO.setOrder(item.getOrder());

                            // 主语
                            List<String> subject = item.getSubject();
                            // 判断是主语是实体还是外键
                            boolean allSubjectIsEntity = subject.stream().allMatch(s -> s.length() == 32);
                            if (allSubjectIsEntity) {
                                List<String> subjectContents = subject.stream()
                                        .map(x -> entityMap.get(x).getEntityInfos().stream()
                                                .map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")))
                                        .collect(Collectors.toList());
                                excelDTO.setSubject(JSON.toJSONString(subjectContents));
                                subject.forEach(x -> addEntityAnnoExcelDTO(entityMap.get(x), attrEntityMap, entityLabelMap, entityIdAttrLabelMap, userMap, entityExcelDTOs, attrExcelDTOs));
                            } else {
                                String[] split = subject.get(0).split("#");
                                excelDTO.setSubject(StrUtil.format("引用：{}的{}",
                                        split[0],
                                        split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                ));
                                if (split.length == 1) {
                                    excelDTO.setSubject(StrUtil.format("引用：{}", split[0]));
                                }
                            }

                            // 宾语
                            List<String> objects = item.getObjects();
                            // 判断是宾语是实体还是外键
                            boolean allObjectsIsEntity = objects.stream().allMatch(s -> s.length() == 32);
                            if (allObjectsIsEntity) {
                                List<String> objectContents = objects.stream()
                                        .map(x -> entityMap.get(x).getEntityInfos().stream()
                                                .map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")))
                                        .collect(Collectors.toList());
                                excelDTO.setObjects(JSON.toJSONString(objectContents));
                                objects.forEach(x -> addEntityAnnoExcelDTO(entityMap.get(x), attrEntityMap, entityLabelMap, entityIdAttrLabelMap, userMap, entityExcelDTOs, attrExcelDTOs));
                            } else {
                                String[] split = objects.get(0).split("#");
                                excelDTO.setObjects(StrUtil.format("引用：{}的{}",
                                        split[0],
                                        split.length > 1 && split[1].equals("subject") ? "主语" : "宾语"
                                ));
                                if (split.length == 1) {
                                    excelDTO.setObjects(StrUtil.format("引用：{}", split[0]));
                                }
                            }
                            excelDTO.setRelation(relationLabelMap.get(item.getRelation()));
                            excelDTO.setNegation(item.getNegation());
                            if (CollUtil.isNotEmpty(item.getAnnotations())) {
                                excelDTO.setAnnotations(JSON.toJSONString(item.getAnnotations()));
                            }

                            // 判断主语宾语是不是同一个段落
                            if (allSubjectIsEntity && allObjectsIsEntity) {
                                List<String> subjectsAndObjects = new ArrayList<>(item.getSubject());
                                subjectsAndObjects.addAll(item.getObjects());

                                List<AnnoDTO.EntityInfo> allEntityInfos = subjectsAndObjects.stream()
                                        .map(entityMap::get)
                                        .flatMap(entity -> entity.getEntityInfos().stream())
                                        .collect(Collectors.toList());

                                boolean isSameTextId = allEntityInfos.stream()
                                        .map(AnnoDTO.EntityInfo::getTextId)
                                        .distinct()
                                        .count() == 1;
                                excelDTO.setSameTextId(isSameTextId);
                            }
                            // 主语id
                            excelDTO.setSubjectIds(item.getSubject());
                            // 宾语id
                            excelDTO.setObjectIds(item.getObjects());
                            // 标注人
                            excelDTO.setUserName(relationship.getAuditorId() != null
                                    ? userMap.get(relationship.getAuditorId()) : userMap.get(relationship.getAnnotatorId()));
                            // 标注时间
                            excelDTO.setCreateTime(relationship.getCreateTime());
                            relationExcelDTOs.add(excelDTO);
                        }
                    }
                }
                // 写入文件
                writer = ExcelUtil.getWriter(file);

                // 写入关系
                writer.renameSheet("关系");
                HutoolExcelUtil.setHeaderAlias(writer, RelationAnnoExcelDTO.class);
                writer.write(relationExcelDTOs, true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), RelationAnnoExcelDTO.class.getDeclaredFields().length - 1);

                // 写入属性
                writer.setSheet("实体");
                HutoolExcelUtil.setHeaderAlias(writer, EntityAnnoExcelDTO.class);
                writer.write(objectListToMapListWithJSON(entityExcelDTOs, EntityAnnoExcelDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), EntityAnnoExcelDTO.class.getDeclaredFields().length - 1);


                // 写入属性
                writer.setSheet("属性");
                HutoolExcelUtil.setHeaderAlias(writer, AttrAnnoExcelDTO.class);
                writer.write(objectListToMapListWithJSON(attrExcelDTOs, AttrAnnoExcelDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), AttrAnnoExcelDTO.class.getDeclaredFields().length - 1);
                writer.flush();
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                IoUtil.close(writer);
            }
        }
    }


    /**
     * 添加实体和属性的数据excel
     */
    private void addEntityAnnoExcelDTO(Entity entity,
                                       Map<String, Entity> attrEntityMap,
                                       Map<Long, String> entityLabelMap,
                                       Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap,
                                       Map<Long, String> userMap,
                                       List<EntityAnnoExcelDTO> entityExcelDTOs,
                                       List<AttrAnnoExcelDTO> attrExcelDTOs) {
        EntityMapper entityMapper = EntityMapper.INSTANCE;
        EntityAnnoExcelDTO entityExcelDTO = new EntityAnnoExcelDTO();
        // 实体id
        entityExcelDTO.setId(entity.getId());
        // 文章编号
        entityExcelDTO.setArticleId(entity.getArticleId());
        // 实体标签名
        entityExcelDTO.setLabelName(entityLabelMap.get(entity.getLabelId()));
        // 实体信息
        entityExcelDTO.setEntityInfos(entityMapper.copyEntityInfo(entity.getEntityInfos()));
        // 实体标注人
        entityExcelDTO.setUserName(entity.getAuditorId() != null
                ? userMap.get(entity.getAuditorId()) : userMap.get(entity.getAnnotatorId()));
        // 实体标注时间
        entityExcelDTO.setCreateTime(entity.getCreateTime());
        // conceptId
        entityExcelDTO.setConceptId(entity.getConceptId());
        entityExcelDTO.setConceptText(entity.getConceptText());

        // 找到实体下的所有属性
        List<Attributes> attributesList = attributesRepository.findByEntityId(entity.getId());
        Map<Long, List<Attributes>> attrMap = attributesList.stream()
                .collect(Collectors.groupingBy(Attributes::getAttrLabelId, LinkedHashMap::new, Collectors.toList()));

        // 找到实体标签下的属性标签
        Map<Long, AttributeLabel> attrLabelMap = entityIdAttrLabelMap.get(entity.getLabelId());

        // 属性简要信息
        Map<String, List<String>> attr = new LinkedHashMap<>();

        // 属性详细信息
        List<EntityAnnoExcelDTO.AttrInfo> attrInfos = new ArrayList<>();

        for (Long key : attrMap.keySet()) {
            List<Attributes> value = attrMap.get(key);
            AttributeLabel attributeLabel = attrLabelMap.get(key);

            attr.put(attributeLabel.getName(), value.stream().map(Attributes::getContent).collect(Collectors.toList()));

            for (Attributes attributes : value) {

                EntityAnnoExcelDTO.AttrInfo attrInfo = new EntityAnnoExcelDTO.AttrInfo();
                attrInfo.setAttrId(attributes.getId());
                attrInfo.setAttrName(attributeLabel.getName());
                attrInfo.setAttrContent(attributes.getContent());
                attrInfos.add(attrInfo);

                AttrAnnoExcelDTO attrAnnoExcelDTO = new AttrAnnoExcelDTO();
                attrAnnoExcelDTO.setArticleId(attributes.getArticleId());
                // 属性id
                attrAnnoExcelDTO.setId(attributes.getId());
                // 属性名
                attrAnnoExcelDTO.setAttrName(attributeLabel.getName());
                // 属性字段
                attrAnnoExcelDTO.setAttrField(attributeLabel.getField());

                // 是标注属性还是自填属性
                if (StrUtil.isNotBlank(attributes.getAttributeId())) {
                    Entity attrEntity = attrEntityMap.getOrDefault(attributes.getAttributeId(), new Entity());
                    attrAnnoExcelDTO.setContent(JSON.toJSONString(entityMapper.copyEntityInfo(attrEntity.getEntityInfos())));
                    attrAnnoExcelDTO.setType("标注属性");
                } else {
                    attrAnnoExcelDTO.setContent(attributes.getContent());
                    attrAnnoExcelDTO.setType("自填属性");
                }
                // 属性标注人
                attrAnnoExcelDTO.setUserName(attributes.getAuditorId() != null
                        ? userMap.get(attributes.getAuditorId()) : userMap.get(attributes.getAnnotatorId()));
                // 属性创建时间
                attrAnnoExcelDTO.setCreateTime(attributes.getCreateTime());
                attrExcelDTOs.add(attrAnnoExcelDTO);
            }
        }
        if (CollUtil.isNotEmpty(attr)) {
            entityExcelDTO.setAttr(attr);
        }
        if (CollUtil.isNotEmpty(attrInfos)) {
            entityExcelDTO.setAttrInfos(attrInfos);
        }

        entityExcelDTOs.add(entityExcelDTO);
    }

    @Override
    public void sendDownloadMail(String exportLogId) {
        ExportLog exportLog = exportLogService.getById(exportLogId);
        if (exportLog.getStatus().equals(ExportStatusEnum.finish.getCode())) {
            sendFinishedEmail(exportLog);
        }
        if (exportLog.getStatus().equals(ExportStatusEnum.fail.getCode())) {
            sendFailedEmail(exportLog);
        }
    }

    @Override
    public EntityAnnoJsonDTO getEntityJson(NoteTask noteTask) {
        Note note = noteService.getById(noteTask.getNoteId());
        // 实体标签
        Map<Long, String> entityLabelMap = entityLabelService.findAllEnableByProjectId(noteTask.getProjectId())
                .stream()
                .collect(Collectors.toMap(EntityLabel::getId, EntityLabel::getName));
        // 属性标签
        Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.keySet())
                .stream()
                .collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getId, x -> x)));

        return getEntityAnnoJsonDTO(entityLabelMap, entityIdAttrLabelMap, noteTask.getTaskId(), note.getArticleId());
    }


    @Override
    public List<EntityAnnoJsonDTO> getEntityJsonList(List<NoteTask> noteTaskList) {
        return noteTaskList.stream().map(this::getEntityJson).collect(Collectors.toList());
    }

    @Override
    public EntityAnnoJsonDTO.Info getEntityJsonInfo(Entity entity) {
        Map<Long, String> entityLabelMap = entityLabelService.findAllEnableByProjectId(entity.getProjectId())
                .stream()
                .collect(Collectors.toMap(EntityLabel::getId, EntityLabel::getName));
        // 属性标签
        Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.keySet())
                .stream()
                .collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getId, x -> x)));
        Map<String, Entity> attrEntityMap = entityRepository.findAllByTaskIdAndIsAttrAndDeleted(entity.getTaskId(), AttrAnnoEnum.is_attr.getCode(), false)
                .stream()
                .collect(Collectors.toMap(Entity::getId, x -> x));

        return getInfo(entity, attrEntityMap, entityLabelMap, entityIdAttrLabelMap);
    }

    @Override
    public List<RelationAnnoJsonDTO> getRelationJson(NoteTask noteTask) {
        Note note = noteService.getById(noteTask.getNoteId());
        Long projectId = note.getProjectId();
        // 实体标签
        Map<Long, String> entityLabelMap = entityLabelService.findAllEnableByProjectId(projectId)
                .stream()
                .collect(Collectors.toMap(EntityLabel::getId, EntityLabel::getName));
        // 属性标签
        Map<Long, Map<Long, AttributeLabel>> entityIdAttrLabelMap = attributeLabelService.findAllEnableByEntityLabelIdIn(entityLabelMap.keySet())
                .stream()
                .collect(Collectors.groupingBy(AttributeLabel::getEntityLabelId, Collectors.toMap(AttributeLabel::getId, x -> x)));
        // 关系标签
        Map<Long, String> relationLabelMap = relationLabelService.findAllEnableByProjectId(projectId)
                .stream()
                .collect(Collectors.toMap(RelationLabel::getId, RelationLabel::getName));
        // 关系模板
        Map<Long, String> relationPatternMap = relationPatternService.findAllEnableByProjectId(projectId)
                .stream()
                .collect(Collectors.toMap(RelationPattern::getId, RelationPattern::getName));
        List<Relationship> relationList = relationshipRepository.findAllByTaskIdAndDeleted(noteTask.getTaskId(), false);
        // 没有标注数据则跳过
        if (CollUtil.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<RelationAnnoJsonDTO> list = relationList.stream().map(x -> getRelationAnnoJsonDTO(entityLabelMap, entityIdAttrLabelMap, relationLabelMap, relationPatternMap, note.getArticleId(), x)).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<RelationAnnoJsonDTO> getRelationJsonBatch(List<NoteTask> noteTaskList) {
        return noteTaskList.stream()
                .flatMap(task -> this.getRelationJson(task).stream())
                .collect(Collectors.toList());
    }

    @Override
    public ExportLog findExportLog(Long projectId) {
        LambdaQueryWrapper<ExportLog> logLambdaQueryWrapper = new LambdaQueryWrapper<>();
        logLambdaQueryWrapper.eq(ExportLog::getProjectId, projectId).orderByDesc(ExportLog::getCreateTime).last("limit 1");
        return exportLogService.getOne(logLambdaQueryWrapper);
    }

    private void sendFinishedEmail(ExportLog exportLog) {
        String email = exportLog.getEmail();
        if (StrUtil.isBlank(email)) {
            return;
        }
        String temp = "您于 {} 在BNLP系统提交的 {}_{} 数据导出任务已完成,数据文件点击链接下载:" + downloadHost + "/api/download/export?id={} ,若在邮箱中打不开，请复制到浏览器打开";
        Project project = projectService.getById(exportLog.getProjectId());
        String projectName = project.getName();
        String batchName;
        if (exportLog.getBatchId() != null) {
            Batch batch = batchService.getById(exportLog.getBatchId());
            batchName = batch.getName();
        } else {
            batchName = "所有批次";
        }
        String content = StrUtil.format(temp, DateUtil.format(exportLog.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), projectName, batchName, exportLog.getId());
        try {
            mailService.sendSimpleMail(email, "标注数据导出成功", content);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private void sendFailedEmail(ExportLog exportLog) {
        String temp = "您于 {} 在BNLP系统提交的 {}_{} 数据导出任务失败,请重试,多次导出失败请咨询管理员";
        Project project = projectService.getById(exportLog.getProjectId());
        String projectName = project.getName();
        String batchName;
        if (exportLog.getBatchId() != null) {
            Batch batch = batchService.getById(exportLog.getBatchId());
            batchName = batch.getName();
        } else {
            batchName = "所有批次";
        }
        String content = StrUtil.format(temp, DateUtil.format(exportLog.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), projectName, batchName, exportLog.getId());
        try {
            mailService.sendSimpleMail(exportLog.getEmail(), "标注数据导出失败", content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 将需要导出的excel数据中的复杂对象字段转为json,目前包含List、Map、Date
     */
    @SneakyThrows
    public static List<Map<String, Object>> objectListToMapListWithJSON(List<?> data, Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<Map<String, Object>> dataListMap = data.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        for (Map<String, Object> dataMap : dataListMap) {
            for (Field field : fields) {
                String fieldName = field.getName();
                if (fieldName.equals("serialVersionUID")) {
                    continue;
                }
                Object o = dataMap.get(fieldName);
                if (o instanceof List) {
                    dataMap.put(fieldName, JSON.toJSONString(o));
                }
                if (o instanceof Map) {
                    dataMap.put(fieldName, JSON.toJSONString(o));
                }
                if (o instanceof Date) {
                    dataMap.put(fieldName, DateUtil.formatDateTime((Date) o));
                }
            }
        }
        return dataListMap;
    }
}
