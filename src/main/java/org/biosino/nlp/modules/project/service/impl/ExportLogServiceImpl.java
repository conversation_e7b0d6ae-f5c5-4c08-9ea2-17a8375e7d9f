package org.biosino.nlp.modules.project.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.ExportStatusEnum;
import org.biosino.nlp.modules.project.dao.ExportLogDao;
import org.biosino.nlp.modules.project.entity.ExportLog;
import org.biosino.nlp.modules.project.service.DataExportService;
import org.biosino.nlp.modules.project.service.ExportLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * ImportLog业务层
 *
 * <AUTHOR> <PERSON>
 * @date 2023/5/24
 */
@Service
@Slf4j
public class ExportLogServiceImpl extends ServiceImpl<ExportLogDao, ExportLog> implements ExportLogService {

    @Autowired
    private DataExportService dataExportService;

    @PostConstruct
    private void handleUnfinishedExport() {
        List<ExportLog> list = this.list(Wrappers.<ExportLog>lambdaQuery()
                .in(ExportLog::getStatus, ExportStatusEnum.wait.getCode(), ExportStatusEnum.processing.getCode()));
        for (ExportLog exportLog : list) {
            exportLog.setStatus(ExportStatusEnum.fail.getCode());
            this.saveOrUpdate(exportLog);
            dataExportService.sendDownloadMail(exportLog.getId());
        }
    }

    @Override
    public boolean existsUnfinishedByProject(Long projectId, Long userId) {
        return this.count(Wrappers.<ExportLog>lambdaQuery().eq(ExportLog::getProjectId, projectId)
                .in(ExportLog::getStatus, ExportStatusEnum.wait.getCode(), ExportStatusEnum.processing.getCode())
                .eq(ExportLog::getUserId, userId)) > 0;
    }
}
