package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 实体导出类
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class EntityAnnoExcelDTO implements Serializable {

    private static final long serialVersionUID = -8360371506484686672L;

    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "实体ID", order = 2)
    private String id;

    @ExcelProperty(value = "实体标签", order = 3)
    private String labelName;

    @ExcelProperty(value = "实体内容", order = 4)
    private List<EntityAnnoJsonDTO.EntityInfo> entityInfos;

    @ExcelProperty(value = "属性", order = 5)
    private Map<String, List<String>> attr;

    @ExcelProperty(value = "属性详情", order = 6)
    private List<AttrInfo> attrInfos;

    @ExcelProperty(value = "conceptId", order = 7)
    private String conceptId;

    @ExcelProperty(value = "conceptText", order = 8)
    private String conceptText;

    @ExcelProperty(value = "标注人", order = 9)
    private String userName;

    @ExcelProperty(value = "标注时间", order = 10)
    private Date createTime;

    @Data
    public static class AttrInfo implements Serializable {
        private static final long serialVersionUID = 4296912980387015731L;
        @JSONField(name = "attr_id")
        private String attrId;
        @JSONField(name = "attr_name")
        private String attrName;
        @JSONField(name = "attr_content")
        private String attrContent;
    }
}
