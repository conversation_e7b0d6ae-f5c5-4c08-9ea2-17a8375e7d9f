package org.biosino.nlp.modules.project.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-12-04 15:26
 */
@Data
public class BaseDTO {

    private Integer page = 0;
    private Integer limit = 10;

    private String orderBy;
    private Boolean isAsc;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 用于前端上一页、下一页翻页用
     */
    private Boolean forPageIds = false;

}
