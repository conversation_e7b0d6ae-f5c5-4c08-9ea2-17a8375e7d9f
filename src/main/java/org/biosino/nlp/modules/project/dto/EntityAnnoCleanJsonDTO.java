package org.biosino.nlp.modules.project.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Data
public class EntityAnnoCleanJsonDTO implements Serializable {

    private static final long serialVersionUID = 7970456296975573200L;

    @JSONField(name = "article_id", ordinal = 1)
    private String articleId;

    private String content;

    @JSONField(ordinal = 2)
    private List<EntityItem> entities;

    @Data
    public static class EntityItem implements Serializable {

        private String label;

        private String content;

    }
}
