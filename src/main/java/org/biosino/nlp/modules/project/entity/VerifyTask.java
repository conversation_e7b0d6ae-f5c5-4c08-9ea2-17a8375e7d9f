package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 数据验证任务
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
@TableName("t_verify_task")
public class VerifyTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long projectId;

    private Long preSourceId;
    private Long batchId;

    private String taskName;

    private String description;

    private Integer status;
    private Integer method;

    private Long creator;
    private Date createTime;
    private Date updateTime;

    private Date finishTime;

    /**
     * 是否删除[1.已删除值  0.未删除]
     */
    @TableLogic
    private Integer deleted;


}
