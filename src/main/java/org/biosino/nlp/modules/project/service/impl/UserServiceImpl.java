package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.modules.project.service.UserService;
import org.biosino.nlp.modules.sys.dao.SysUserDao;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.entity.SysUserRoleEntity;
import org.biosino.nlp.modules.sys.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:57
 */
@Service
public class UserServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements UserService {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Override
    public List<SysUserEntity> queryUsers() {
        List<SysUserRoleEntity> userRoleIds = sysUserRoleService.list();
        Map<Long, List<Long>> roleMap = new HashMap<>(128);
        for (SysUserRoleEntity userRoleId : userRoleIds) {
            Long userId = userRoleId.getUserId();
            Long roleId = userRoleId.getRoleId();
            if (roleMap.containsKey(userId)) {
                List<Long> longs = roleMap.get(userId);
                longs.add(roleId);
                roleMap.put(userId, longs);
            } else {
                roleMap.put(userId, CollUtil.newArrayList(roleId));
            }
        }
        List<SysUserEntity> userList = this.list(Wrappers.<SysUserEntity>lambdaQuery()
                .eq(SysUserEntity::getStatus, StatusEnum.enable.getValue()));

        userList = userList.stream().peek(it -> {
            it.setRoleIdList(roleMap.get(it.getUserId()));
        }).collect(Collectors.toList());
        return userList;
    }
}
