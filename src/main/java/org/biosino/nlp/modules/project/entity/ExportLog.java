package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导入日志记录表
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Data
@TableName("t_export_log")
public class ExportLog implements Serializable {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private Long projectId;

    private Long batchId;

    private Integer module;

    private Integer type;

    private String email;

    private Date createTime;

    private Date finishTime;

    private Integer status;

    private String outputFilePath;

    private Long userId;

    @TableLogic
    private Integer deleted;

}
