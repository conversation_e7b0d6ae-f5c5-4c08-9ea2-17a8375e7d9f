package org.biosino.nlp.modules.project.dto;

import lombok.Data;
import org.biosino.nlp.common.annotation.ExcelProperty;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
public class PreAnnoExcelDTO {

    @ExcelProperty(value = "文章编号", order = 1)
    private String articleId;

    @ExcelProperty(value = "段落编号", order = 2)
    private String textId;

    @ExcelProperty(value = "实体标签", order = 3)
    private String type;

    @ExcelProperty(value = "实体内容", order = 4)
    private String name;

    @ExcelProperty(value = "实体起始位", order = 5)
    private String start;

    @ExcelProperty(value = "实体结束位", order = 6)
    private String end;

    @ExcelProperty(value = "实体属性", order = 7)
    private String attr;

    @ExcelProperty(value = "实体属性详情", order = 8)
    private String attrInfo;
}
