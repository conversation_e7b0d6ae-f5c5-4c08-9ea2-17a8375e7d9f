package org.biosino.nlp.modules.project.service;

import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.project.dto.EntityAnnoJsonDTO;
import org.biosino.nlp.modules.project.dto.ExportDTO;
import org.biosino.nlp.modules.project.dto.RelationAnnoJsonDTO;
import org.biosino.nlp.modules.project.entity.ExportLog;

import java.io.File;
import java.io.IOException;
import java.util.List;

public interface DataExportService {

    /**
     * 生成导出任务
     */
    void generateExportLog(ExportDTO dto) throws IOException;

    /**
     * 发送下载通知邮件
     */
    void sendDownloadMail(String exportLogId);

    EntityAnnoJsonDTO getEntityJson(NoteTask noteTask);

    List<EntityAnnoJsonDTO> getEntityJsonList(List<NoteTask> noteTaskList);

    EntityAnnoJsonDTO.Info getEntityJsonInfo(Entity entity);

    List<RelationAnnoJsonDTO> getRelationJson(NoteTask noteTask);

    List<RelationAnnoJsonDTO> getRelationJsonBatch(List<NoteTask> noteTaskList);

    ExportLog findExportLog(Long projectId);

    /**
     * 导出标注数据
     * 
     * @param dto 导出参数
     * @param noteList 文献列表
     * @param noteTaskList 任务列表
     * @param userId 用户ID
     * @param dir 导出目录
     */
    void exportAnnoData(ExportDTO dto, List<Note> noteList, List<NoteTask> noteTaskList, Long userId, File dir);
}
