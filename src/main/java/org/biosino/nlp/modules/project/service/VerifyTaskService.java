package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.project.dto.VerifyLabelDTO;
import org.biosino.nlp.modules.project.dto.VerifyTaskDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.entity.VerifyLabel;
import org.biosino.nlp.modules.project.entity.VerifyTask;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数据验证
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
public interface VerifyTaskService extends IService<VerifyTask> {

    PageUtils queryPage(VerifyTaskDTO dto);

    void saveOrUpdate(VerifyTask task, Long creatorId);

    void uploadData(MultipartFile file, Long projectId);

    VerifyTask getTaskById(Long taskId);

    void deleteById(Long taskId);

    List<VerifyLabel> queryLabel(Long verifyTaskId, Integer type);

    List<ImportLog> getPreSourceList(Long projectId);

    void calculation(Batch batch, VerifyTask verifyTask);

    List<VerifyLabel> queryLabelByOther(Long verifyLabelId);
}
