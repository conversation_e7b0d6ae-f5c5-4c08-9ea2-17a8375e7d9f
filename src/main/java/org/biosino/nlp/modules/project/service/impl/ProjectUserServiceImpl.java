package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.dao.ProjectUserDao;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.service.ProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProjectUserServiceImpl extends ServiceImpl<ProjectUserDao, ProjectUser> implements ProjectUserService {
    @Autowired
    private ProjectDao projectDao;

    @Override
    public List<ProjectUser> queryUsers(Long projectId) {
        final List<ProjectUser> projectUserList = this.list(Wrappers.<ProjectUser>lambdaQuery().eq(ProjectUser::getProjectId, projectId));
        if (CollUtil.isNotEmpty(projectUserList)) {
            Project project = projectDao.selectById(projectId);
            if (project != null) {
                for (ProjectUser projectUser : projectUserList) {
                    projectUser.setMarkRounds(project.getMarkRounds());
                    projectUser.setAutoReview(project.getAutoReview());
                }
            } else {
                throw new RRException("项目不已存在");
            }
        }

        return projectUserList;
    }

    @Override
    public List<ProjectUser> queryProjectByUser(Long userId) {
        return this.list(Wrappers.<ProjectUser>lambdaQuery().eq(ProjectUser::getUserId, userId));
    }

    @Override
    public Integer queryProjectAnnotatorNum(Long projectId) {
        return this.count(Wrappers.<ProjectUser>lambdaQuery().eq(ProjectUser::getProjectId, projectId).eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));
    }
}
