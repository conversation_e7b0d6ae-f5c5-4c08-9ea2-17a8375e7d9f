package org.biosino.nlp.modules.project.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.project.dto.VerifyTaskDTO;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.entity.VerifyLabel;
import org.biosino.nlp.modules.project.entity.VerifyTask;
import org.biosino.nlp.modules.project.service.VerifyTaskService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数据验证
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@RestController
@RequestMapping("/verify")
public class VerifyTaskController extends AbstractController {

    @Autowired
    private VerifyTaskService verifyTaskService;

    /**
     * 数据验证列表
     */
    @RequestMapping("/list")
    public R list(VerifyTaskDTO dto) {
        PageUtils page = verifyTaskService.queryPage(dto);
        return R.ok().put("page", page);
    }

    @SysLog("保存数据验证任务")
    @RequestMapping("/save")
    @RequiresPermissions("project:manage")
    public R save(@RequestBody VerifyTask verifyTask) {
        verifyTaskService.saveOrUpdate(verifyTask, getUserId());
        return R.ok();
    }

    /**
     * 单条任务详细信息
     */
    @RequestMapping("/getTaskById/{taskId}")
    public R getTaskById(@PathVariable Long taskId) {
        VerifyTask verifyTask = verifyTaskService.getTaskById(taskId);
        return R.success(verifyTask);
    }

    /**
     * 查询预标注数据版本
     */
    @RequestMapping("/getPreSourceList/{projectId}")
    public R getPreSourceList(@PathVariable Long projectId) {
        List<ImportLog> importLog = verifyTaskService.getPreSourceList(projectId);
        return R.success(importLog);
    }

    /**
     * 已废弃
     */
    @SysLog("导入数据验证文件")
    @RequestMapping("/uploadData")
    public R uploadData(MultipartFile file, Long taskId) {
        verifyTaskService.uploadData(file, taskId);
        return R.ok();
    }

    @SysLog("删除批次")
    @RequestMapping("/delete")
    @RequiresPermissions("project:manage")
    public R delete(Long taskId) {
        verifyTaskService.deleteById(taskId);
        return R.ok();
    }

    /**
     * 数据验证标签列表
     */
    @RequestMapping("/label/list")
    public R labelList(Long verifyTaskId, Integer type) {
        List<VerifyLabel> verifyLabels = verifyTaskService.queryLabel(verifyTaskId, type);
        return R.success(verifyLabels);
    }

    /**
     * 数据验证标签 相关版本数据列表
     */
    @RequestMapping("/label/listByOther")
    public R listByOther(Long verifyLabelId) {
        List<VerifyLabel> verifyLabels = verifyTaskService.queryLabelByOther(verifyLabelId);
        return R.success(verifyLabels);
    }
}
