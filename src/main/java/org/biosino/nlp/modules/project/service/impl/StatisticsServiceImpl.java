package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mongodb.BasicDBObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.biosino.nlp.common.enums.AttrAnnoEnum;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.MathUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.dao.StatisticsDao;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.entity.Statistics;
import org.biosino.nlp.modules.project.entity.mongo.AnnoKappaAlpha;
import org.biosino.nlp.modules.project.entity.mongo.AnnoStatistics;
import org.biosino.nlp.modules.project.service.*;
import org.biosino.nlp.modules.project.vo.*;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-5-24
 */
@Service
@Slf4j
public class StatisticsServiceImpl extends ServiceImpl<StatisticsDao, Statistics> implements StatisticsService {
    @Autowired
    private NoteDao noteDao;
    @Autowired
    private NoteService noteService;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private BatchService batchService;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private MongoOperations mongoOperations;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private EntityLabelService entityLabelService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MailService mailService;

    private static final String NO_ANNO = "no_annotation";
    // 添加防重复提交机制
//    private static final Set<String> runningTasks = ConcurrentHashMap.newKeySet();

    /**
     * 项目概要统计
     */
    @Override
    public StatisticsVO overview(Long projectId, Long roleId, Long userId) {
        StatisticsVO vo = new StatisticsVO();
        // 文章总数
        vo.setTotal(countNoteStep(projectId, null));
        // 已验收
        vo.setReviewed(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.reviewed.getCode())));
        // 标注员
        if (RoleEnum.annotator.getId() == roleId) {
            // 当前标注员待标注任务数
            vo.setUnmarked(noteTaskDao.countUnmarkedTask(projectId, userId));
            vo.setNoting(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.noting.getCode())));
            // 标注员已标注完成待审核
            vo.setUnReview(countTaskStep(projectId, userId, roleId, Arrays.asList(NoteStepEnum.marked.getCode(), NoteStepEnum.reviewing.getCode())));
            vo.setRepulse(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.repulse.getCode())));
        }
        // 审核员
        else if (RoleEnum.auditor.getId() == roleId) {
            // 当前审核员待审核的任务数
            vo.setUnReview(noteTaskDao.countUnreviewTask(projectId, userId));
            vo.setReviewing(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.reviewing.getCode())));
            vo.setRepulse(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.repulse.getCode())));
            vo.setCorrected(countTaskStep(projectId, userId, roleId, CollUtil.newArrayList(NoteStepEnum.corrected.getCode())));
        }
        // 其他人如管理员和观察员
        else {
            // 待标注
            vo.setUnmarked(countNoteStep(projectId, CollUtil.newArrayList(NoteStepEnum.unmarked.getCode())));
            // 标注中
            vo.setNoting(countNoteStep(projectId, CollUtil.newArrayList(NoteStepEnum.noting.getCode(), NoteStepEnum.marked.getCode(),
                    NoteStepEnum.reviewing.getCode())));
            vo.setReviewed(countNoteStep(projectId, CollUtil.newArrayList(NoteStepEnum.reviewed.getCode())));
            // 已废弃
            LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                    .eq(Note::getProjectId, projectId)
                    .eq(Note::getInvalid, NoteInvalidEnum.invalid.getCode());
            vo.setInvalid(noteDao.selectCount(wrapper));

            // 预计剩余时间
            if (vo.getReviewed() != 0) {
                Project project = projectService.getById(projectId);
                Date startDate = project.getCreateTime();
                double workDay = DateUtil.betweenDay(startDate, DateUtil.tomorrow(), true);
                int time = (int) Math.ceil(vo.getTotal() / (vo.getReviewed() / workDay));
                vo.setExpectEndTime(time);
            }
            if (Objects.equals(vo.getTotal(), vo.getReviewed())) {
                vo.setExpectEndTime(0);
            }
        }
        return vo;
    }

    private int countTaskStep(Long projectId, Long userId, Long roleId, List<Integer> steps) {
        LambdaQueryWrapper<NoteTask> noteTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getProjectId, projectId)
                .eq(roleId == RoleEnum.annotator.getId() && userId != null, NoteTask::getAnnotator, userId)
                .eq(roleId == RoleEnum.auditor.getId() && userId != null, NoteTask::getAuditor, userId)
                .in(CollUtil.isNotEmpty(steps), NoteTask::getStep, steps);
        return noteTaskDao.selectCount(noteTaskWrapper);
    }

    private int countNoteStep(Long projectId, List<Integer> steps) {
        LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, projectId)
                .in(CollUtil.isNotEmpty(steps), Note::getStep, steps);
        return noteDao.selectCount(wrapper);
    }

    /**
     * 批次标注情况图表
     */
    @Override
    public List<BatchStatisticsVO> batchesStatistics(Long projectId) {
        List<Batch> batches = batchService.list(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, projectId));
        return batches.stream().sorted(Comparator.comparing(Batch::getCreateTime))
                .parallel().map(batch -> {
                    BatchStatisticsVO statisticsVO = new BatchStatisticsVO();
                    statisticsVO.setId(batch.getBatchId());
                    statisticsVO.setName(batch.getName());
                    statisticsVO.setStatistics(batchStatistics(batch.getBatchId()));
                    return statisticsVO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BatchVO> batchCorrectChart(Long projectId) {
        List<Batch> batches = batchService.list(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, projectId));
        return batches.stream().sorted(Comparator.comparing(Batch::getCreateTime))
                .parallel().map(batch -> {
                    BatchVO vo = new BatchVO();
                    Long batchId = batch.getBatchId();
                    vo.setBatchId(batchId);
                    vo.setBatchName(batch.getName());
                    // 正确率
                    vo.setCorrectRate(noteTaskDao.selectBatchAvgCorrectRate(batchId));
                    // 打回率
                    Double repulseRate = noteTaskDao.selectBatchAvgRepulseRate(batchId);
                    repulseRate = repulseRate == null ? 0 : repulseRate * 100;
                    vo.setRepulseRate(repulseRate);
                    vo.setAnnotator(noteTaskDao.selectBatchAnnoAvgTime(batchId));
                    vo.setAuditor(noteTaskDao.selectBatchAuditorAvgTime(batchId));
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 标注工作量排行榜统计
     */
    @Override
    public List<AnnoStatisticsVO> annotationStatistics(Long projectId) {
        List<ProjectUser> users = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, projectId)
                .eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));
        return users.parallelStream().map(user -> {
            long entityCount = mongoOperations.count(Query.query(
                    Criteria.where("project_id").is(projectId)
                            .and("user_id").is(user.getUserId())), Entity.class);
            long attrCount = 0;
            long relationCount = statisticsRelation(projectId, user.getUserId(), null);
            AnnoStatisticsVO annoStatisticsVO = new AnnoStatisticsVO();
            annoStatisticsVO.setUserId(user.getUserId());
            annoStatisticsVO.setUserName(userService.getById(user.getUserId()).getUsername());
            annoStatisticsVO.setEntityCount((int) entityCount);
            annoStatisticsVO.setAttrCount((int) attrCount);
            annoStatisticsVO.setAnnoCount((int) (entityCount + attrCount + relationCount));
            annoStatisticsVO.setRelationCount((int) relationCount);
            return annoStatisticsVO;
        }).sorted(Comparator.comparing(AnnoStatisticsVO::getAnnoCount)).collect(Collectors.toList());
    }

    /**
     * 统计关系标注的内容
     */
    private int statisticsRelation(Long projectId, Long userId, List<Long> noteIds) {
        if (CollUtil.isEmpty(noteIds)) {
            return 0;
        }
        Criteria criteria = new Criteria();
        criteria.and("project_id").is(projectId).and("user_id").is(userId)
                .and("note_id").in(noteIds).and("deleted").is(false);

        // group 已修改为 id
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("group").count().as("条数"));
        AggregationResults<BasicDBObject> results =
                mongoOperations.aggregate(aggregation, Relationship.class, BasicDBObject.class);
        return results.getMappedResults().size();
    }

    /**
     * 标注员和审核员标注统计
     */
    @Override
    public List<UserStatisticsVO> roleStatistics(Long projectId, Long batchId, Long roleId, boolean refresh) {
        if (refresh) {
            refreshRoleStatistics(projectId);
        }
        Query query = new Query();
        List<Criteria> condition = new ArrayList<>();

        if (batchId != null) {
            condition.add(Criteria.where("batchId").is(batchId));
        } else if (projectId != null) {
            condition.add(Criteria.where("projectId").is(projectId));
        }
        String today = DateUtil.today();

        condition.add(Criteria.where("createDate").is(today));
        condition.add(Criteria.where("roleId").is(roleId));

        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        query.addCriteria(criteria);
        List<AnnoStatistics> annoStatistics = mongoTemplate.find(query, AnnoStatistics.class);

        if (CollUtil.isEmpty(annoStatistics)) {
            return null;
        }
        annoStatistics = aggregateByUserId(annoStatistics);
        List<UserStatisticsVO> resultList = new ArrayList<>();
        for (AnnoStatistics annoStatistic : annoStatistics) {
            UserStatisticsVO result = new UserStatisticsVO();
            BeanUtil.copyProperties(annoStatistic, result);
            // 准确率
            int n = (int) (result.getCorrectTotal() + result.getErrorTotal() + result.getMissTotal());
            if (n != 0) {
                result.setCorrectRate(NumberUtil.div(result.getCorrectTotal().intValue(), n, 4) * 100);
            }

            // 精确率
            int p = result.getCorrectTotal().intValue() + result.getErrorTotal().intValue();
            if (p != 0) {
                result.setPrecision(NumberUtil.div(result.getCorrectTotal().intValue(), p, 4) * 100);
            }

            // 召回率
            int r = result.getCorrectTotal().intValue() + result.getMissTotal().intValue();
            if (r != 0) {
                result.setRecall(NumberUtil.div(result.getCorrectTotal().intValue(), r, 4) * 100);
            }

            // f1值
            if (result.getPrecision() != null && result.getRecall() != null && result.getPrecision() + result.getRecall() != 0d) {
                result.setF1Score(NumberUtil.div(2 * result.getPrecision() * result.getRecall(), result.getPrecision() + result.getRecall(), 4));
            }

            result.setUsername(getUsernameById(result.getUserId()));

            if (RoleEnum.annotator.getId() == annoStatistic.getRoleId()) {
                if (batchId == null) {
                    result.setAvgTime(noteTaskDao.selectAnnoAvgTime(annoStatistic.getProjectId(), annoStatistic.getUserId()));
                } else {
                    result.setAvgTime(noteTaskDao.selectAnnoAvgTimeByBatch(batchId, annoStatistic.getUserId()));
                }
            }
            if (RoleEnum.auditor.getId() == annoStatistic.getRoleId()) {
                if (batchId == null) {
                    result.setAvgTime(noteTaskDao.selectAuditorAvgTime(annoStatistic.getProjectId(), annoStatistic.getUserId()));
                } else {
                    result.setAvgTime(noteTaskDao.selectAuditorAvgTimeByBatch(batchId, annoStatistic.getUserId()));
                }
            }
            resultList.add(result);
        }
        return resultList;
    }

    public static List<AnnoStatistics> aggregateByUserId(List<AnnoStatistics> dataList) {
        // 使用Map以userId为键进行分组，并将各个字段累加
        Map<String, AnnoStatistics> aggregatedMap = new HashMap<>();

        for (AnnoStatistics data : dataList) {
            Long userId = data.getUserId();
            String key = data.getCreateDate() + "_" + data.getUserId();
            AnnoStatistics aggregated = aggregatedMap.getOrDefault(key, new AnnoStatistics());
            aggregated.setUserId(userId);
            aggregated.setCreateDate(data.getCreateDate());
            aggregated.setProjectId(data.getProjectId());
            aggregated.setBatchId(data.getBatchId());
            aggregated.setUserId(data.getUserId());
            aggregated.setRoleId(data.getRoleId());
            // 累加各字段
            aggregated.setCount(aggregated.getCount() + data.getCount());
            aggregated.setEntityCount(aggregated.getEntityCount() + data.getEntityCount());
            aggregated.setCorrectEntityCount(aggregated.getCorrectEntityCount() + data.getCorrectEntityCount());
            aggregated.setAttrCount(aggregated.getAttrCount() + data.getAttrCount());
            aggregated.setCorrectAttrCount(aggregated.getCorrectAttrCount() + data.getCorrectAttrCount());
            aggregated.setEntityAttrCount(aggregated.getEntityAttrCount() + data.getEntityAttrCount());
            aggregated.setCorrectEntityAttr(aggregated.getCorrectEntityAttr() + data.getCorrectEntityAttr());
            aggregated.setRelationGroupCount(aggregated.getRelationGroupCount() + data.getRelationGroupCount());
            aggregated.setCorrectRelationGroupCount(aggregated.getCorrectRelationGroupCount() + data.getCorrectRelationGroupCount());
            aggregated.setRelationCount(aggregated.getRelationCount() + data.getRelationCount());
            aggregated.setCorrectRelationCount(aggregated.getCorrectRelationCount() + data.getCorrectRelationCount());
            aggregated.setNeedTotal(aggregated.getNeedTotal() + data.getNeedTotal());
            aggregated.setCorrectTotal(aggregated.getCorrectTotal() + data.getCorrectTotal());
            aggregated.setErrorTotal(aggregated.getErrorTotal() + data.getErrorTotal());
            aggregated.setMissTotal(aggregated.getMissTotal() + data.getMissTotal());
            aggregatedMap.put(key, aggregated);
        }
        return new ArrayList<>(aggregatedMap.values());
    }

    @Override
    public Map<String, Object> accuracyChart(Long projectId, Long batchId) {
        Query query = new Query();
        List<Criteria> condition = new ArrayList<>();

        if (batchId != null) {
            condition.add(Criteria.where("batchId").is(batchId));
        } else if (projectId != null) {
            condition.add(Criteria.where("projectId").is(projectId));
        }
        condition.add(Criteria.where("roleId").is(RoleEnum.annotator.getId()));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        query.addCriteria(criteria);
        List<AnnoStatistics> annoStatistics = mongoTemplate.find(query, AnnoStatistics.class);

        if (CollUtil.isEmpty(annoStatistics)) {
            return null;
        }
        annoStatistics = aggregateByUserId(annoStatistics);
        List<AccuracyChartVO> resultList = new ArrayList<>();
        for (AnnoStatistics annoStatistic : annoStatistics) {
            AccuracyChartVO result = new AccuracyChartVO();
            result.setDate(annoStatistic.getCreateDate());

            // 准确率
            int n = (int) (annoStatistic.getCorrectTotal() + annoStatistic.getErrorTotal() + annoStatistic.getMissTotal());
            if (n != 0) {
                result.setCorrectRate(NumberUtil.div(annoStatistic.getCorrectTotal().intValue(), n, 4) * 100);
            }
            result.setUsername(getUsernameById(annoStatistic.getUserId()));

            resultList.add(result);
        }
        return buildEchartsData(resultList);
    }

    @Override
    public Map<String, Object> consistencyChart(Long projectId, Long batchId) {
        Query query = new Query();
        List<Criteria> condition = new ArrayList<>();
        if (batchId != null) {
            condition.add(Criteria.where("batchId").is(batchId));
        } else if (projectId != null) {
            condition.add(Criteria.where("projectId").is(projectId));
        }
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        query.addCriteria(criteria);
        List<AnnoKappaAlpha> annoStatistics = mongoTemplate.find(query, AnnoKappaAlpha.class);

        if (CollUtil.isEmpty(annoStatistics)) {
            return null;
        }
        List<AccuracyChartVO> resultList = new ArrayList<>();
        for (AnnoKappaAlpha annoStatistic : annoStatistics) {
            AccuracyChartVO result = new AccuracyChartVO();
            result.setDate(annoStatistic.getCreateDate());
            result.setCorrectRate(annoStatistic.getEntityKappa());

            List<Long> userIds = annoStatistic.getUserIds();
            Collections.sort(userIds);
            List<String> username = new ArrayList<>();
            for (Long userId : userIds) {
                username.add(getUsernameById(userId));
            }
            result.setUsername(CollUtil.join(username, "、"));
            resultList.add(result);
        }
        return buildEchartsData(resultList);
    }

    public static Map<String, Object> buildEchartsData(List<AccuracyChartVO> dataList) {
        // 1. 提取所有日期并排序，作为横轴数据
        Set<String> dates = dataList.stream()
                .map(AccuracyChartVO::getDate)
                .collect(Collectors.toCollection(TreeSet::new));

        // 2. 提取所有用户名
        Set<String> usernames = dataList.stream()
                .map(AccuracyChartVO::getUsername)
                .collect(Collectors.toSet());

        // 3. 初始化结果数据结构
        Map<String, Object> result = new HashMap<>();
        result.put("xAxis", dates);

        // 4. 为每个用户名构建 series 数据
        List<Map<String, Object>> seriesData = new ArrayList<>();

        for (String username : usernames) {
            Map<String, Object> seriesItem = new HashMap<>();
            seriesItem.put("name", username);
            seriesItem.put("type", "line");

            // 构建用户在每个日期的准确率数据（默认0.00d）
            List<Double> correctRates = new ArrayList<>();
            for (String date : dates) {
                // 查找对应日期和用户名的记录，若没有则设置为默认值
                Optional<AccuracyChartVO> record = dataList.stream()
                        .filter(data -> data.getUsername().equals(username) && data.getDate().equals(date))
                        .findFirst();
                correctRates.add(record.map(AccuracyChartVO::getCorrectRate).orElse(0.00d));
            }
            seriesItem.put("data", correctRates);
            seriesData.add(seriesItem);
        }

        result.put("series", seriesData);
        return result;
    }

    @Override
    public void annotatedStatisticsJob() {
        String today = DateUtil.today();

        List<Project> projects = projectService.list();

        for (Project project : projects) {
            try {
                statisticalAnnoData(project.getProjectId(), today);

                if (project.getMarkRounds() > 1) {
                    calculationConsistency(project.getProjectId(), today);
                }
            } catch (Exception ignore) {
            }
        }
    }

    public void refreshRoleStatistics(Long projectId) {
        String today = DateUtil.today();
        statisticalAnnoData(projectId, today);
    }

    public void refreshCalculationConsistency(Long projectId) {
        String today = DateUtil.today();
        calculationConsistency(projectId, today);
    }

    // 使用synchronized，防止出现数据异常
    private synchronized void statisticalAnnoData(Long projectId, String today) {
        /*final String taskKey = "refresh_" + projectId;
        if (!runningTasks.add(taskKey)) {
            log.info("任务正在执行中,projectId:{}", projectId);
            return;
        }
        try {
        } finally {
            runningTasks.remove(taskKey);
        }*/
        List<ProjectUser> users = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, projectId));
        if (CollUtil.isEmpty(users)) {
            return;
        }

        List<Batch> batchList = batchService.findAllByProjectId(projectId);

        if (CollUtil.isEmpty(batchList)) {
            return;
        }

        Query query = Query.query(Criteria.where("projectId").is(projectId).and("createDate").is(today));
        mongoTemplate.remove(query, AnnoStatistics.class);

        final List<AnnoStatistics> list = new ArrayList<>();
        for (Batch batch : batchList) {
            Long batchId = batch.getBatchId();
            for (ProjectUser user : users) {
                Long userId = user.getUserId();
                Long roleId = user.getRoleId();

                if (RoleEnum.annotator.getId() == roleId || RoleEnum.auditor.getId() == roleId) {
                    UserStatisticsVO userStatisticsVO = userStatisticsVO(batchId, userId, roleId);
                    if (userStatisticsVO == null || userStatisticsVO.getCount() == 0) {
                        continue;
                    }

                    AnnoStatistics annoStatistics = new AnnoStatistics();
                    BeanUtil.copyProperties(userStatisticsVO, annoStatistics);
                    annoStatistics.setCreateDate(today);
                    annoStatistics.setProjectId(projectId);
                    annoStatistics.setBatchId(batchId);
                    annoStatistics.setUserId(userId);
                    annoStatistics.setRoleId(roleId);
//                    mongoTemplate.save(annoStatistics);
                    list.add(annoStatistics);
                }
            }
        }
        mongoTemplate.insertAll(list);
    }

    /**
     * 项目标注进度
     */
    @Override
    public List<Statistics> dateStatistics(Long projectId) {
        task();
        List<Statistics> list = this.list(Wrappers.<Statistics>lambdaQuery().eq(Statistics::getProjectId, projectId));
        list = list.stream().sorted(Comparator.comparing(Statistics::getCreateTime)).collect(Collectors.toList());
        return list;
    }

    private StatisticsVO batchStatistics(Long batchId) {
        if (batchId == null) {
            return null;
        }
        int unmarked = noteService.count(Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .ne(Note::getStep, NoteStepEnum.unmarked.getCode()));
        int marked = noteService.count(Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .notIn(Note::getStep,
                        CollUtil.newArrayList(NoteStepEnum.unmarked.getCode()
                                , NoteStepEnum.reviewed.getCode())));
        int audited = noteService.count(Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .eq(Note::getStep, NoteStepEnum.reviewed.getCode()));
        StatisticsVO statisticsVO = new StatisticsVO();
        statisticsVO.setReviewed(audited);
        statisticsVO.setUnReview(marked);
        statisticsVO.setUnmarked(unmarked);
        return statisticsVO;
    }

    private UserStatisticsVO userStatisticsVO(Long batchId, Long userId, Long roleId) {
        UserStatisticsVO result = new UserStatisticsVO();
        // 查询文章数
        List<NoteTask> noteTasks = noteTaskDao.selectList(Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getBatchId, batchId)
                .eq(roleId == RoleEnum.annotator.getId(), NoteTask::getAnnotator, userId)
                .eq(roleId == RoleEnum.auditor.getId(), NoteTask::getAuditor, userId)
                .eq(roleId == RoleEnum.auditor.getId(), NoteTask::getMaster, MasterEnum.master.getValue()));
        if (CollUtil.isEmpty(noteTasks)) {
            return null;
        }
        result.setCount(noteTasks.size());

        // 标注员
        if (roleId == RoleEnum.annotator.getId()) {
            // 所有实体数量
            long countEntity = annoTaskTotal(batchId, null, userId, AttrAnnoEnum.not_attr.getCode(), Entity.class);
            result.setEntityCount(countEntity);
            // 正确的实体数量
            long correctEntity = noteTasks.stream().filter(x -> x.getCorrectEntity() != null).mapToLong(NoteTask::getCorrectEntity).sum();
            result.setCorrectEntityCount(correctEntity);

            // 所有属性数量
            long countAttr = annoTaskTotal(batchId, null, userId, AttrAnnoEnum.is_attr.getCode(), Entity.class);
            result.setAttrCount(countAttr);
            // 正确的属性数量
            long correctAttr = noteTasks.stream().filter(x -> x.getCorrectAttr() != null).mapToLong(NoteTask::getCorrectAttr).sum();
            result.setCorrectAttrCount(correctAttr);

            // 实体属性关联数量
            long countEntityAttr = annoTaskTotal(batchId, null, userId, null, Attributes.class);
            result.setEntityAttrCount(countEntityAttr);
            // 正确的实体属性关联数量
            long correctEntityAttr = annoCorrectTask(batchId, null, userId, null, Attributes.class);
            result.setCorrectEntityAttr(correctEntityAttr);

            // 所有关系组数量
            long countRelation = annoTaskTotal(batchId, null, userId, null, Relationship.class);
            result.setRelationGroupCount(countRelation);
            // 正确的关系组数量
            long correctRelation = annoCorrectTask(batchId, null, userId, null, Relationship.class);
            result.setCorrectRelationGroupCount(correctRelation);

            // 关系条数量
            long countItemRelation = annoRelationItemTotal(batchId, null, userId);
            result.setRelationCount(countItemRelation);
            // 正确关系条数量
            long correctRelationItemCount = noteTasks.stream().filter(x -> x.getCorrectRelation() != null).mapToLong(NoteTask::getCorrectRelation).sum();
            result.setCorrectRelationCount(correctRelationItemCount);

            result.setNeedTotal(noteTasks.stream().filter(x -> x.getNeedTotal() != null).mapToLong(NoteTask::getNeedTotal).sum());
            result.setCorrectTotal(noteTasks.stream().filter(x -> x.getCorrectTotal() != null).mapToLong(NoteTask::getCorrectTotal).sum());
            result.setErrorTotal(noteTasks.stream().filter(x -> x.getErrorTotal() != null).mapToLong(NoteTask::getErrorTotal).sum());
            result.setMissTotal(noteTasks.stream().filter(x -> x.getMissTotal() != null).mapToLong(NoteTask::getMissTotal).sum());

            // f1值
            if (result.getPrecision() != null && result.getRecall() != null && result.getPrecision() + result.getRecall() != 0d) {
                result.setF1Score(NumberUtil.div(2 * result.getPrecision() * result.getRecall(), result.getPrecision() + result.getRecall(), 4));
            }

            // 返工率
            List<NoteTask> repulseTask = noteTaskDao.selectList(Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getBatchId, batchId)
                    .eq(NoteTask::getAnnotator, userId)
                    .isNotNull(NoteTask::getRepulseMsg));
            if (CollUtil.isNotEmpty(repulseTask)) {
                result.setRework((double) repulseTask.size() / noteTasks.size() * 100);
            }
            // 平均耗时
            result.setAvgTime(noteTaskDao.selectBatchAnnoAvgTimeByUser(batchId, userId));
        }
        // 审核员
        if (roleId == RoleEnum.auditor.getId()) {
            // 所有实体数量
            long countEntity = countTaskTotal(batchId, null, AttrAnnoEnum.not_attr.getCode(), Entity.class);
            result.setEntityCount(countEntity);
            // 审核员 操作的实体数量
            long correctEntity = auditorCorrectTask(batchId, null, userId, AttrAnnoEnum.not_attr.getCode(), Entity.class);
            result.setCorrectEntityCount(correctEntity);

            // 所有属性数量
            long countAttr = countTaskTotal(batchId, null, AttrAnnoEnum.is_attr.getCode(), Entity.class);
            result.setAttrCount(countAttr);
            // 审核员 操作的属性数量
            long correctAttr = auditorCorrectTask(batchId, null, userId, AttrAnnoEnum.is_attr.getCode(), Entity.class);
            result.setCorrectAttrCount(correctAttr);

            // 实体属性关联数量
            long countEntityAttr = countTaskTotal(batchId, null, null, Attributes.class);
            result.setEntityAttrCount(countEntityAttr);
            // 审核员 操作的实体属性关联数量
            long correctEntityAttr = auditorCorrectAttrTask(batchId, null, userId);
            result.setCorrectEntityAttr(correctEntityAttr);

            // 所有关系组数量
            long countRelation = countTaskTotal(batchId, null, null, Relationship.class);
            result.setRelationGroupCount(countRelation);
            // 审核员 操作的关系组数量
            long correctRelation = auditorCorrectTask(batchId, null, userId, null, Relationship.class);
            result.setCorrectRelationGroupCount(correctRelation);

            // 关系条总数量
            long countItemRelation = countRelationItem(batchId, null);
            result.setRelationCount(countItemRelation);
            // 审核员 操作的关系条数量
            long correctRelationItemCount = auditorCorrectRelationItem(batchId, null, userId);
            result.setCorrectRelationCount(correctRelationItemCount);

            // 平均耗时
            result.setAvgTime(noteTaskDao.selectBatchAuditorAvgTimeByUser(batchId, userId));
        }
        result.setUserId(userId);
        return result;
    }

    /**
     * 标注员标注过的所有实体、属性、关系的总数
     */
    private long annoTaskTotal(Long batchId, Long taskId, Long annotatorId, Integer isAttr, Class<?> entityClass) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        if (isAttr != null) {
            condition.add(Criteria.where("is_attr").is(isAttr));
        }
        Criteria c1 = Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").is(null)
                .and("deleted").is(false);
        Criteria c2 = Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").exists(true);
        condition.add(new Criteria().orOperator(c1, c2));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoTemplate.count(Query.query(criteria), entityClass);
    }

    /**
     * 标注员标注的正确实体、属性、关系总数
     */
    private long annoCorrectTask(Long batchId, Long taskId, Long annotatorId, Integer isAttr, Class<?> entityClass) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        if (isAttr != null) {
            condition.add(Criteria.where("is_attr").is(isAttr));
        }
        condition.add(Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").is(null)
                .and("deleted").is(false));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoTemplate.count(Query.query(criteria), entityClass);
    }

    /**
     * 标注员标注过的关系的条数总数
     */
    private long annoRelationItemTotal(Long batchId, Long taskId, Long annotatorId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        Criteria c1 = Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").is(null)
                .and("deleted").is(false);
        Criteria c2 = Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").exists(true);
        condition.add(new Criteria().orOperator(c1, c2));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return getItemCount(criteria);
    }

    private long correctRelationItem(Long projectId, Long taskId, Long annotatorId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("project_id").is(projectId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        condition.add(Criteria.where("annotator_id").is(annotatorId)
                .and("auditor_id").is(null)
                .and("deleted").is(false));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return getItemCount(criteria);
    }

    private long countRelationItem(Long batchId, Long taskId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        condition.add(Criteria.where("deleted").is(false));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoTemplate.count(Query.query(criteria), Relationship.class);
    }

    private long getItemCount(Criteria criteria) {
        MatchOperation matchOperation = Aggregation.match(criteria);
        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation,
                Aggregation.project("items"),
                Aggregation.unwind("items"),
                Aggregation.group().count().as("count")
        );
        AggregationResults<Document> documents = mongoTemplate.aggregate(aggregation, Relationship.class, Document.class);
        List<Document> mappedResults = documents.getMappedResults();
        if (CollUtil.isEmpty(mappedResults)) {
            return 0;
        }
        return (long) mappedResults.get(0).getInteger("count");
    }

    /**
     * 统计所有实体、属性、关系的总数
     */
    private long countTaskTotal(Long batchId, Long taskId, Integer isAttr, Class<?> entityClass) {
        Query query = Query.query(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            query.addCriteria(Criteria.where("task_id").is(taskId));
        }
        if (isAttr != null) {
            query.addCriteria(Criteria.where("is_attr").is(isAttr));
        }
        if (!"Attributes".equals(entityClass.getSimpleName())) {
            query.addCriteria(Criteria.where("deleted").is(false));
        }
        return mongoTemplate.count(query, entityClass);
    }


    /**
     * 审核员操作过的实体、属性、关系总数
     */
    private long auditorCorrectTask(Long batchId, Long taskId, Long auditorId, Integer isAttr, Class<?> entityClass) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        if (isAttr != null) {
            condition.add(Criteria.where("is_attr").is(isAttr));
        }
        Criteria c1 = Criteria.where("annotator_id").is(null)
                .and("auditor_id").is(auditorId)
                .and("deleted").is(false);
        Criteria c2 = Criteria.where("annotator_id").is(null)
                .and("auditor_id").is(null);
        condition.add(new Criteria().orOperator(c1, c2));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoTemplate.count(Query.query(criteria), entityClass);
    }

    /**
     * 审核员操作的实体属性关联总数
     */
    private long auditorCorrectAttrTask(Long batchId, Long taskId, Long auditorId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        condition.add(Criteria.where("auditor_id").is(auditorId));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoTemplate.count(Query.query(criteria), Attributes.class);
    }

    private long auditorCorrectRelationItem(Long batchId, Long taskId, Long auditorId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("batch_id").is(batchId));
        if (taskId != null) {
            condition.add(Criteria.where("task_id").is(taskId));
        }
        condition.add(Criteria.where("auditor_id").is(auditorId)
                .and("deleted").is(false));
        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return getItemCount(criteria);
    }

    @Override
    public void task() {
        Date today = DateUtil.parse(DateUtil.today());
        List<Project> projects = projectService.list(Wrappers.<Project>lambdaQuery()
                .eq(Project::getStatus, StatusEnum.enable.getValue()));
        projects.parallelStream().forEach(project -> {
            // 统计项目标注整体进度
            Statistics statistics = this.getOne(Wrappers.<Statistics>lambdaQuery()
                    .eq(Statistics::getProjectId, project.getProjectId())
                    .eq(Statistics::getCreateTime, today));
            if (statistics == null) {
                statistics = new Statistics();
                statistics.setCreateTime(today);
                statistics.setProjectId(project.getProjectId());
            }
            Integer unmarked = noteService.count(Wrappers.<Note>lambdaQuery()
                    .eq(Note::getProjectId, project.getProjectId())
                    .eq(Note::getStep, NoteStepEnum.unmarked.getCode()));
            Integer marked = noteService.count(Wrappers.<Note>lambdaQuery()
                    .eq(Note::getProjectId, project.getProjectId())
                    .notIn(Note::getStep, Arrays.asList(NoteStepEnum.unmarked.getCode(), NoteStepEnum.reviewed.getCode())));
            Integer reviewed = noteService.count(Wrappers.<Note>lambdaQuery()
                    .eq(Note::getProjectId, project.getProjectId())
                    .eq(Note::getStep, NoteStepEnum.reviewed.getCode()));
            statistics.setUnmarked(unmarked);
            statistics.setMarked(marked);
            statistics.setReviewed(reviewed);
            this.saveOrUpdate(statistics);
        });
    }

    @Override
    public List<ProjectVO> getProjects(Long roleId, Long userId) {
        return projectDao.queryProjects(roleId, userId);
    }

    @Override
    public List<WorkProgressVO> workProgressStatistics(Long projectId, Long roleId) {
        List<WorkProgressVO> result = new ArrayList<>();
        List<ProjectUser> users = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, projectId)
                .eq(ProjectUser::getRoleId, roleId));
        for (ProjectUser user : users) {
            WorkProgressVO workProgressVO = new WorkProgressVO();
            // 标注员
            if (roleId == RoleEnum.annotator.getId()) {
                // 标注中
                workProgressVO.setNoting(countTaskStep(
                        projectId, user.getUserId(), roleId,
                        CollUtil.newArrayList(NoteStepEnum.repulse.getCode(), NoteStepEnum.noting.getCode())));
                // 待审核
                workProgressVO.setUnReview(countTaskStep(
                        projectId, user.getUserId(), roleId,
                        CollUtil.newArrayList(NoteStepEnum.marked.getCode(),
                                NoteStepEnum.corrected.getCode(), NoteStepEnum.reviewing.getCode())));

                // 审核员
            } else if (roleId == RoleEnum.auditor.getId()) {
                // 审核中
                workProgressVO.setReviewing(countTaskStep(
                        projectId, user.getUserId(), roleId,
                        CollUtil.newArrayList(NoteStepEnum.corrected.getCode(), NoteStepEnum.reviewing.getCode())));
            }
            // 已验收
            workProgressVO.setReviewed(countTaskStep(
                    projectId, user.getUserId(), roleId,
                    CollUtil.newArrayList(NoteStepEnum.reviewed.getCode())));

            workProgressVO.setUserId(user.getUserId());
            workProgressVO.setUsername(getUsernameById(user.getUserId()));
            result.add(workProgressVO);
        }
        return result;
    }

    @Override
    public List<AnnoDetailsVO> annoDetails(Long projectId, Long roleId, Long userId) {
        List<AnnoDetailsVO> details = noteTaskDao.queryAnnoDetail(projectId, roleId, userId);
        if (CollUtil.isEmpty(details)) {
            return null;
        }
        List<Batch> batches = batchService.list(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, projectId));
        Map<Long, String> batchMap = batches.stream().collect(Collectors.toMap(Batch::getBatchId, Batch::getName));
        for (AnnoDetailsVO result : details) {
            if (batchMap.containsKey(result.getBatchId())) {
                result.setBatchName(batchMap.get(result.getBatchId()));
            }
            result.setAuditor(getUsernameById(result.getAuditorId()));
            result.setStepStr(NoteStepEnum.findStep(result.getStep()));
            if (result.getCorrectRate() == null || !result.getStep().equals(NoteStepEnum.reviewed.getCode())) {
                result.setCorrectRate(0d);
            }
            Long taskId = result.getTaskId();
            if (roleId == RoleEnum.annotator.getId()) {

                // 所有实体数量
                long countEntity = annoTaskTotal(projectId, taskId, userId, AttrAnnoEnum.not_attr.getCode(), Entity.class);
                result.setEntityCount(countEntity);

                // 所有属性数量
                long countAttr = annoTaskTotal(projectId, taskId, userId, AttrAnnoEnum.is_attr.getCode(), Entity.class);
                result.setAttrCount(countAttr);

                // 实体属性关联数量
                long countEntityAttr = annoTaskTotal(projectId, taskId, userId, null, Attributes.class);
                result.setEntityAttrCount(countEntityAttr);
                // 实体属性关联数量
                long correctEntityAttr = annoCorrectTask(projectId, taskId, userId, null, Attributes.class);
                result.setCorrectEntityAttr(correctEntityAttr);

                // 所有关系组数量
                long countRelation = annoTaskTotal(projectId, taskId, userId, null, Relationship.class);
                result.setRelationGroupCount(countRelation);

                // 关系条数量
                long countItemRelation = annoRelationItemTotal(projectId, taskId, userId);
                result.setRelationCount(countItemRelation);
                // 正确关系条数量
                long correctRelationItemCount = correctRelationItem(projectId, taskId, userId);
                result.setCorrectRelationCount(correctRelationItemCount);
            }
            if (roleId == RoleEnum.auditor.getId()) {
                // 所有实体数量
                long countEntity = countTaskTotal(projectId, taskId, AttrAnnoEnum.not_attr.getCode(), Entity.class);
                result.setEntityCount(countEntity);
                // 审核员 操作的实体数量
                long correctEntity = auditorCorrectTask(projectId, taskId, userId, AttrAnnoEnum.not_attr.getCode(), Entity.class);
                result.setCorrectEntityCount(correctEntity);

                // 所有属性数量
                long countAttr = countTaskTotal(projectId, taskId, AttrAnnoEnum.is_attr.getCode(), Entity.class);
                result.setAttrCount(countAttr);
                // 审核员 操作的属性数量
                long correctAttr = auditorCorrectTask(projectId, taskId, userId, AttrAnnoEnum.is_attr.getCode(), Entity.class);
                result.setCorrectAttrCount(correctAttr);

                // 实体属性关联数量
                long countEntityAttr = countTaskTotal(projectId, taskId, null, Attributes.class);
                result.setEntityAttrCount(countEntityAttr);
                // 审核员 操作的实体属性关联数量
                long correctEntityAttr = auditorCorrectAttrTask(projectId, null, userId);
                result.setCorrectEntityAttr(correctEntityAttr);

                // 所有关系组数量
                long countRelation = countTaskTotal(projectId, taskId, null, Relationship.class);
                result.setRelationGroupCount(countRelation);
                // 审核员 操作的关系组数量
                long correctRelation = auditorCorrectTask(projectId, taskId, userId, null, Relationship.class);
                result.setCorrectRelationGroupCount(correctRelation);

                // 关系条总数量
                long countItemRelation = countRelationItem(projectId, taskId);
                result.setRelationCount(countItemRelation);
                // 审核员 操作的关系条数量
                long correctRelationItemCount = auditorCorrectRelationItem(projectId, taskId, userId);
                result.setCorrectRelationCount(correctRelationItemCount);
            }
        }
        return details;
    }

    @Override
    public List<KappaAlphaVO> calculationConsistency(Long projectId, Long batchId, boolean refresh) {
        if (refresh) {
            refreshCalculationConsistency(projectId);
        }
        Query query = new Query();
        List<Criteria> condition = new ArrayList<>();

        if (batchId != null) {
            condition.add(Criteria.where("batchId").is(batchId));
        } else if (projectId != null) {
            condition.add(Criteria.where("projectId").is(projectId));
        }
        String today = DateUtil.today();

        condition.add(Criteria.where("createDate").is(today));

        Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        query.addCriteria(criteria);
        List<AnnoKappaAlpha> annoStatistics = mongoTemplate.find(query, AnnoKappaAlpha.class);

        if (CollUtil.isEmpty(annoStatistics)) {
            return null;
        }
        List<KappaAlphaVO> resultList = new ArrayList<>();
        for (AnnoKappaAlpha annoStatistic : annoStatistics) {
            KappaAlphaVO result = new KappaAlphaVO();
            BeanUtil.copyProperties(annoStatistic, result);

            List<Long> userIds = annoStatistic.getUserIds();
            List<String> username = new ArrayList<>();
            for (Long userId : userIds) {
                username.add(getUsernameById(userId));
            }
            result.setAnnotater(username);
            resultList.add(result);
        }
        return resultList;
    }

    public synchronized void calculationConsistency(Long projectId, String today) {
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery()
                .eq(Project::getProjectId, projectId));

        if (project == null) {
            return;
        }

        Integer markRounds = project.getMarkRounds();
        if (markRounds < 2) {
            return;
        }

        List<List<Long>> results = getUserGroupLists(projectId, markRounds);
        if (CollUtil.isEmpty(results)) {
            return;
        }

        List<Batch> batchList = batchService.findAllByProjectId(projectId);
        if (CollUtil.isEmpty(batchList)) {
            return;
        }

        // 按照批次的维度进行计算
        for (Batch batch : batchList) {
            Long batchId = batch.getBatchId();
            Query query = Query.query(Criteria.where("batchId").is(batchId).and("createDate").is(today));

            mongoTemplate.remove(query, AnnoKappaAlpha.class);
            for (List<Long> userIdList : results) {
                AnnoKappaAlpha annoKappaAlpha = calculationConsistency(userIdList, markRounds, null, batchId, null);

                if (annoKappaAlpha == null) {
                    continue;
                }
                if (annoKappaAlpha.getEntityKappa() == 0 && annoKappaAlpha.getEntityAlpha() == 0
                        && annoKappaAlpha.getAttrKappa() == 0 && annoKappaAlpha.getAttrAlpha() == 0) {
                    continue;
                }
                annoKappaAlpha.setUserIds(userIdList);
                annoKappaAlpha.setBatchId(batchId);
                annoKappaAlpha.setCreateDate(today);
                mongoTemplate.save(annoKappaAlpha);
            }
        }

        Query query = Query.query(Criteria.where("projectId").is(projectId).and("createDate").is(today));
        mongoTemplate.remove(query, AnnoKappaAlpha.class);
        // 按照项目的维度进行计算
        for (List<Long> userIdList : results) {
            AnnoKappaAlpha annoKappaAlpha = calculationConsistency(userIdList, markRounds, project.getProjectId(), null, null);

            if (annoKappaAlpha == null) {
                continue;
            }
            if (annoKappaAlpha.getEntityKappa() == 0 && annoKappaAlpha.getEntityAlpha() == 0
                    && annoKappaAlpha.getAttrKappa() == 0 && annoKappaAlpha.getAttrAlpha() == 0) {
                continue;
            }
            annoKappaAlpha.setUserIds(userIdList);
            annoKappaAlpha.setProjectId(projectId);
            annoKappaAlpha.setCreateDate(today);
            mongoTemplate.save(annoKappaAlpha);
        }
    }

    /**
     * 导出项目所有标签的标注一致性数据到Excel并发送邮件
     *
     * @param projectId 项目ID
     * @param batchId   批次ID
     * @param email     接收邮件的地址
     * @return 是否发送成功
     */
    @Override
    public void exportConsistencyToExcel(Long projectId, Long batchId, String email) {

        // 查询项目信息
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery()
                .eq(Project::getProjectId, projectId));
        if (project == null) {
            throw new RRException("项目不存在");
        }

        // 查询项目下所有标签
        List<EntityLabel> labels = entityLabelService.findAllEnableByProjectId(projectId);
        if (CollUtil.isEmpty(labels)) {
            throw new RRException("项目下没有标签");
        }

        try {
            // 创建Excel工作簿
            SXSSFWorkbook workbook = new SXSSFWorkbook();

            // 为每个标签创建一个工作表并填充数据
            for (EntityLabel label : labels) {
                List<AnnoKappaAlpha> kappaAlphaList = calculationConsistencyByLabel(projectId, batchId, label.getId());
                if (CollUtil.isNotEmpty(kappaAlphaList)) {
                    createSheet(workbook, label.getName(), kappaAlphaList);
                }
            }

            Batch batch = null;
            if (batchId != null) {
                batch = batchService.queryBatch(batchId);
            }

            // 生成临时文件
            String fileName = "实体标签标注一致性报告_" + project.getName() + "_" + (batch == null ? "" : "_批次_" + batch.getName()) + "_" + DateUtil.format(new Date(), "yyyyMMdd_HHmm") + ".xlsx";
            String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            FileOutputStream fos = new FileOutputStream(tempFilePath);
            workbook.write(fos);
            fos.close();
            workbook.dispose();

            // 发送邮件
            String subject = "标注一致性报告 - " + project.getName();
            String content = "您好，\n\n附件是项目:" + project.getName() + (batch == null ? "" : "  批次:" + batch.getName()) + " 的实体标签标注一致性报告，请查收。\n\n此邮件由系统自动发送，请勿回复。";
            mailService.sendAttachmentMail(email, subject, content, fileName, tempFilePath);

            // 删除临时文件
            FileUtil.del(tempFilePath);
        } catch (Exception e) {
            log.error("导出项目{}的实体标签标注一致性数据失败", projectId, e);
        }
    }

    /**
     * 创建Excel工作表并填充数据
     *
     * @param workbook       Excel工作簿
     * @param sheetName      工作表名称
     * @param kappaAlphaList 一致性数据列表
     */
    private void createSheet(SXSSFWorkbook workbook, String sheetName, List<AnnoKappaAlpha> kappaAlphaList) {
        // 创建工作表
        Sheet sheet = workbook.createSheet(sheetName);

        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建数值单元格样式（保留2位小数）
        CellStyle decimalStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        decimalStyle.setDataFormat(format.getFormat("0.00"));
        decimalStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"用户组", "实体Gwet's AC1系数", "属性Fleiss' Kappa (κ)系数", "属性Krippendorff's Alpha（α）系数"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            // 设置列宽
            sheet.setColumnWidth(i, 8000);
        }

        // 填充数据
        int rowNum = 1;
        for (AnnoKappaAlpha kappaAlpha : kappaAlphaList) {
            Row row = sheet.createRow(rowNum++);

            // 用户组信息
            List<Long> userIds = kappaAlpha.getUserIds();
            List<String> usernames = userIds.stream()
                    .map(this::getUsernameById)
                    .collect(Collectors.toList());
            row.createCell(0).setCellValue(String.join(", ", usernames));

            // 统计数据 - 保留2位小数
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(kappaAlpha.getEntityKappa());
            cell1.setCellStyle(decimalStyle);

            Cell cell2 = row.createCell(2);
            cell2.setCellValue(kappaAlpha.getAttrKappa());
            cell2.setCellStyle(decimalStyle);

            Cell cell3 = row.createCell(3);
            cell3.setCellValue(kappaAlpha.getAttrAlpha());
            cell3.setCellStyle(decimalStyle);
        }
    }

    /**
     * 导出标签的标注一致性
     *
     * @param projectId 项目ID
     * @param batchId   批次ID
     * @param labelId   所选标签ID
     */
    public List<AnnoKappaAlpha> calculationConsistencyByLabel(Long projectId, Long batchId, Long labelId) {
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery()
                .eq(Project::getProjectId, projectId));

        if (project == null) {
            return null;
        }

        Integer markRounds = project.getMarkRounds();
        if (markRounds < 2) {
            return null;
        }

        List<List<Long>> results = getUserGroupLists(projectId, markRounds);
        if (CollUtil.isEmpty(results)) {
            return null;
        }

        List<Batch> batchList = batchService.findAllByProjectId(projectId);
        if (CollUtil.isEmpty(batchList)) {
            return null;
        }

        // 按照批次的维度进行计算
        List<AnnoKappaAlpha> result = new ArrayList<>();
        for (List<Long> userIdList : results) {

            if (userIdList.get(0) == 50 && userIdList.get(1) == 111) {
                System.out.print("123");
            }
            AnnoKappaAlpha annoKappaAlpha;
            if (batchId != null) {
                annoKappaAlpha = calculationConsistency(userIdList, markRounds, null, batchId, labelId);
            } else {
                annoKappaAlpha = calculationConsistency(userIdList, markRounds, projectId, null, labelId);
            }

            if (annoKappaAlpha == null) {
                continue;
            }
            if (annoKappaAlpha.getEntityKappa() == 0 && annoKappaAlpha.getEntityAlpha() == 0
                    && annoKappaAlpha.getAttrKappa() == 0 && annoKappaAlpha.getAttrAlpha() == 0) {
                continue;
            }
            annoKappaAlpha.setUserIds(userIdList);
            annoKappaAlpha.setBatchId(batchId);
            result.add(annoKappaAlpha);
        }
        return result;
    }

    private List<List<Long>> getUserGroupLists(Long projectId, Integer markRounds) {
        List<ProjectUser> users = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, projectId)
                .eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));

        if (CollUtil.isEmpty(users)) {
            return null;
        }

        List<Long> userIds = users.stream()
                .map(ProjectUser::getUserId)
                .sorted() // 先排序以支持后续去重
                .collect(Collectors.toList());

        Set<List<Long>> uniqueCombinations = new HashSet<>();
        combineHelper(userIds, new ArrayList<>(), uniqueCombinations, 0, markRounds);
        return new ArrayList<>(uniqueCombinations);
    }

    private void combineHelper(List<Long> userIds, List<Long> current,
                               Set<List<Long>> results, int start, int markRounds) {
        if (current.size() == markRounds) {
            // 生成已排序的组合，确保[1,2]和[2,1]被视为相同
            List<Long> sortedCombination = new ArrayList<>(current);
            sortedCombination.sort(Long::compareTo);
            results.add(sortedCombination);
            return;
        }

        for (int i = start; i < userIds.size(); i++) {
            // 跳过重复元素（如果输入列表可能包含重复值）
            if (i > start && userIds.get(i).equals(userIds.get(i - 1))) {
                continue;
            }

            current.add(userIds.get(i));
            combineHelper(userIds, current, results, i + 1, markRounds); // 从i+1开始，避免重复选择
            current.remove(current.size() - 1);
        }
    }

    @Override
    public R drawDendrogram(Long projectId, boolean refresh) {
        File pngFile = new File(Constant.getHomeDir(Constant.DirectoryEnum.temp), projectId + ".png");
        if (!refresh && FileUtil.exist(pngFile)) {
            return R.ok();
        }

        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery()
                .eq(Project::getProjectId, projectId));

        if (project == null) {
            throw new RRException("未查询到有关项目");
        }

        Integer markRounds = project.getMarkRounds();
        if (markRounds < 2) {
            return R.error(HttpStatus.SC_SEE_OTHER, "标注轮数小于2，不支持计算");
        }
        List<ProjectUser> users = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                .eq(ProjectUser::getProjectId, projectId)
                .eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));

        if (CollUtil.isEmpty(users)) {
            throw new RRException("项目未配置标注员");
        }

        // 找出这个项目中有效的文章
        List<Long> noteIds = noteTaskDao.selectBatchAnnotatorNote2(projectId);

        if (CollUtil.isEmpty(noteIds)) {
            return R.error(HttpStatus.SC_SEE_OTHER, "暂无有效标注结果数据，请先去标注数据");
        }

        Set<Long> userIds = users.stream().map(ProjectUser::getUserId).collect(Collectors.toSet());

        // 计算K的中间矩阵
        LinkedHashMap<String, Set<String>> entityMatrixMap = new LinkedHashMap<>();

        Map<Long, String> usernameMap = new HashMap<>();

        for (Long noteId : noteIds) {
            for (Long userId : userIds) {
                String username;
                if (usernameMap.containsKey(userId)) {
                    username = usernameMap.get(userId);
                } else {
                    username = getUsernameById(userId);
                    usernameMap.put(userId, username);
                }

                // 根据noteId和用户ID找到标注结果
                List<Entity> entityList = findByNoteIdAndAnnotator(noteId, userId);
                if (CollUtil.isEmpty(entityList)) {
                    continue;
                }

                for (Entity entity : entityList) {
                    String colKey = noteId + "_" + entity.getMd5();

                    // 实体
                    if (entity.getIsAttr() == 0) {
                        // 构建矩阵的第一列
                        if (entityMatrixMap.containsKey(username)) {
                            Set<String> colMap = entityMatrixMap.get(username);
                            colMap.add(colKey);
                            entityMatrixMap.put(username, colMap);
                        } else {
                            Set<String> colMap = new HashSet<>();
                            colMap.add(colKey);
                            entityMatrixMap.put(username, colMap);
                        }
                    }
                }
            }
        }

        if (entityMatrixMap.size() < 2) {
            return R.error(HttpStatus.SC_SEE_OTHER, "标注人员数量未达到计算条件");
        }

        Map<String, Map<String, Double>> matrix = calculateJaccardMatrix(entityMatrixMap);

        int i = MathUtils.calculateDendrogram(matrix, projectId);
        if (i != 0) {
            throw new RRException("聚类树图计算出错，请联系管理员");
        }
        return R.ok();
    }

    public static Map<String, Map<String, Double>> calculateJaccardMatrix(Map<String, Set<String>> map) {
        // 将结果存入Jaccard相似性矩阵
        Map<String, Map<String, Double>> similarityMatrix = new LinkedHashMap<>();

        // 获取标注员名称列表
        List<String> annotators = new ArrayList<>(map.keySet());

        // 遍历每对标注员计算相似度
        for (int i = 0; i < annotators.size(); i++) {
            String annotator1 = annotators.get(i);
            similarityMatrix.putIfAbsent(annotator1, new LinkedHashMap<>());

            for (int j = i + 1; j < annotators.size(); j++) {
                String annotator2 = annotators.get(j);

                // 计算Jaccard相似性
                double jaccardSimilarity = calculateJaccard(map.get(annotator1), map.get(annotator2));

                // 存储结果
                similarityMatrix.get(annotator1).put(annotator2, jaccardSimilarity);
                similarityMatrix.putIfAbsent(annotator2, new LinkedHashMap<>());
                similarityMatrix.get(annotator2).put(annotator1, jaccardSimilarity);
            }
        }

        return similarityMatrix;
    }

    private static double calculateJaccard(Set<String> set1, Set<String> set2) {
        // 按文章ID对实体进行分组
        Map<String, Set<String>> articlesMap1 = groupByArticleId(set1);
        Map<String, Set<String>> articlesMap2 = groupByArticleId(set2);

        int intersectionSize = 0;
        int unionSize = 0;

        // 遍历文章ID，计算每篇文章的交集和并集
        for (String articleId : articlesMap1.keySet()) {
            if (articlesMap2.containsKey(articleId)) {
                Set<String> entities1 = articlesMap1.get(articleId);
                Set<String> entities2 = articlesMap2.get(articleId);

                // 计算交集和并集大小
                Set<String> intersection = new HashSet<>(entities1);
                intersection.retainAll(entities2);

                Set<String> union = new HashSet<>(entities1);
                union.addAll(entities2);

                intersectionSize += intersection.size();
                unionSize += union.size();
            }
        }

        // 如果并集为0，返回0避免除0错误
        return unionSize == 0 ? 0.0 : (double) intersectionSize / unionSize;
    }

    private static Map<String, Set<String>> groupByArticleId(Set<String> entitySet) {
        Map<String, Set<String>> articlesMap = new HashMap<>();

        for (String entity : entitySet) {
            String[] parts = entity.split("_");
            String articleId = parts[0];

            articlesMap.putIfAbsent(articleId, new HashSet<>());
            articlesMap.get(articleId).add(entity);
        }

        return articlesMap;
    }

    public AnnoKappaAlpha calculationConsistency(List<Long> userIds, Integer markRounds, Long projectId, Long batchId, Long targetLabelId) {
        AnnoKappaAlpha kappaAlpha = new AnnoKappaAlpha();

        // 找出多个标注员共同标注过的有效文章
        List<Long> noteIds;

        if (batchId != null) {
            noteIds = noteTaskDao.selectBatchAnnotatorNoteByBatch(batchId, userIds, markRounds);
        } else {
            noteIds = noteTaskDao.selectBatchAnnotatorNoteByProject(projectId, userIds, markRounds);
        }

        if (CollUtil.isEmpty(noteIds)) {
            return null;
        }

        // 计算K的中间矩阵
        Map<String, Map<String, Integer>> entityMatrixMap = new HashMap<>();
        Map<String, Map<String, Integer>> attrMatrixMap = new HashMap<>();

        for (Long noteId : noteIds) {
            for (Long userId : userIds) {

                // 根据noteId和用户ID找到标注结果
                List<Entity> entityList = findByNoteIdAndAnnotator(noteId, userId);
                if (CollUtil.isEmpty(entityList)) {
                    continue;
                }

                for (Entity entity : entityList) {
                    Long labelId = entity.getLabelId();
                    if (labelId != null && targetLabelId != null && !Objects.equals(labelId, targetLabelId)) {
                        continue;
                    }
                    String md5 = entity.getMd5();
                    String rowKey = noteId + "_" + md5;

                    // 实体
                    if (entity.getIsAttr() == 0) {
                        // 构建矩阵的第一列
                        String colKey = String.valueOf(labelId);

                        if (entityMatrixMap.containsKey(rowKey)) {
                            Map<String, Integer> colMap = entityMatrixMap.get(rowKey);
                            if (colMap.containsKey(colKey)) {
                                Integer val = colMap.get(colKey);
                                colMap.put(colKey, val + 1);
                            } else {
                                colMap.put(colKey, 1);
                            }
                            entityMatrixMap.put(rowKey, colMap);
                        } else {
                            Map<String, Integer> colMap = new HashMap<>();
                            colMap.put(colKey, 1);
                            entityMatrixMap.put(rowKey, colMap);
                        }
                    } else {
                        String attrId = entity.getId();
                        List<Attributes> attributesList = findAttributesByAttrId(attrId);
                        for (Attributes attributes : attributesList) {
                            // 实体1_attr1
                            String colKey = noteId + "_" + md5 + "_" + attributes.getAttrLabelId();

                            // 属性
                            if (attrMatrixMap.containsKey(rowKey)) {
                                Map<String, Integer> colMap = attrMatrixMap.get(rowKey);
                                if (colMap.containsKey(colKey)) {
                                    Integer val = colMap.get(colKey);
                                    colMap.put(colKey, val + 1);
                                } else {
                                    colMap.put(colKey, 1);
                                }
                                attrMatrixMap.put(rowKey, colMap);
                            } else {
                                Map<String, Integer> colMap = new HashMap<>();
                                colMap.put(colKey, 1);
                                attrMatrixMap.put(rowKey, colMap);
                            }
                        }
                    }
                }
            }
        }

        if (entityMatrixMap.isEmpty()) {
            return kappaAlpha;
        }
        // 计算实体的 Fleiss' kappa
        double[][] entityMatrix = convertToMatrix(entityMatrixMap, markRounds);
        if (targetLabelId != null) {
            kappaAlpha.setEntityKappa(MathUtils.gwetsAC1(entityMatrix));
        } else {
            double entityFleissKappa = MathUtils.fleissKappa(entityMatrix, entityMatrix.length, entityMatrix[0].length, markRounds);
            kappaAlpha.setEntityKappa(entityFleissKappa);

            // 计算实体的 Krippendorff's alpha
            int[][] convertedMatrix1 = convertMatrix(entityMatrix, markRounds);
            double alpha1 = MathUtils.calculateKrippendorffAlpha(convertedMatrix1);
            kappaAlpha.setEntityAlpha(alpha1);
        }

        if (attrMatrixMap.isEmpty()) {
            return kappaAlpha;
        }
        // 计算属性的 Fleiss' kappa
        double[][] attrMatrix = convertToMatrix(attrMatrixMap, markRounds);
        double attrFleissKappa = MathUtils.fleissKappa(attrMatrix, attrMatrix.length, attrMatrix[0].length, markRounds);
        kappaAlpha.setAttrKappa(attrFleissKappa);

        // 计算属性的 Krippendorff's alpha
        int[][] convertedMatrix = convertMatrix(attrMatrix, markRounds);
        double alpha2 = MathUtils.calculateKrippendorffAlpha(convertedMatrix);
        kappaAlpha.setAttrAlpha(alpha2);

        return kappaAlpha;
    }

    public static int[][] convertMatrix(double[][] matrix, int numAnnotators) {
        List<int[]> convertedList = new ArrayList<>();  // 用于保存转换后的标注者分类

        // 遍历每个样本的标注数据
        for (double[] doubles : matrix) {
            // 为每个样本创建一个标注者的标注结果列表
            int[] annotators = new int[numAnnotators];  // 存放每个标注员的标注类别
            int annotatorIndex = 0;  // 标注员索引

            // 遍历每个类别，填充标注员的标注结果
            for (int j = 0; j < doubles.length; j++) {
                double numVotes = doubles[j];  // 类别 j 被标记的次数

                // 将 numVotes 个标注员的标注结果设置为类别 j
                for (int k = 0; k < (int) numVotes; k++) {  // 强制转换为整数，确保循环次数为整数
                    if (annotatorIndex < numAnnotators) {
                        annotators[annotatorIndex] = j;
                        annotatorIndex++;
                    }
                }
            }

            // 将标注员的标注结果加入转换后的矩阵
            convertedList.add(annotators);
        }

        // 将 List<double[]> 转换为二维数组
        int[][] resultMatrix = new int[convertedList.size()][numAnnotators];
        for (int i = 0; i < convertedList.size(); i++) {
            resultMatrix[i] = convertedList.get(i);
        }

        return resultMatrix;
    }

    private List<Entity> findByNoteIdAndAnnotator(Long noteId, Long annotatorId) {
        Query query = Query.query(Criteria.where("note_id").is(noteId).and("annotator_id").is(annotatorId).and("deleted").is(false));
        query.fields().include("id").include("md5").include("label_id").include("is_attr");
        return mongoTemplate.find(query, Entity.class);
    }

    private List<Attributes> findAttributesByAttrId(String attrId) {
        Query query = Query.query(Criteria.where("attribute_id").is(attrId).and("deleted").is(false));
        query.fields().include("attr_label_id");
        return mongoTemplate.find(query, Attributes.class);
    }

    public static double[][] convertToMatrix(Map<String, Map<String, Integer>> kMatrix, int annotatorCount) {
        // 获取所有列的并集，添加"未标注"列
        Set<String> allColumns = new HashSet<>();
        for (Map<String, Integer> innerMap : kMatrix.values()) {
            allColumns.addAll(innerMap.keySet());
        }
        allColumns.add(NO_ANNO);

        // 构建列名与索引的映射
        List<String> columnList = new ArrayList<>(allColumns);
        int colCount = columnList.size();
        Map<String, Integer> colIndexMap = new HashMap<>();
        for (int i = 0; i < colCount; i++) {
            colIndexMap.put(columnList.get(i), i);
        }

        // 构建结果矩阵，矩阵行数等于外层Map的size，列数等于所有列的并集
        int rowCount = kMatrix.size();
        double[][] resultMatrix = new double[rowCount][colCount];

        // 填充矩阵，同时处理标注结果数超出annotatorCount的情况
        int rowIndex = 0;
        for (Map.Entry<String, Map<String, Integer>> outerEntry : kMatrix.entrySet()) {
            Map<String, Integer> innerMap = outerEntry.getValue();
            double sum = 0;

            // 计算当前行的标注结果总数
            for (int count : innerMap.values()) {
                sum += count;
            }

            // 如果标注结果数超过了annotatorCount，按比例缩放
            double scale = sum > annotatorCount ? annotatorCount / sum : 1.0;

            // 填充当前行的矩阵
            for (Map.Entry<String, Integer> innerEntry : innerMap.entrySet()) {
                String key = innerEntry.getKey();
                int colIndex = colIndexMap.get(key);
                resultMatrix[rowIndex][colIndex] = innerEntry.getValue() * scale;
            }

            // 如果某些列没有标注结果，设置"未标注"列的值
            if (sum < annotatorCount) {
                int unknownColIndex = colIndexMap.get(NO_ANNO);
                resultMatrix[rowIndex][unknownColIndex] = annotatorCount - sum;
            }

            rowIndex++;
        }

        return resultMatrix;
    }

    // 用户修改名称的概率非常小，弄个缓存放着
    private final Map<Long, String> usernameMap = new HashMap<>();

    private String getUsernameById(Long userId) {
        if (userId == null) {
            return null;
        }
        if (usernameMap.containsKey(userId)) {
            return usernameMap.get(userId);
        }
        SysUserEntity userEntity = userService.getById(userId);
        if (userEntity == null) {
            return null;
        }
        usernameMap.put(userId, userEntity.getUsername());
        return userEntity.getUsername();
    }
}
