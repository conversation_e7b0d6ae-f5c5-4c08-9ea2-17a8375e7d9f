package org.biosino.nlp.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导入日志记录表
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Data
@TableName("t_import_log")
public class ImportLog implements Serializable {

    @TableId
    private Long id;

    private Long projectId;

    private Long batchId;

    private String name;

    private Integer type;

    private Date createTime;

    private Date finishTime;

    private Integer status;

    private Integer enabled;

    private String importFilePath;

    private String logFilePath;

    private Long userId;

    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private Object data;

}
