package org.biosino.nlp.modules.project.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标注员、审核员详情
 *
 * <AUTHOR>
 * @date 2023-5-26
 */
@Data
public class AnnoDetailsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long batchId;
    private String batchName;
    private Long taskId;
    private String articleId;
    private Integer step;
    private String stepStr;

    private Long entityCount = 0L;
    private Long correctEntityCount = 0L;

    private Long attrCount = 0L;
    private Long correctAttrCount = 0L;

    private Long entityAttrCount = 0L;
    private Long correctEntityAttr = 0L;

    private Long relationGroupCount = 0L;
    private Long correctRelationGroupCount = 0L;

    private Long relationCount = 0L;
    private Long correctRelationCount = 0L;

    // 应标实体数
    private Long needTotal = 0L;
    // 标对数TP
    private Long correctTotal = 0L;
    // 标错数FP
    private Long errorTotal = 0L;
    // 漏标数FN
    private Long missTotal = 0L;

    private Long auditorId;
    private String auditor;
    private Integer invalid;

    private Date annoStartTime;
    private Date annoEndTime;
    private Date auditStartTime;
    private Date auditEndTime;
    private Long diffTime;
    private String repulseMsg;
    /**
     * 正确率
     */
    private Double correctRate = 0d;
}
