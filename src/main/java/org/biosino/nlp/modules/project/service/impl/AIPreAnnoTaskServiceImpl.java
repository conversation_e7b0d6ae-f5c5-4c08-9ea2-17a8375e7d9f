package org.biosino.nlp.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingType;
import com.knuddels.jtokkit.api.IntArrayList;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.AnnoModuleEnum;
import org.biosino.nlp.common.enums.ExportFileTypeEnum;
import org.biosino.nlp.common.enums.ImportStatusEnum;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.*;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.mongo.DocumentRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.biosino.nlp.modules.project.dao.AIPreAnnoTaskDao;
import org.biosino.nlp.modules.project.dto.*;
import org.biosino.nlp.modules.project.entity.AIPreAnnoTask;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.*;
import org.biosino.nlp.modules.project.vo.AIPreAnnoTaskVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.biosino.nlp.modules.project.service.impl.DataExportServiceImpl.objectListToMapListWithJSON;

/**
 * AI预标注任务服务实现类
 *
 * @date 2023/11/28
 */
@Service("aiPreAnnoTaskService")
@Slf4j
public class AIPreAnnoTaskServiceImpl extends ServiceImpl<AIPreAnnoTaskDao, AIPreAnnoTask> implements AIPreAnnoTaskService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private NoteService noteService;

    @Autowired
    private NoteTaskService noteTaskService;

    @Autowired
    private DataExportService dataExportService;

    @Autowired
    private EntityLabelService entityLabelService;

    @Autowired
    private AttributeLabelService attributeLabelService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private PreAnnotationService preAnnotationService;

    @PostConstruct
    public void init() {
        // 把数据是running的记录改为暂停
        Wrapper<AIPreAnnoTask> updateWrapper = Wrappers.<AIPreAnnoTask>lambdaUpdate()
                .eq(AIPreAnnoTask::getStatus, ImportStatusEnum.processing.getValue())
                .set(AIPreAnnoTask::getStatus, ImportStatusEnum.pause.getValue());
        this.update(updateWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTask(AIPreAnnoTaskDTO dto, Long userId) {
        // 计算prompt的token
        if (countTiktoken(dto.getPrompt()) > 50000) {
            throw new RRException("prompt过长，请不要超过5万token!");
        }

        // 1. 创建任务记录
        AIPreAnnoTask task = new AIPreAnnoTask();
        // 如果是全选，就得查出所有金标准文献数据
        if (dto.getSourceSelectAllPage()) {
            List<Note> notes = noteService.findAllByBatchIdAndStepAndNotInvalid(dto.getSourceBatchId(), NoteStepEnum.reviewed.getCode());
            List<String> articleIds = notes.stream().map(Note::getArticleId).distinct().collect(Collectors.toList());
            dto.setSourceArticleIds(articleIds);
        }
        // 如果是全选，就得查出改批次下所有的文献数据
        if (dto.getSelectAllPage()) {
            List<Note> notes = noteService.findAllByBatchIdAndStepAndNotInvalid(dto.getBatchId(), NoteStepEnum.findStep(dto.getActiveStep()));
            List<String> articleIds = notes.stream().map(Note::getArticleId).distinct().collect(Collectors.toList());
            List<String> documentIds = notes.stream().map(Note::getDocumentId).distinct().collect(Collectors.toList());
            dto.setArticleIds(articleIds);
            dto.setDocumentIds(documentIds);
        } else {
            List<Note> noteList = noteService.findAllByBatchIdAndArticleIdInAndNotInvalid(dto.getBatchId(), dto.getArticleIds());
            dto.setDocumentIds(noteList.stream().map(Note::getDocumentId).collect(Collectors.toList()));
        }
        // 属性拷贝
        BeanUtils.copyProperties(dto, task);
        task.setStatus(ImportStatusEnum.processing.getValue()); // 0-运行中
        task.setCreateTime(new Date());
        task.setCreator(userId);
        // 保存
        this.save(task);

        // 2. 创建AI预标注文件夹
        File taskDir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.ai_anno), task.getId().toString());
        if (!FileUtil.exist(taskDir)) {
            FileUtil.mkdir(taskDir);
        }

        // 3. 从MongoDB获取文献数据并生成article.json文件
        List<Map<String, Object>> articleList = new ArrayList<>();

        List<Document> documents = documentRepository.findAllByIdIn(dto.getDocumentIds());

        // 处理待标注文献
        for (Document document : documents) {
            if (document != null) {
                Map<String, String> idToContent = NoteServiceImpl.extractIdAndContentByDoc(document);
                Map<String, Object> articleMap = new HashMap<>();
                articleMap.put("article_id", document.getArticleId());
                articleMap.put("text_list", idToContent.values());
                articleList.add(articleMap);
            }
        }

        // 4. 生成article.json文件
        String articleJson = JSON.toJSONString(articleList);
        FileUtil.writeUtf8String(articleJson, taskDir + File.separator + "input_article.json");

        // 5. 保存prompt到prompt.txt
        if (StrUtil.isNotBlank(dto.getPrompt())) {
            FileUtil.writeUtf8String(dto.getPrompt(), taskDir + File.separator + "prompt.txt");
        }

        // 6. 将标签数据写入到txt
        List<EntityLabel> entityLabels = entityLabelService.findAllEnableByProjectId(dto.getProjectId());
        List<Long> entityLabelIds = entityLabels.stream().map(EntityLabel::getId).collect(Collectors.toList());
        Map<Long, List<AttributeLabel>> entityLabelIdToAttrLabelMap = attributeLabelService.findAllEnableMapByEntityLabelIdIn(entityLabelIds);

        ArrayList<String> labelList = new ArrayList<>();
        ArrayList<String> entityLabelWithAttr = new ArrayList<>();

        for (EntityLabel label : entityLabels) {
            entityLabelWithAttr.add(label.getName());
            labelList.add(label.getName());

            List<AttributeLabel> attributeLabels = entityLabelIdToAttrLabelMap.get(label.getId());
            if (CollUtil.isNotEmpty(attributeLabels)) {
                for (AttributeLabel attributeLabel : attributeLabels) {
                    labelList.add(label.getName() + ":" + attributeLabel.getName());
                }
            }
        }
        // label_list 是实体标签+属性标签数据
        FileUtil.writeUtf8Lines(labelList, FileUtil.file(taskDir, "label_list.txt"));
        // entity_with_attr 是带有属性标签的实体标签数据
        FileUtil.writeUtf8Lines(entityLabelWithAttr, FileUtil.file(taskDir, "entity_with_attr.txt"));

        // 开始运行
        ThreadUtil.execAsync(() -> runScript(task));

        return task.getId();
    }

    private void runScript(AIPreAnnoTask task) {
        File taskDir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.ai_anno), task.getId().toString());

        String scriptPath = Constant.getHomeDir(Constant.DirectoryEnum.script) + "/ai_pre_anno/main.py";
        String[] cmds = new String[]{"python3", scriptPath,
                "--input_dir", taskDir.getAbsolutePath(),
                "--chunk_size", String.valueOf(task.getChunkSize()),
                "--model_name", task.getAiModel(),
                "--end_point", task.getEndpoint(),
                "--api_key", task.getApiKey()};

        log.info("执行命令: {}", StrUtil.join(" ", cmds));
        try {
            int exitCode = RuntimeUtils.execForExitCode(ArrayUtil.join(cmds, " "), task.getId().toString());
            if (exitCode == 0) {
                task.setStatus(ImportStatusEnum.finish.getValue());
            } else {
                task.setStatus(ImportStatusEnum.fail.getValue());
            }
            // 读取token计数文件
            readTokenCount(taskDir, task);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            task.setStatus(ImportStatusEnum.fail.getValue());
        } finally {
            // 只有在任务未被暂停的情况下才设置结束时间和更新状态
            AIPreAnnoTask currentTask = this.getById(task.getId());
            if (!currentTask.getStatus().equals(ImportStatusEnum.pause.getValue())) {
                task.setEndTime(new Date());
                this.updateById(task);
            }
        }
    }

    /**
     * 读取token计数文件并更新任务的token统计信息
     *
     * @param taskDir 任务目录
     * @param task    AI预标注任务
     */
    private void readTokenCount(File taskDir, AIPreAnnoTask task) {
        File tokenCountFile = FileUtil.file(taskDir, "token_count.txt");
        if (!FileUtil.exist(tokenCountFile)) {
            log.warn("Token计数文件不存在: {}", tokenCountFile.getAbsolutePath());
            return;
        }

        try {
            // 使用CsvUtil读取tab分隔的文件
            CsvReader reader = CsvUtil.getReader(CsvReadConfig.defaultConfig().setFieldSeparator('\t'));

            CsvData read = reader.read(tokenCountFile);
            List<CsvRow> rows = read.getRows();

            if (CollUtil.isEmpty(rows)) {
                log.warn("Token计数文件为空");
                return;
            }

            // 初始化累计值
            long totalPromptTokens = 0L;
            long totalCompletionTokens = 0L;
            long totalAllTokens = 0L;

            for (CsvRow row : rows) {
                totalPromptTokens += Long.parseLong(row.get(0));
                totalCompletionTokens += Long.parseLong(row.get(1));
                totalAllTokens += Long.parseLong(row.get(2));
            }

            // 设置累计的token计数
            task.setPromptTokens(totalPromptTokens);
            task.setCompletionTokens(totalCompletionTokens);
            task.setTotalTokens(totalAllTokens);

        } catch (Exception e) {
            log.error("读取Token计数文件时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取JSON结果文件并导出到Excel
     */
    public void exportJsonResultToExcel(File taskDir) {
        try {
            // 读取processed_result.json文件
            File jsonFile = FileUtil.file(taskDir, "processed_result.json");
            if (!FileUtil.exist(jsonFile)) {
                log.warn("JSON结果文件不存在: {}", jsonFile.getAbsolutePath());
                return;
            }

            // 解析JSON文件为EntityAttrPreAnnoDTO列表
            String jsonContent = FileUtil.readUtf8String(jsonFile);
            List<EntityAttrPreAnnoDTO> preAnnoList = JSON.parseArray(jsonContent, EntityAttrPreAnnoDTO.class);

            if (CollUtil.isEmpty(preAnnoList)) {
                log.warn("JSON结果文件为空或解析失败");
                return;
            }

            // 转换为Excel导出格式
            List<PreAnnoExcelDTO> excelDTOs = convertToPreAnnoExcelDTOs(preAnnoList);

            if (CollUtil.isEmpty(excelDTOs)) {
                log.warn("转换后的Excel数据为空");
                return;
            }

            // 创建Excel文件
            File excelFile = FileUtil.file(taskDir, "ai_pre_anno_result.xlsx");
            ExcelWriter writer = null;
            try {
                writer = ExcelUtil.getWriter(excelFile);
                writer.renameSheet("AI预标注结果");
                HutoolExcelUtil.setHeaderAlias(writer, PreAnnoExcelDTO.class);
                writer.write(objectListToMapListWithJSON(excelDTOs, PreAnnoExcelDTO.class), true);
                HutoolExcelUtil.setSizeColumn(writer.getSheet(), PreAnnoExcelDTO.class.getDeclaredFields().length - 1);

                log.info("AI预标注结果已导出到Excel文件: {}", excelFile.getAbsolutePath());
            } finally {
                IoUtil.close(writer);
            }

        } catch (Exception e) {
            log.error("导出JSON结果到Excel失败", e);
        }
    }

    /**
     * 将EntityAttrPreAnnoDTO列表转换为PreAnnoExcelDTO列表
     */
    private List<PreAnnoExcelDTO> convertToPreAnnoExcelDTOs(List<EntityAttrPreAnnoDTO> preAnnoList) {
        List<PreAnnoExcelDTO> excelDTOs = new ArrayList<>();

        for (EntityAttrPreAnnoDTO preAnno : preAnnoList) {
            String articleId = preAnno.getArticleId();

            for (EntityAttrPreAnnoDTO.Info entityInfo : preAnno.getEntities()) {
                String label = entityInfo.getLabel();
                // 处理实体信息
                if (CollUtil.isNotEmpty(entityInfo.getEntity())) {
                    List<AnnoDTO.EntityInfo> entities = entityInfo.getEntity();

                    for (AnnoDTO.EntityInfo entity : entities) {
                        PreAnnoExcelDTO dto = new PreAnnoExcelDTO();

                        // 基本信息
                        dto.setArticleId(articleId);
                        dto.setTextId(entity.getTextId());
                        dto.setType(label);
                        dto.setName(entity.getContent());
                        dto.setStart(entity.getStart().toString());
                        dto.setEnd(entity.getEnd().toString());

                        // 处理属性信息
                        if (CollUtil.isNotEmpty(entityInfo.getAttribute())) {
                            List<String> attrTexts = entityInfo.getAttribute().stream().map(x -> StrUtil.format("【{}】:{}", x.getAttrName(),
                                            x.getAttrs().stream().map(z -> z.getEntity().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" "))).collect(Collectors.joining(""))
                                    )
                            ).collect(Collectors.toList());
                            dto.setAttr(String.join(";", attrTexts));
                            dto.setAttrInfo(JSON.toJSONString(entityInfo.getAttribute()));
                        }

                        excelDTOs.add(dto);
                    }
                }
            }
        }

        return excelDTOs;
    }

    @Override
    public List<EntityAnnoCleanJsonDTO> getCleanEntityAnnoData(AIPreAnnoTaskDTO dto) {
        Long projectId = dto.getProjectId();
        List<String> articleIds = dto.getSourceArticleIds();
        if (dto.getSourceSelectAllPage()) {
            List<Note> noteList = noteService.findAllByBatchIdAndStepAndNotInvalid(dto.getSourceBatchId(), NoteStepEnum.reviewed.getCode());
            articleIds = noteList.stream().map(Note::getArticleId).collect(Collectors.toList());
            dto.setSourceDocumentIds(noteList.stream().map(Note::getDocumentId).collect(Collectors.toList()));
        } else {
            List<Note> noteList = noteService.findAllByBatchIdAndArticleIdInAndNotInvalid(dto.getSourceBatchId(), articleIds);
            dto.setSourceDocumentIds(noteList.stream().map(Note::getDocumentId).collect(Collectors.toList()));
        }

        // 查询金标准的实体标注数据（数据格式是 预标注数据导出 的格式）
        List<EntityAnnoJsonDTO> dataList = getGoldStandardEntityAnnoData(projectId, dto.getSourceBatchId(), articleIds);

        List<EntityAnnoCleanJsonDTO> result = new ArrayList<>();

        // 找出标注的文献信息
        List<Document> documents = documentRepository.findAllByIdIn(dto.getSourceDocumentIds());
        Map<String, Document> articleIdToDocument = documents.stream().collect(Collectors.toMap(Document::getArticleId, Function.identity(), (o, n) -> n, LinkedHashMap::new));

        // 把实体标注数据摊平，将实体标注下的属性标注也视为 实体标注
        for (EntityAnnoJsonDTO entityAnnoJsonDTO : dataList) {
            EntityAnnoCleanJsonDTO item = new EntityAnnoCleanJsonDTO();

            String articleId = entityAnnoJsonDTO.getArticleId();
            Document document = articleIdToDocument.get(articleId);

            item.setArticleId(articleId);
            Map<String, String> idToContent = NoteServiceImpl.extractIdAndContentByDoc(document);
            item.setContent(CollUtil.join(idToContent.values(), "\n"));

            List<EntityAnnoCleanJsonDTO.EntityItem> entityItems = new ArrayList<>();

            for (EntityAnnoJsonDTO.Info entityInfo : entityAnnoJsonDTO.getEntities()) {
                for (EntityAnnoJsonDTO.EntityInfo info : entityInfo.getEntity()) {
                    EntityAnnoCleanJsonDTO.EntityItem entityItem = new EntityAnnoCleanJsonDTO.EntityItem();
                    entityItem.setLabel(entityInfo.getLabel());
                    entityItem.setContent(info.getContent());
                    entityItems.add(entityItem);
                }
                if (CollUtil.isNotEmpty(entityInfo.getAttribute())) {
                    for (EntityAnnoJsonDTO.AttrInfo attrInfo : entityInfo.getAttribute()) {
                        for (EntityAnnoJsonDTO.Attribute attr : attrInfo.getAttrs()) {
                            for (EntityAnnoJsonDTO.EntityInfo info : attr.getEntity()) {
                                EntityAnnoCleanJsonDTO.EntityItem entityItem = new EntityAnnoCleanJsonDTO.EntityItem();
                                entityItem.setLabel(entityInfo.getLabel() + ":" + attrInfo.getAttrName());
                                entityItem.setContent(info.getContent());
                                entityItems.add(entityItem);
                            }
                        }
                    }
                }
            }
            item.setEntities(entityItems);

            result.add(item);
        }
        return result;
    }

    public List<EntityAnnoJsonDTO> getGoldStandardEntityAnnoData(Long projectId, Long batchId, List<String> articleIds) {
        // 获取一个临时目录，然后写入
        File tempDir = Constant.getTempDir(null);
        // 将数据导入到临时目录
        exportGoldStandardEntityData(projectId, batchId, articleIds, tempDir);
        // 找到临时目录下，*实体标注数据.json
        File file = null;
        for (File loopFile : FileUtil.loopFiles(tempDir)) {
            if (StrUtil.containsIgnoreCase(loopFile.getName(), "实体标注数据.json")) {
                file = loopFile;
                break;
            }
        }
        if (file == null) {
            throw new RRException("未找到实体标注数据.json文件");
        }
        return JSON.parseArray(FileUtil.readString(file, StandardCharsets.UTF_8), EntityAnnoJsonDTO.class);
    }


    /**
     * 导出金标准文献的实体标注数据
     *
     * @param projectId  项目ID
     * @param articleIds 文献ID列表
     * @param taskDir    任务目录
     */
    private void exportGoldStandardEntityData(Long projectId, Long batchId, List<String> articleIds, File taskDir) {
        if (CollUtil.isEmpty(articleIds)) {
            return;
        }

        // 1. 查询文献对应的Note和NoteTask
        LambdaQueryWrapper<Note> noteQuery = Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, projectId)
                .eq(Note::getBatchId, batchId)
                .in(Note::getArticleId, articleIds);

        List<Note> noteList = noteService.list(noteQuery);
        if (CollUtil.isEmpty(noteList)) {
            return;
        }

        List<Long> noteIds = noteList.stream().map(Note::getNoteId).collect(Collectors.toList());

        LambdaQueryWrapper<NoteTask> noteTaskQuery = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getProjectId, projectId)
                .eq(NoteTask::getBatchId, batchId)
                .in(NoteTask::getNoteId, noteIds)
                .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode())
                .eq(NoteTask::getMaster, MasterEnum.master.getValue());

        List<NoteTask> noteTaskList = noteTaskService.list(noteTaskQuery);
        if (noteTaskList.isEmpty()) {
            return;
        }

        // 2. 创建ExportDTO对象
        ExportDTO exportDTO = new ExportDTO();
        exportDTO.setProjectId(projectId);
        exportDTO.setStep(NoteStepEnum.reviewed.getCode()); // 已验收的数据 (NoteStepEnum.reviewed.getCode() = 4)
        exportDTO.setModule(AnnoModuleEnum.entity.getCode()); // 实体标注
        exportDTO.setType(ExportFileTypeEnum.json.getCode()); // JSON格式

        // 3. 直接调用DataExportService的exportAnnoData方法导出实体标注数据
        dataExportService.exportAnnoData(exportDTO, noteList, noteTaskList, null, taskDir);
    }

    @Override
    public PageUtils queryPage(AIPreAnnoTaskQueryDTO dto, Long userId) {
        LambdaQueryWrapper<AIPreAnnoTask> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), AIPreAnnoTask::getName, dto.getName())
                .eq(dto.getStatus() != null, AIPreAnnoTask::getStatus, dto.getStatus())
                .ge(dto.getStartDate() != null, AIPreAnnoTask::getCreateTime, dto.getStartDate())
                .le(dto.getEndDate() != null, AIPreAnnoTask::getCreateTime, DateUtil.format(dto.getEndDate(), "yyyy-MM-dd 23:59:59"));


        // 根据创建人ID查询
        queryWrapper.eq(AIPreAnnoTask::getCreator, userId);

        IPage<AIPreAnnoTask> page = this.page(
                new Query<AIPreAnnoTask>().getPage(dto, "createTime", false, true),
                queryWrapper
        );

        // 根据批次ID和项目ID查询批次和项目
        Set<Long> batchIdSet = new HashSet<>();
        Set<Long> projectIdSet = new HashSet<>();
        List<AIPreAnnoTask> records = page.getRecords();
        for (AIPreAnnoTask record : records) {
            batchIdSet.add(record.getSourceBatchId());
            batchIdSet.add(record.getBatchId());
            projectIdSet.add(record.getProjectId());
        }

        Map<Long, Batch> batchIdToBatchMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(batchIdSet)) {
            List<Batch> batches = batchService.listByIds(batchIdSet);
            batchIdToBatchMap = batches.stream().collect(Collectors.toMap(Batch::getBatchId, Function.identity(), (o, n) -> n, LinkedHashMap::new));
        }
        Map<Long, Project> projectIdToProjectMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(projectIdSet)) {
            List<Project> projects = projectService.listByIds(projectIdSet);
            projectIdToProjectMap = projects.stream().collect(Collectors.toMap(Project::getProjectId, Function.identity(), (o, n) -> n, LinkedHashMap::new));
        }

        // 加载每个的进度
        Map<Long, Batch> finalBatchIdToBatchMap = batchIdToBatchMap;
        Map<Long, Project> finalProjectIdToProjectMap = projectIdToProjectMap;
        IPage<AIPreAnnoTaskVO> convert = page.convert(x -> {
            AIPreAnnoTaskVO vo = new AIPreAnnoTaskVO();
            BeanUtil.copyProperties(x, vo);

            Batch sourceBatch = finalBatchIdToBatchMap.get(x.getSourceBatchId());
            Batch batch = finalBatchIdToBatchMap.get(x.getBatchId());
            Project project = finalProjectIdToProjectMap.get(x.getProjectId());
            if (sourceBatch != null) {
                vo.setSourceBatchName(sourceBatch.getName());
            }
            if (batch != null) {
                vo.setBatchName(batch.getName());
            }
            if (project != null) {
                vo.setProjectName(project.getName());
            }
            File taskDir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.ai_anno), x.getId().toString());
            // 计算进度
            int size = 0;
            try {
                File file = FileUtil.file(taskDir, "interrupt.txt");
                size = FileUtil.readUtf8Lines(file).size();
            } catch (Exception e) {
//                log.error("读取文件出错：{}", e.getMessage());
            }
            vo.setProcessSize(size);

            if (size != 0) {
                // 添加耗时
                Date endDate = x.getEndTime() != null ? x.getEndTime() : new Date();
                String processTime = DateUtil.formatBetween(x.getCreateTime(), endDate, BetweenFormatter.Level.SECOND);
                vo.setProcessTime(processTime);

                // 处理速度
                long processTimeMinutes = DateUtil.between(x.getCreateTime(), endDate, DateUnit.MINUTE);
                if (processTimeMinutes == 0) {
                    processTimeMinutes = 1;
                }
                vo.setProcessSpeed(size / processTimeMinutes);

                // 剩余时间
                long leftTimeMs = (DateUtil.between(x.getCreateTime(), endDate, DateUnit.MS) / size) * (x.getArticleIds().size() - size);
                // 换成秒
                if (leftTimeMs / 1000 < 60) {
                    leftTimeMs = 1000 * 60;
                }

                vo.setProcessLeftTime(DateUtil.formatBetween(leftTimeMs, BetweenFormatter.Level.MINUTE));
            }

            return vo;
        });

        return new PageUtils(convert);
    }

    @Override
    public ResponseEntity<Resource> download(Long id, String type) {
        AIPreAnnoTask task = getById(id);
        File taskDir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.ai_anno), task.getId().toString());
        File downloadFile;
        if (task.getStatus().equals(ImportStatusEnum.finish.getValue())) {
            if ("excel".equalsIgnoreCase(type)) {
                // 导出Excel文件
                File excelFile = FileUtil.file(taskDir, "ai_pre_anno_result.xlsx");
                if (!FileUtil.exist(excelFile)) {
                    // 如果Excel文件不存在，则生成
                    exportJsonResultToExcel(taskDir);
                }
                downloadFile = FileUtil.file(taskDir, "ai_pre_anno_result.xlsx");
            } else {
                // 默认下载JSON文件
                downloadFile = FileUtil.file(taskDir, "processed_result.json");
            }
        } else {
            downloadFile = FileUtil.file(taskDir, "run.log");
        }
        if (!FileUtil.exist(downloadFile)) {
            throw new RRException("没有找到想要的文件");
        }
        return DownloadUtil.downLoadWithResource(downloadFile, downloadFile.getName());
    }

    @Override
    public void applyPreAnno(Long id) {
        AIPreAnnoTask task = getById(id);

        File taskDir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.ai_anno), task.getId().toString());

        File jsonFile = FileUtil.file(taskDir, "processed_result.json");
        // 拷贝一个副本，这样能保证名字
        File targetFile = FileUtil.file(taskDir, StrUtil.format("ai_anno_{}_{}.json", task.getName(), task.getId()));
        FileUtil.copy(jsonFile, targetFile, true);

        preAnnotationService.uploadPreAnno("entity", task.getProjectId(), targetFile.getName(), targetFile, task.getCreator());


    }

    @Override
    public void pauseTask(Long id) {
        AIPreAnnoTask task = getById(id);
        if (task == null) {
            throw new RRException("任务不存在");
        }

        // 只有运行中的任务才能暂停
        if (!task.getStatus().equals(ImportStatusEnum.processing.getValue())) {
            throw new RRException("只有运行中的任务才能暂停");
        }
        // 先改状态
        task.setStatus(ImportStatusEnum.pause.getValue());
        this.updateById(task);

        // 停止进程
        int result = RuntimeUtils.killProcess(task.getId().toString());
        if (result == 0) {
            log.info("任务 {} 已暂停", id);
        } else {
            throw new RRException("暂停任务失败，进程可能已结束");
        }
    }

    @Override
    public void resumeTask(Long id) {
        AIPreAnnoTask task = getById(id);
        if (task == null) {
            throw new RRException("任务不存在");
        }

        // 只有暂停的任务才能恢复
        if (!task.getStatus().equals(ImportStatusEnum.pause.getValue())) {
            throw new RRException("只有暂停的任务才能恢复");
        }

        // 更新任务状态为运行中
        task.setStatus(ImportStatusEnum.processing.getValue());
        this.updateById(task);

        // 重新执行脚本
        ThreadUtil.execAsync(() -> runScript(task));
        log.info("任务 {} 已恢复执行", id);
    }

    @Override
    public List<Pair<String, Set<String>>> getLabelToEntitiesInfo(AIPreAnnoTaskDTO dto) {
        dto.setSourceSelectAllPage(true);
        List<EntityAnnoCleanJsonDTO> cleanEntityAnnoData = getCleanEntityAnnoData(dto);
        List<EntityLabel> entityLabels = entityLabelService.findAllEnableByProjectId(dto.getProjectId());

        // 使用 LinkedHashMap 保证插入顺序
        Map<String, Set<String>> tempMap = new LinkedHashMap<>();
        for (EntityLabel entityLabel : entityLabels) {
            tempMap.put(entityLabel.getName(), new HashSet<>());
            for (EntityAnnoCleanJsonDTO item : cleanEntityAnnoData) {
                for (EntityAnnoCleanJsonDTO.EntityItem entityAnno : item.getEntities()) {
                    if (entityAnno.getLabel().startsWith(entityLabel.getName())) {
                        tempMap.computeIfAbsent(entityAnno.getLabel(), k -> new HashSet<>())
                                .add(entityAnno.getContent());
                    }
                }
            }
        }

        // 转成 List<Pair>
        List<Pair<String, Set<String>>> result = new ArrayList<>();
        for (Map.Entry<String, Set<String>> entry : tempMap.entrySet()) {
            result.add(Pair.of(entry.getKey(), entry.getValue()));
        }
        return result;
    }

    @Override
    public Integer countTiktoken(String body) {
        if (StrUtil.isBlank(body)) {
            return 0;
        }
        Encoding encoding = Encodings.newDefaultEncodingRegistry().getEncoding(EncodingType.O200K_BASE);
        IntArrayList list = encoding.encode(body);
        return list.size();
    }
}
