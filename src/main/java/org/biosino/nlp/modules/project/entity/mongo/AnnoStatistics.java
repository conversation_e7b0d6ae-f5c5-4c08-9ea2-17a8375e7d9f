package org.biosino.nlp.modules.project.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * 标注员、审核员统计
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@Document(collection = "m_anno_statistics")
public class AnnoStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @Indexed
    private String createDate;

    @Indexed
    private Long projectId;
    @Indexed
    private Long batchId;
    @Indexed
    private Long userId;
    @Indexed
    private Long roleId;

    /**
     * 已标注/已审核的文章数
     */
    private Integer count = 0;

    private Long entityCount = 0L;
    private Long correctEntityCount = 0L;

    private Long attrCount = 0L;
    private Long correctAttrCount = 0L;

    private Long entityAttrCount = 0L;
    private Long correctEntityAttr = 0L;

    private Long relationGroupCount = 0L;
    private Long correctRelationGroupCount = 0L;

    private Long relationCount = 0L;
    private Long correctRelationCount = 0L;

    // 应标数
    private Long needTotal = 0L;
    // 标对数 TP
    private Long correctTotal = 0L;
    // 标错数 FP
    private Long errorTotal = 0L;
    // 漏标数 FN
    private Long missTotal = 0L;

    /**
     * 平均耗时
     */
    private Integer avgTime = 0;
}
