package org.biosino.nlp.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.project.entity.Statistics;
import org.biosino.nlp.modules.project.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-5-24
 */
public interface StatisticsService extends IService<Statistics> {

    /**
     * 概览
     */
    StatisticsVO overview(Long projectId, Long roleId, Long userId);

    /**
     * 批次信息统计
     */
    List<BatchStatisticsVO> batchesStatistics(Long projectId);

    List<BatchVO> batchCorrectChart(Long projectId);

    /**
     * 标注工作量排行榜统计
     */
    List<AnnoStatisticsVO> annotationStatistics(Long projectId);

    /**
     * 标注员和审核员标注统计
     */
    List<UserStatisticsVO> roleStatistics(Long projectId, Long batchId, Long roleId, boolean refresh);

    /**
     * 项目标注进度
     */
    List<Statistics> dateStatistics(Long projectId);

    /**
     * 统计任务，统计项目进度
     */
    void task();

    List<ProjectVO> getProjects(Long roleId, Long userId);

    List<WorkProgressVO> workProgressStatistics(Long projectId, Long roleId);

    List<AnnoDetailsVO> annoDetails(Long projectId, Long roleId, Long userId);

    void exportConsistencyToExcel(Long projectId, Long batchId, String email);

    List<KappaAlphaVO> calculationConsistency(Long projectId, Long batchId, boolean refresh);

    R drawDendrogram(Long projectId, boolean refresh);

    void annotatedStatisticsJob();

    Map<String, Object> accuracyChart(Long projectId, Long batchId);

    Map<String, Object> consistencyChart(Long projectId, Long batchId);
}
