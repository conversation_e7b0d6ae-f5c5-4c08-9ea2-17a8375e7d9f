package org.biosino.nlp.modules.project.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.project.dto.ImportLogDTO;
import org.biosino.nlp.modules.project.service.ImportLogService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 导入管理
 *
 * <AUTHOR>
 * @date 2023/4/21
 */
@RestController
@RequestMapping("/importLog")
public class ImportLogController extends AbstractController {

    @Autowired
    private ImportLogService importLogService;

    @RequestMapping("/list")
    @RequiresPermissions("project:manage")
    public R list(ImportLogDTO dto) {
        PageUtils page = importLogService.queryPage(dto, getUserId());
        return R.ok().put("page", page);
    }

    /**
     * 获取上传任务历史
     */
    @RequestMapping("/download")
    public ResponseEntity<Resource> download(String type, Long id) {
        return importLogService.download(type, id);
    }

    @RequestMapping("/changeEnabled")
    @RequiresPermissions("project:manage")
    public R changeEnabled(@RequestBody ImportLogDTO dto) {
        importLogService.changeEnabled(dto);
        return R.ok();
    }

}
