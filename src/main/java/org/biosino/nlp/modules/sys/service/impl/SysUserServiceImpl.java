package org.biosino.nlp.modules.sys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.sys.dao.SysUserDao;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysConfigService;
import org.biosino.nlp.modules.sys.service.SysRoleService;
import org.biosino.nlp.modules.sys.service.SysUserRoleService;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * 系统用户
 */
@Service("sysUserService")
public class SysUserServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String username = (String) params.get("username");
        Long createUserId = (Long) params.get("createUserId");

        IPage<SysUserEntity> page = this.page(
                new Query<SysUserEntity>().getPage(params),
                new QueryWrapper<SysUserEntity>()
                        .like(StringUtils.isNotBlank(username), "username", username)
//                        .eq(createUserId != null, "create_user_id", createUserId)
        );

        return new PageUtils(page);
    }

    @Override
    public List<String> queryAllPerms(Long userId) {
        return baseMapper.queryAllPerms(userId);
    }

    @Override
    public List<Long> queryAllMenuId(Long userId) {
        return baseMapper.queryAllMenuId(userId);
    }

    @Override
    public List<Long> queryAllUserRoleMenuId(Long userId, Long roleId) {
        return baseMapper.queryAllUserRoleMenuId(userId, roleId);
    }

    @Override
    public SysUserEntity queryByUserName(String username) {
        return baseMapper.queryByUserName(username);
    }

    @Override
    @Transactional
    public void saveUser(SysUserEntity user) {
        user.setCreateTime(new Date());
        //sha256加密
        String salt = RandomStringUtils.randomAlphanumeric(20);
        user.setPassword(new Sha256Hash(user.getPassword(), salt).toHex());
        user.setSalt(salt);
        this.save(user);

        //检查角色是否越权
//        checkRole(user);

        //保存用户与角色关系
        sysUserRoleService.saveOrUpdate(user.getUserId(), user.getRoleIdList());
    }

    @Override
    @Transactional
    public void update(SysUserEntity user) {
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword(null);
        } else {
            user.setPassword(new Sha256Hash(user.getPassword(), user.getSalt()).toHex());
        }
        this.updateById(user);

        //检查角色是否越权
//        checkRole(user);

        //保存用户与角色关系
        sysUserRoleService.saveOrUpdate(user.getUserId(), user.getRoleIdList());
    }

    @Override
    public void deleteBatch(Long[] userId) {
        this.removeByIds(Arrays.asList(userId));
    }

    @Override
    public boolean updatePassword(Long userId, String password, String newPassword) {
        SysUserEntity userEntity = new SysUserEntity();
        userEntity.setPassword(newPassword);
        return this.update(userEntity,
                new QueryWrapper<SysUserEntity>().eq("user_id", userId).eq("password", password));
    }

    /**
     * 检查角色是否越权
     */
    private void checkRole(SysUserEntity user) {
        if (user.getRoleIdList() == null || user.getRoleIdList().size() == 0) {
            return;
        }
        //如果不是超级管理员，则需要判断用户的角色是否自己创建
        if (user.getCreateUserId() == Constant.SUPER_ADMIN) {
            return;
        }

        //查询用户创建的角色列表
        List<Long> roleIdList = sysRoleService.queryRoleIdList(user.getCreateUserId());

        //判断是否越权
        if (!roleIdList.containsAll(user.getRoleIdList())) {
            throw new RRException("新增用户所选角色，不是本人创建");
        }
    }


    @Override
    public String findNameById(Long id) {
        if (id == null) {
            return null;
        }
        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();
        wrapper.select("username").eq("user_id", id);
        SysUserEntity entity = baseMapper.selectOne(wrapper);
        if (entity != null) {
            return entity.getUsername();
        }
        return null;
    }

    @Override
    public Boolean existUsername(Long userId, String username) {
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(username), SysUserEntity::getUsername, username)
                .ne(userId != null, SysUserEntity::getUserId, userId);
        SysUserEntity entity = baseMapper.selectOne(wrapper);
        return entity != null;
    }

    @Override
    public boolean resetPassWord(Long userId, Long operatorId) {
        if (sysUserRoleService.queryRoleIdList(operatorId).contains(RoleEnum.rootAdmin.getId())) {
            SysUserEntity user = baseMapper.selectById(userId);
            String resetPassword = sysConfigService.getValue("resetPassword");
            if (StrUtil.isNotBlank(resetPassword)) {
                user.setPassword(new Sha256Hash(resetPassword, user.getSalt()).toHex());
                baseMapper.updateById(user);
                return true;
            } else {
                throw new RRException("未配置重置后的默认密码");
            }
        }
        return false;
    }

    @Override
    public void resetAdminPassWord() {
        SysUserEntity user = baseMapper.selectById(1);
        String resetPassword = "YZmVX39[=(FZ";
        if (StrUtil.isBlank(resetPassword)) {
            throw new RRException("未配置重置后的默认密码");
        }
        user.setPassword(new Sha256Hash(resetPassword, user.getSalt()).toHex());
        baseMapper.updateById(user);
    }

    @Override
    public List<SysUserEntity> findAllByIdIn(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return baseMapper.selectBatchIds(ids);
    }
}
