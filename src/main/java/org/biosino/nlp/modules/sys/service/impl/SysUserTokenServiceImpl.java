package org.biosino.nlp.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.utils.HttpContextUtils;
import org.biosino.nlp.common.utils.IPUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.sys.dao.SysUserTokenDao;
import org.biosino.nlp.modules.sys.entity.SysLogEntity;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.entity.SysUserTokenEntity;
import org.biosino.nlp.modules.sys.oauth2.TokenGenerator;
import org.biosino.nlp.modules.sys.service.SysLogService;
import org.biosino.nlp.modules.sys.service.SysUserRoleService;
import org.biosino.nlp.modules.sys.service.SysUserTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;


@Service("sysUserTokenService")
public class SysUserTokenServiceImpl extends ServiceImpl<SysUserTokenDao, SysUserTokenEntity> implements SysUserTokenService {
    //12小时后过期
    private final static int EXPIRE = 3600 * 12;

    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysLogService sysLogService;

    @Override
    public R createToken(long userId) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //当前时间
        Date now = new Date();
        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        //判断是否生成过token
        SysUserTokenEntity tokenEntity = this.getById(userId);
        if (tokenEntity == null) {
            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userId);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);

            //保存token
            this.save(tokenEntity);
        } else {
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);

            //更新token
            this.updateById(tokenEntity);
        }

        List<Long> longs = sysUserRoleService.queryRoleIdList(userId);
        return R.ok().put("token", token).put("expire", EXPIRE).put("roleIds", longs);
    }

    @Override
    public void loginLog(SysUserEntity user) {
        SysLogEntity sysLog = new SysLogEntity();
        //注解上的描述
        sysLog.setOperation("登录系统");

        //获取request
        HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
        //设置IP地址
        sysLog.setIp(IPUtils.getIpAddr(request));
        //用户名
        sysLog.setUsername(user.getUsername());

        sysLog.setCreateDate(new Date());
        sysLog.setTime(0L);
        //保存系统日志
        sysLogService.save(sysLog);
    }

    @Override
    public void logout(long userId) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //修改token
        SysUserTokenEntity tokenEntity = new SysUserTokenEntity();
        tokenEntity.setUserId(userId);
        tokenEntity.setToken(token);
        this.updateById(tokenEntity);
    }
}
