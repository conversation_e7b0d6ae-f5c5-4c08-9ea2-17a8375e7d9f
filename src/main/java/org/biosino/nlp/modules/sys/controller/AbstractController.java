package org.biosino.nlp.modules.sys.controller;

import cn.hutool.core.util.StrUtil;
import org.apache.shiro.SecurityUtils;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;

/**
 * Controller公共组件
 */
public abstract class AbstractController {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    protected Long getUserId() {
        return getUser().getUserId();
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
            /**
             * 字符串去空格
             * @param text  The string to be parsed.
             * @throws IllegalArgumentException
             */
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                /* String htmlEscape = StrUtil.trimToNull(text);
                if (htmlEscape != null) {
                    // 防止XXS跨站脚本攻击
                    htmlEscape = HtmlUtils.htmlEscape(htmlEscape);
                    htmlEscape = StringUtils.replace(htmlEscape, "'", "''");
                    htmlEscape = StringUtils.replace(htmlEscape, "&gt;", ">");
                    htmlEscape = StringUtils.replace(htmlEscape, "&#39;", "'");
                } */
                setValue(StrUtil.trimToNull(text));
            }
        });
    }
}
