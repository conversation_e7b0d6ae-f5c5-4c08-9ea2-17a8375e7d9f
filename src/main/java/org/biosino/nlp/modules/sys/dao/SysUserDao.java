package org.biosino.nlp.modules.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;

import java.util.List;

/**
 * 系统用户
 */
@Mapper
public interface SysUserDao extends BaseMapper<SysUserEntity> {

    /**
     * 查询用户的所有权限
     *
     * @param userId 用户ID
     */
    List<String> queryAllPerms(Long userId);

    List<String> queryAllUserRolesPerms(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 查询用户的所有菜单ID
     */
    List<Long> queryAllMenuId(Long userId);

    /**
     * 查询用户角色的所有菜单ID
     */
    List<Long> queryAllUserRoleMenuId(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 根据用户名，查询系统用户
     */
    SysUserEntity queryByUserName(String username);

    /**
     * 查询所有用户角色ID
     */
    List<Long> queryRoleIdsByUserId(Long userId);

}
