package org.biosino.nlp.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.sys.dao.SysLogDao;
import org.biosino.nlp.modules.sys.entity.SysLogEntity;
import org.biosino.nlp.modules.sys.service.SysLogService;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("sysLogService")
public class SysLogServiceImpl extends ServiceImpl<SysLogDao, SysLogEntity> implements SysLogService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String username = (String) params.get("username");
        String operation = (String) params.get("operation");

        IPage<SysLogEntity> page = this.page(
                new Query<SysLogEntity>().getPage(params),
                new QueryWrapper<SysLogEntity>()
                        .like(StringUtils.isNotBlank(username), "username", username)
                        .like(StringUtils.isNotBlank(operation), "operation", operation)
                        .orderByDesc("create_date")
        );

        return new PageUtils(page);
    }
}
