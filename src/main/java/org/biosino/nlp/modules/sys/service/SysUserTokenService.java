package org.biosino.nlp.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.entity.SysUserTokenEntity;

/**
 * 用户Token
 */
public interface SysUserTokenService extends IService<SysUserTokenEntity> {

    /**
     * 生成token
     *
     * @param userId 用户ID
     */
    R createToken(long userId);

    /**
     * 退出，修改token值
     *
     * @param userId 用户ID
     */
    void logout(long userId);

    /**
     * 记录登录日志
     */
    void loginLog(SysUserEntity user);

}
