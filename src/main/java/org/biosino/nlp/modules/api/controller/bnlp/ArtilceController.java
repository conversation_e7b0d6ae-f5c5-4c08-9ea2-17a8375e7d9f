package org.biosino.nlp.modules.api.controller.bnlp;

import cn.hutool.core.thread.ThreadUtil;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.ArticleService;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共数据请求API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/article")
public class ArtilceController {

    @Autowired
    private ArticleService articleService;

    /**
     * 根据文章ID查询指定文章，没有版本返回最新的
     *
     * @param articleId 文章ID
     * @param version   文章版本 (可选)
     * @return 文章
     */
    @GetMapping("/findById")
    public R findArticleById(String articleId, @RequestParam(required = false) Integer version) {
        // 根据ID查询文章
        Document document = articleService.findArticleById(articleId, version);
        return R.success(document);
    }

    /**
     * 将所有文章的html字符串转义回来 &lt; ->  <
     */
    @GetMapping("/unHtmlEscape")
    public R findArticleById() {
        ThreadUtil.execAsync(articleService::unHtmlEscape);
        return R.success("start clear data");
    }

    /**
     * 将所有文章的html字符串转义回来 &lt; ->  <
     */
    @GetMapping("/unHtmlEscapeByPath")
    public R unHtmlEscapeByPath() {
        ThreadUtil.execAsync(articleService::unHtmlEscapeByPath);
        return R.success("start clear data");
    }

    @GetMapping("/addWordCount")
    public R addWordCount() {
        articleService.checkWordCountTask();
        ThreadUtil.execAsync(articleService::addWordCount);
        return R.success("start add word count");
    }

}
