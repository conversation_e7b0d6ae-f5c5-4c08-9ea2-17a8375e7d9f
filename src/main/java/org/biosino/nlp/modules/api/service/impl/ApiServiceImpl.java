package org.biosino.nlp.modules.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.enums.DeleteEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.modules.api.mapper.BatchMapper;
import org.biosino.nlp.modules.api.mapper.EntityMapper;
import org.biosino.nlp.modules.api.service.ApiService;
import org.biosino.nlp.modules.api.vo.BatchVo;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.mongo.PreAnnotation;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ApiServiceImpl implements ApiService {

    private final ProjectService projectService;
    private final EntityLabelService entityLabelService;
    private final AttributeLabelService attributeLabelService;
    private final EntityRepository entityRepository;
    private final BatchService batchService;
    private final NoteService noteService;
    private final MongoOperations mongoOperations;

    /**
     * 校验Token合法性
     *
     * @param token 项目的唯一token标识码
     */
    @Override
    public void checkToken(String token) {
        Project project = projectService.findByToken(token);
        if (project == null) {
            throw new RRException("Token认证失败");
        }
    }

    /**
     * 根据文章ID查询指定文章
     *
     * @param articleId 文章ID
     * @return 文章
     */
    @Override
    public Document findArticleById(String articleId, Integer version) {
        Query query;
        if (version != null) {
            query = Query.query(Criteria.where("article_id").is(articleId).and("version").is(version)).limit(1);
        } else {
            query = Query.query(Criteria.where("article_id").is(articleId))
                    .with(Sort.by(Sort.Direction.DESC, "version")).limit(1);
        }
        return Optional.ofNullable(mongoOperations.findOne(query, Document.class))
                .orElseThrow(() -> new RRException("未查询到该篇文献"));
    }

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 实体标签
     */
    @Override
    public EntityLabelVo findLabelById(Long id) {
        if (id == null) {
            throw new RRException("标签ID不能为空");
        }
        EntityLabel entityLabel = entityLabelService.getById(id);
        EntityLabelVo entityLabelVo = new EntityLabelVo();
        // 复制属性值
        BeanUtil.copyProperties(entityLabel, entityLabelVo);
        return entityLabelVo;
    }

    @Override
    public EntityLabelVo findLabelByCode(Long code, String projectCode) {
        return null;
    }

//    @Override
//    public EntityLabelVo findLabelByCode(Long code, String projectCode) {
//        EntityLabel entityLabel = entityLabelService.getOne(Wrappers.<EntityLabel>lambdaQuery()
//                .eq(EntityLabel::getProjectId, projectCode)
//                .eq(EntityLabel::getValue, code));
//        return EntityLabelMapper.INSTANCE.copy(entityLabel);
//    }

    @Override
    public List<BatchVo> findAllBatchInfo(String projectCode) {
        if (projectCode == null) {
            throw new RRException("projectCode不能为空");
        }
        Project project = getProject(projectCode);
        List<Batch> list = batchService.list(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, project.getProjectId()));
        return BatchMapper.INSTANCE.copy(list);
    }

    @Override
    public List<EntityDataVo> findEntityByArticleId(String articleId, String projectCode) {
        Project project = getProject(projectCode);
        Note note = noteService.getOne(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .eq(Note::getArticleId, articleId));
        if (note == null) {
            throw new RRException("该文章不存在");
        }
        List<Entity> entities = entityRepository.findAllByNoteIdAndDeleted(note.getNoteId(), DeleteEnum.not_delete.getVal());
        return EntityMapper.INSTANCE.copy(entities);
    }

    @Override
    public List<EntityDataVo> findEntityByBatchId(String batchId, String projectCode) {
        Project project = getProject(projectCode);
        Batch batch = batchService.getOne(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, project.getProjectId())
                .eq(Batch::getBatchId, batchId));
        if (batch == null) {
            throw new RRException("该批次不存在");
        }
        List<Entity> entities = mongoOperations.find(Query.query(Criteria.where("batch_id").is(batchId)), Entity.class);
        return EntityMapper.INSTANCE.copy(entities);
    }

    @Override
    public List<EntityDataVo> findAllEntity(String projectCode) {
        Project project = getProject(projectCode);
        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery().eq(Note::getProjectId, project.getProjectId()));
        if (CollUtil.isEmpty(notes)) {
            return null;
        }
        List<Long> noteIds = notes.stream().map(Note::getNoteId).collect(Collectors.toList());
        List<Entity> entities = mongoOperations.find(Query.query(Criteria.where("note_id").in(noteIds)), Entity.class);
        return EntityMapper.INSTANCE.copy(entities);
    }

    /**
     * 查询当前文章有哪些预标注来源
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注来源
     */
    @Override
    public List<SourceVO> getSource(String articleId, Integer version) {
        if (StrUtil.isBlank(articleId) || version == null) {
            return null;
        }
//        Query query = Query.query(Criteria.where("article_id").is(articleId).and("version").is(version));
//        List<String> source = mongoOperations.findDistinct(query, "source", PreAnnotation.class, String.class);
        Query query = Query.query(Criteria.where("article_id").is(articleId).and("source").ne(PreSourceEnum.SELF.getId()));
        List<Integer> source = mongoOperations.findDistinct(query, "source", Entity.class, Integer.class);
        List<SourceVO> sourceVOList = new ArrayList<>();
        for (Integer id : source) {
            if (id.equals(PreSourceEnum.CUSTOMIZE.getId())) {
                sourceVOList.add(new SourceVO(PreSourceEnum.CUSTOMIZE.getTitle(), PreSourceEnum.CUSTOMIZE.getId()));
            }
        }
        return sourceVOList;
    }

    /**
     * 查询当前文章机器预标注数据
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注数据
     */
    @Override
    public List<PreAnnotation> getPreAnnotation(String articleId, Integer version, String source) {
        if (StrUtil.isBlank(articleId) || version == null) {
            return null;
        }
        Query query = Query.query(Criteria.where("article_id").is(articleId).and("version")
                .is(version).and("source").is(source));
        query.fields().include("note_id").include("article_id").include("text_id")
                .include("start").include("end").include("end").include("label")
                .include("text").include("create_time");
        query.with(Sort.by(Sort.Order.desc("create_time")));

        List<PreAnnotation> annotationList = mongoOperations.find(query, PreAnnotation.class);
        if (CollUtil.isEmpty(annotationList)) {
            return null;
        }

        // 只取最新导入批次的
        PreAnnotation preAnnotation = annotationList.get(0);
        Long noteId = preAnnotation.getNoteId();
        return annotationList.stream().filter(x -> x.getNoteId().equals(noteId)).distinct().collect(Collectors.toList());
    }

    @Override
    public String getPreAnnotation(String articleId, Integer version, String source,
                                   String textId, Integer start, Integer end) {
        Query query = Query.query(Criteria.where("article_id").is(articleId)
                .and("version").is(version)
                .and("source").is(source)
                .and("text_id").is(textId)
                .and("start").is(start)
                .and("end").is(end));
        query.with(Sort.by(Sort.Order.desc("create_time")));
        List<PreAnnotation> preAnnotations = mongoOperations.find(query, PreAnnotation.class);
        if (CollUtil.isNotEmpty(preAnnotations)) {
            PreAnnotation annotation = preAnnotations.get(0);
            String result = "一级标签：" + annotation.getLabel();
            if (StrUtil.isNotBlank(annotation.getCode())) {
                result += "\n" + "二级标签：" + annotation.getCode();
            }
            if (StrUtil.isNotBlank(annotation.getAnnotate())) {
                result += "\n" + "备注：" + annotation.getAnnotate();
            }
            return result;
        }
        return null;
    }

    @Override
    public Boolean updatePdf(MultipartFile file) throws IOException {
        String strPath = Constant.getHomeDir().toString();
        String filename = file.getOriginalFilename();
        Map<String, File> map = new HashMap<>(16);
        Map<String, File> fileList = getFileList(map, strPath);

        File fi = fileList.get(filename);
        if (fi != null) {
            file.transferTo(fi);
            return true;
        } else {
            throw new RRException("未找到相同名称的文档");
        }
    }

    public static Map<String, File> getFileList(Map<String, File> map, String strPath) {
        File dir = new File(strPath);
        // 该文件目录下文件全部放入数组
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    getFileList(map, file.getAbsolutePath());
                } else {
                    System.out.println(file.getParent() + "-----" + file);
                    map.put(file.getName(), file);
                }
            }
        }
        return map;
    }

    private Project getProject(String projectCode) {
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery().eq(Project::getCode, projectCode));
        if (project == null) {
            throw new RRException("该项目不存在");
        }
        return project;
    }

    @Override
    public List<LabelUseVo> getLabelUseInfo(String labelCode) {
        ArrayList<LabelUseVo> result = new ArrayList<>();
        if (StrUtil.isBlank(labelCode)) {
            return result;
        }
        String[] split = labelCode.split(",");
        ArrayList<String> queryCode = new ArrayList<>();
        for (String s : split) {
            if (StrUtil.isNotBlank(s)) {
                queryCode.add(StrUtil.trim(s));
            }
        }
        if (CollUtil.isEmpty(queryCode)) {
            return result;
        }
        List<LabelUseVo> result1 = entityLabelService.getLabelUseInfo(queryCode);
        if (CollUtil.isEmpty(result1)) {
            return result;
        }
        List<LabelUseVo> result2 = attributeLabelService.getLabelUseInfo(queryCode);
        if (CollUtil.isNotEmpty(result2)) {
            result.addAll(result2);
        }


        return result;
    }
}
