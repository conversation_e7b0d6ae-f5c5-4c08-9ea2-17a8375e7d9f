package org.biosino.nlp.modules.api.controller;

import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.modules.api.service.DocService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyEditorSupport;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021-01-09 15:59
 */
@RestController
@RequestMapping("/file")
public class FileController {

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
            /**
             * 字符串去空格
             * @param text  The string to be parsed.
             * @throws IllegalArgumentException
             */
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                setValue(StrUtil.trimToEmpty(text));
            }
        });
    }

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DocService docService;

    @GetMapping("/project/doc")
    public ResponseEntity<Resource> downloadProjectDoc(Long docId) {
        return null;
    }

    /**
     * 项目规范文档预览
     * 浏览器选项卡标题：
     * Chrome：chrome选项卡的标题对于无标题属性的pdf文件，显示{title}内容。对于存在标题属性的pdf显示该属性内容;
     * Firefox：根据Content-Disposition中的filename设置标题
     */
    @GetMapping("/project/docView/{title}")
    public ResponseEntity<Resource> projectDocView(Long projectId, @PathVariable("title") String title) {
        return null;
    }

    /**
     * 帮助文档预览
     */
    @RequestMapping("/instructionPDF/{fileName}")
    public ResponseEntity<Resource> instructionPDF(@PathVariable("fileName") String fileName) {
        return docService.downloadInstructionPDF(fileName);
    }

    /**
     * 用于转发参考文献
     */
    @RequestMapping("/referencePDF/{articleId}")
    public void dispatcherPdfUrl(@PathVariable String articleId, HttpServletResponse resp) throws IOException {
        docService.downloadReferencePDF(articleId, resp);
    }

    @GetMapping("/preAnnoTemplate")
    public ResponseEntity<Object> downloadPreAnnoTemplate() throws IOException {
        return docService.downloadTemplate("pre-annotation-template.csv", "预标注-模板.csv");
    }

    @GetMapping("/relationFileTemplate")
    public ResponseEntity<Object> relationFileTemplate() throws IOException {
        return docService.downloadTemplate("relationship-template.xlsx", "关系标签-批量上传-模板.xlsx");
    }

    @GetMapping("/entityLabelTemplate")
    public ResponseEntity<Object> entityLabelTemplate() throws IOException {
        return docService.downloadTemplate("entity-label-template.xlsx", "实体标签-批量上传-模板.xlsx");
    }

    @GetMapping("/verifyTemplate")
    public ResponseEntity<Object> verifyTemplate() throws IOException {
        return docService.downloadTemplate("verify-entity-template.json", "数据验证-实体-模板.json");
    }

    @GetMapping("/umlsFileTemplate")
    public ResponseEntity<Object> umlsFileTemplate() throws IOException {
        return docService.downloadTemplate("umls-template.xlsx", "UMLS-Concept-批量上传-模板.xlsx");
    }

    @GetMapping("/downloadTemplate")
    public ResponseEntity<Object> uploadJsonTemplate(String filename, String displayName) throws IOException {
        return docService.downloadTemplate(filename, displayName);
    }

    /**
     * 测试文件路径是否正常
     */
    @RequestMapping("/test/{fileName}")
    public String test(@PathVariable("fileName") String fileName, HttpServletResponse response) throws IOException {
        return docService.test(fileName);
    }

    /**
     * 测试文件路径是否正常
     */
    @RequestMapping("/testPath/{fileName}")
    public String test2(@PathVariable("fileName") String fileName) {
        return docService.testPath();
    }
}
