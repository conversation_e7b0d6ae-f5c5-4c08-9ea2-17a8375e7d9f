package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.vo.EntityVO;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.note.vo.PageIdsVO;
import org.biosino.nlp.modules.project.dto.EntityAnnoJsonDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public interface EntityService {

    /**
     * 根据ArticleId获取指定项目某个批次文献的实体标注数据
     *
     * @param articleId   文章ArticleId
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    EntityAnnoJsonDTO findEntityByArticleId(String articleId, String projectCode);

    /**
     * 查询项目某个批次的所有实体标注数据
     *
     * @param batchId     批次ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<EntityAnnoJsonDTO> findEntityByBatchId(Long batchId, String projectCode);

    /**
     * 查询项目所有实体标注数据
     *
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<EntityAnnoJsonDTO> findAllEntity(String projectCode);

    /**
     * 根据实体标注ID实体标注数据
     *
     * @param id 实体标签的ID
     * @return 实体标签
     */
    EntityAnnoJsonDTO.Info findById(String id);


    /**
     * 根据id查询是否存在实体标注
     *
     * @param id
     * @return
     */
    boolean existsById(String id);

    PageIdsListVO queryPage(EntitySearchDTO dto);

    Map<Long, String> getUsersByLabelId(Long labelId);

    /**
     * 根据id查询在ids中的实体
     *
     * @param ids id集合
     * @return 实体列表
     */
    List<Entity> findAllByIdIn(Collection<String> ids);

    /**
     * 统计id查询在ids中的数量
     *
     * @param ids id集合
     * @return 实体数量
     */
    Long countByIdIn(Collection<String> ids);

}
