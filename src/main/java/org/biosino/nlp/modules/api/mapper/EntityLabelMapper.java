package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-30 13:16
 */
@Mapper
public interface EntityLabelMapper {

    EntityLabelMapper INSTANCE = Mappers.getMapper(EntityLabelMapper.class);

    EntityLabelVo copy(EntityLabel batch);

    List<EntityLabelVo> copy(List<EntityLabel> batches);

}
