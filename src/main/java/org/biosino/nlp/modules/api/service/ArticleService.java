package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.springframework.stereotype.Service;

@Service
public interface ArticleService {

    /**
     * 根据文章ArticleId查询指定文章，没有版本返回最新的
     *
     * @param articleId 文章ArticleId
     * @param version   文章版本
     * @return 文章
     */
    Document findArticleById(String articleId, Integer version);

    void unHtmlEscape();

    void unHtmlEscapeByPath();

    void checkWordCountTask();

    void addWordCount();
}
