package org.biosino.nlp.modules.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.service.CommonService;
import org.biosino.nlp.modules.api.service.EntityService;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.AttributeEntityService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.vo.EntityVO;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.note.vo.PageIdsVO;
import org.biosino.nlp.modules.project.dto.EntityAnnoJsonDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.service.DataExportService;
import org.biosino.nlp.modules.project.service.impl.BatchServiceImpl;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EntityServiceImpl implements EntityService {

    @Lazy
    private final CommonService commonService;
    private final EntityRepository entityRepository;
    private final AttributeEntityService attributeEntityService;
    private final BatchService batchService;
    private final NoteService noteService;
    private final SysUserService sysUserService;
    private final NoteTaskService noteTaskService;
    private final DataExportService dataExportService;

    @Override
    public EntityAnnoJsonDTO findEntityByArticleId(String articleId, String projectCode) {
        Project project = commonService.findProjectByCode(projectCode);
        Note note = noteService.getOne(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .eq(Note::getArticleId, articleId));
        if (note == null) {
            throw new RRException("该文章不存在");
        }
        NoteTask noteTask = noteTaskService.getOne(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster, MasterEnum.master.getValue()).eq(NoteTask::getNoteId, note.getNoteId()));
        if (noteTask == null) {
            throw new RRException("该标注任务不存在");
        }
        EntityAnnoJsonDTO data = dataExportService.getEntityJson(noteTask);
        return data;
    }

    @Override
    public List<EntityAnnoJsonDTO> findEntityByBatchId(Long batchId, String projectCode) {
        Project project = commonService.findProjectByCode(projectCode);
        Batch batch = batchService.getOne(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, project.getProjectId())
                .eq(Batch::getBatchId, batchId));
        if (batch == null) {
            throw new RRException("该批次不存在");
        }
        List<NoteTask> noteTaskList = noteTaskService.list(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster,
                MasterEnum.master.getValue()).eq(NoteTask::getBatchId, batch.getBatchId()));
        if (CollUtil.isEmpty(noteTaskList)) {
            throw new RRException("暂无数据");
        }
        return dataExportService.getEntityJsonList(noteTaskList);
    }

    @Override
    public List<EntityAnnoJsonDTO> findAllEntity(String projectCode) {
        Project project = commonService.findProjectByCode(projectCode);
        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery().eq(Note::getProjectId, project.getProjectId()));
        if (CollUtil.isEmpty(notes)) {
            return null;
        }
        List<Long> noteIds = notes.stream().map(Note::getNoteId).collect(Collectors.toList());
        List<NoteTask> noteTaskList = noteTaskService.list(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster,
                MasterEnum.master.getValue()).in(NoteTask::getNoteId, noteIds));
        if (CollUtil.isEmpty(noteTaskList)) {
            throw new RRException("暂无数据");
        }
        return dataExportService.getEntityJsonList(noteTaskList);
    }

    @Override
    public EntityAnnoJsonDTO.Info findById(String id) {
        Optional<Entity> optionalEntity = entityRepository.findById(id);
        if (!optionalEntity.isPresent()) {
            throw new RRException("未查询到该实体标注信息");
        }
        return dataExportService.getEntityJsonInfo(optionalEntity.get());
    }

    @Override
    public boolean existsById(String id) {
        return entityRepository.existsById(id);
    }

    @Override
    public PageIdsListVO queryPage(EntitySearchDTO dto) {
        final PageIdsListVO pageIdsListVO = new PageIdsListVO();

        // 查询上下页切换的pageIds
        // 实体标注列表页面打开的标注页不要上/下页翻页操作
        // pageIdsListVO.setPageIdsVOList(allEntityPageIds(dto));

        // 查询分页数据
        final Page<Entity> page = entityRepository.queryPage(dto);

        // 获取当前页数据
        final List<Entity> content = page.getContent();
        // 找出所有的entity的id
//        final List<String> ids = content.stream().map(Entity::getId).collect(Collectors.toList());
        // 统计entity_id在attributeEntity出现的次数
//        final Map<String, Integer> countMap = attributeEntityService.countMapByEntityIds(ids);
        final List<EntityVO> entityVOList = content.stream().map(x -> {
            EntityVO vo = new EntityVO();
            final String id = x.getId();
            vo.setId(id);
            vo.setArticleId(x.getArticleId());
            vo.setBatchId(x.getBatchId());
            vo.setNoteId(x.getNoteId());
            vo.setUserId(x.getAnnotatorId() != null ? x.getAnnotatorId() : x.getAuditorId());
            vo.setCreateTime(x.getCreateTime());
            vo.setContent(x.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")));
            vo.setAttrCount(0);
            /*
            // 使用异步统计，加快列表页渲染速度
            if (countMap.containsKey(id)) {
                vo.setAttrCount(countMap.get(id));
            }*/

            /*final Integer attrCount = vo.getAttrCount();
            if (attrCount != null && attrCount > 0) {
                final Map<String, Object> attrMap = attributeEntityService.findAttrListByEntityId(id);
                final List<Attributes> attributes = (List<Attributes>) attrMap.get(AttributeEntityServiceImpl.ATTR_LIST_KEY);
                final Map<Long, String> attrLabelMap = (Map<Long, String>) attrMap.get(AttributeEntityServiceImpl.ATTR_LABEL_MAP_KEY);
                if (CollUtil.isNotEmpty(attributes) && CollUtil.isNotEmpty(attrLabelMap)) {
                    final List<String> attrList = new ArrayList<>();
                    for (Attributes attribute : attributes) {
                        final String name = StrUtil.trimToEmpty(attrLabelMap.get(attribute.getAttrLabelId()));
                        final String attrContent = StrUtil.trimToEmpty(attribute.getContent());
                        if (!name.isEmpty()) {
                            attrList.add(StrUtil.format("【{}】 {}", name, attrContent));
                        }
                    }
                    vo.setAttrContent(StrUtil.join("；", attrList));
                }
            }*/

            return vo;
        }).collect(Collectors.toList());

        pageIdsListVO.setListData(new PageImpl<>(entityVOList, page.getPageable(), page.getTotalElements()));
        return pageIdsListVO;
    }

    private List<PageIdsVO> allEntityPageIds(EntitySearchDTO articleListDTO) {
        final EntitySearchDTO dto = BatchServiceImpl.initBaseDTO4Ids(articleListDTO, EntitySearchDTO.class);
        // 查询分页数据
        Page<Entity> page = entityRepository.queryPage(dto);
        // 获取当前页数据
        List<Entity> content = page.getContent();
        final Set<PageIdsVO> set = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(content)) {
            for (Entity entity : content) {
                final PageIdsVO pageIdsVO = new PageIdsVO();
                pageIdsVO.setBatchId(entity.getBatchId());
                pageIdsVO.setNoteId(entity.getNoteId());
                set.add(pageIdsVO);
            }
        }

        return new ArrayList<>(set);
    }

    @Override
    public Map<Long, String> getUsersByLabelId(Long labelId) {
        List<Long> userIds = entityRepository.getUserIdsByLabelId(labelId);
        Map<Long, String> userMap = new HashMap<>(16);
        List<SysUserEntity> users = sysUserService.findAllByIdIn(userIds);
        for (SysUserEntity user : users) {
            userMap.put(user.getUserId(), user.getUsername());
        }
        return userMap;
    }

    @Override
    public List<Entity> findAllByIdIn(Collection<String> ids) {
        return entityRepository.findAllByIdInAndDeleted(ids, false);
    }

    @Override
    public Long countByIdIn(Collection<String> ids) {
        return entityRepository.countByIdInAndDeleted(ids, false);
    }
}
