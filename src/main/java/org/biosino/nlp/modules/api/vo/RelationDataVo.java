package org.biosino.nlp.modules.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-1-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationDataVo {

  private String id;
  private Integer index;
  private String articleId;
  private Integer patternId;
  private String md5;
  private List<Relationship.RelationItem> items;
  private Boolean correct;
}
