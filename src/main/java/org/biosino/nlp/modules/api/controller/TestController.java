package org.biosino.nlp.modules.api.controller;

import cn.hutool.core.thread.ThreadUtil;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.TestService;
import org.biosino.nlp.modules.note.service.AttributeEntityService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.impl.NoteTaskServiceImpl;
import org.biosino.nlp.modules.project.service.StatisticsService;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 前端标注模板的测试API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TestService testService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private NoteTaskServiceImpl noteTaskService;
    @Autowired
    private NoteService noteService;
    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private AttributeEntityService attributeEntityService;

    @GetMapping("/checkVersion")
    public R checkVersion() {
        // 暂时只是返回测试数据
        Map<String, Object> map = new HashMap<>(2);
        map.put("version", "2.0.0");
        return R.ok(map);
    }

    @GetMapping("/resetAdminPassWord")
    public R resetAdminPassWord(String token) {
        if (!"Lfgzs2023".equals(token)) {
            return R.error("token验证失败");
        }
        sysUserService.resetAdminPassWord();
        return R.success("系统管理员密码重置成功");
    }

    /*@GetMapping("/fixEntityData")
    public R fixEntityData(String token, String projectCode) {
        if (!"Lfgzs2023".equals(token)) {
            return R.error("token验证失败");
        }
        testService.fixEntityData(projectCode);
        return R.success("矫正项目下的文章实体完成");
    }

    @GetMapping("/fixAutoReviewed")
    public R fixAutoReviewed(String token) {
        if (!"Lfgzs2023".equals(token)) {
            return R.error("token验证失败");
        }
        testService.fixAutoReviewed();
        return R.success("矫正项目下的文章自动审核");
    }*/

    // /bnlp-api-v3/test/fixSubmitData?token=Lfgzs2024
    @GetMapping("/fixSubmitData")
    public R fixAutoReviewed(String token) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            noteTaskService.fixSubmitData();
            System.out.println("矫正已审核的统计数据完成");
        });
        return R.success("开始矫正已审核的统计数据");
    }

    @GetMapping("/fixAutoAuditData")
    public R fixAutoAuditData(String token) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            noteTaskService.fixAutoAuditData();
            System.out.println("矫正自动审核的数据完成");
        });
        return R.success("矫正自动审核的数据");
    }

    @GetMapping("/caseProjectToSingle")
    public R resetAdminPassWord(String token, String projectCode, Long masterId) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        testService.caseToSingle(projectCode, masterId);
        return R.success("caseProjectToSingle成功");
    }

    /**
     * 恢复审核员误删除的标注
     */
    @GetMapping("/restoreDeletedEntry")
    public R restoreDeletedEntry(String token) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            noteService.restoreDeletedEntry();
            System.out.println("恢复审核员误删除的标注完成");
        });
        return R.success("开始恢复审核员误删除的标注");
    }

    /**
     * 执行统计定时任务
     */
    @GetMapping("/annotatedStatisticsJob")
    public R statisticsService(String token) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            statisticsService.annotatedStatisticsJob();
            System.out.println("执行统计定时任务完成");
        });
        return R.success("执行统计定时任务");
    }

    /**
     * 删除标注不存在的属性关系
     */
    @GetMapping("/deleteNotExistAttributesRelation")
    public R deleteNotExistAttributesRelation(String token) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            attributeEntityService.deleteNotExistAttributesRelation();
            System.out.println("删除标注不存在的属性关系完成");
        });
        return R.success("开始删除标注不存在的属性关系");
    }

    /**
     * 修复entity中note id不一致
     */
    @GetMapping("/fixNoteId")
    public R fixNoteId(String token, @RequestParam(required = false) Long projectId) {
        if (!"Lfgzs2024".equals(token)) {
            return R.error("token验证失败");
        }
        ThreadUtil.execAsync(() -> {
            testService.fixNoteId(projectId);
            System.out.println("修复entity中note id不一致完成");
        });
        return R.success("开始修复entity中note id不一致");
    }
}
