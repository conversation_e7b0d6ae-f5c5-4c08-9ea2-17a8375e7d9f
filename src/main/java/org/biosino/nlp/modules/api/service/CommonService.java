package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.project.entity.Project;
import org.springframework.stereotype.Service;

/**
 * API 公共服务
 */
@Service
public interface CommonService {

    /**
     * 校验Token合法性
     *
     * @param token 项目的唯一token标识码
     */
    void checkToken(String token);

    /**
     * 根据唯一识别码查询项目
     *
     * @param projectCode 项目的唯一token标识码
     */
    Project findProjectByCode(String projectCode);

}
