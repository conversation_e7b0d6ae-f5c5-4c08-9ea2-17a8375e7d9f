package org.biosino.nlp.modules.api.controller.bnlp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.common.enums.ExportFileTypeEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.DownloadUtil;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.ExportLog;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.service.ExportLogService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@Controller
@RequestMapping("/api/download")
public class DownloadController {
    @Autowired
    private ExportLogService exportLogService;

    @Autowired
    private ProjectService projectService;
    @Autowired
    private BatchService batchService;

    @RequestMapping("/export")
    public void downloadAnnoData(String id, HttpServletResponse response) throws IOException {
        ExportLog exportLog = exportLogService.getById(id);
        if (exportLog != null) {
            String path = exportLog.getOutputFilePath();

            String downloadName;
            Project project = projectService.getById(exportLog.getProjectId());
            String projectName = project.getName();
            if (exportLog.getBatchId() != null) {
                Batch batch = batchService.getById(exportLog.getBatchId());
                String batchName = batch.getName();
                String fileType = exportLog.getType().equals(ExportFileTypeEnum.json.getCode()) ? "json数据" : "excel数据";
                downloadName = StrUtil.format("{}_{}_{}_{}.zip", projectName, batchName, fileType, DateUtil.format(exportLog.getCreateTime(), "yyyy-MM-dd-HH-mm-ss"));
            } else {
                String fileType = exportLog.getType().equals(ExportFileTypeEnum.json.getCode()) ? "json数据" : "excel数据";
                downloadName = StrUtil.format("{}_{}_{}_{}.zip", projectName, "所有批次", fileType, DateUtil.format(exportLog.getCreateTime(), "yyyy-MM-dd-HH-mm-ss"));
            }
            DownloadUtil.download(FileUtil.file(path + ".zip"), downloadName, response);
        } else {
            throw new RRException("没有导出记录");
        }
    }
}
