package org.biosino.nlp.modules.api.controller.bnlp;

import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.LabelService;
import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标签 数据请求API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/entity")
public class LabelController {

    @Autowired
    private LabelService labelService;

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 标签信息
     */
    @GetMapping("/findLabelById")
    public R findLabelById(Long id) {
        EntityLabelVo vo = labelService.findLabelById(id);
        return R.success(vo);
    }

}
