package org.biosino.nlp.modules.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.service.ArticleService;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.service.NoteService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl implements ArticleService {
    private static final Map<String, Object> TASK_RUN_MAP = new ConcurrentHashMap<>();
    private static final String ADD_WORD_COUNT = "addWordCountTask";

    private final MongoTemplate mongoTemplate;
    private final NoteDao noteDao;
    private final NoteService noteService;

    /**
     * 根据文章ID查询指定文章
     *
     * @param articleId 文章ID
     * @return 文章
     */
    @Override
    public Document findArticleById(String articleId, Integer version) {
        Query query;
        if (version != null) {
            query = Query.query(Criteria.where("article_id").is(articleId).and("version").is(version)).limit(1);
        } else {
            query = Query.query(Criteria.where("article_id").is(articleId))
                    .with(Sort.by(Sort.Direction.DESC, "version")).limit(1);
        }
        return Optional.ofNullable(mongoTemplate.findOne(query, Document.class))
                .orElseThrow(() -> new RRException("未查询到该篇文献"));
    }

    @Override
    public void unHtmlEscape() {
        long total = mongoTemplate.count(new Query(), Document.class);
        // 单次读取数量
        int size = 100;
        // 读取次数
        long page = total / size;
        int skip;
        for (int i = 0; i <= page; i++) {
            skip = i * size;
            List<Document> infos = mongoTemplate.find(new Query().limit(size).skip(skip), Document.class);
            for (Document document : infos) {
                String s = JSON.toJSONString(document);
                String unescape = HtmlUtil.unescape(s);
                Document parse = JSON.parseObject(unescape, Document.class);
                mongoTemplate.save(parse);
            }
            log.info("数据清洗进度: {}/{}", skip + infos.size(), total);
        }
    }

    @Override
    public void unHtmlEscapeByPath() {
        // 临时代码，写死路径即可
        String path = "D:\\test";

        File file = new File(path + File.separator + "ids.txt");
        if (!file.exists()) {
            log.error("未找到文件：" + file.getAbsolutePath());
        }

        List<String> list = FileUtil.readLines(file, StandardCharsets.UTF_8);
        for (String id : list) {
            Document document = mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), Document.class);
            String s = JSON.toJSONString(document);
            String unescape = HtmlUtil.unescape(s);
            Document parse = JSON.parseObject(unescape, Document.class);
            mongoTemplate.save(parse);
            log.info("{}处理完毕", id);
        }
        log.info("全部清洗完成");
    }

    @Override
    public void checkWordCountTask() {
        Long val = (Long) TASK_RUN_MAP.get(ADD_WORD_COUNT);
        if (val != null) {
            throw new RRException("任务正在运行中");
        }
    }

    @Override
    public void addWordCount() {
        checkWordCountTask();
        try {
            final long start = System.currentTimeMillis();
            TASK_RUN_MAP.put(ADD_WORD_COUNT, start);
            log.warn("开始补充字数任务");
//            String id = "";
//            Document document = mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), Document.class);

            final int size = 3000;
            final Map<String, Integer> docWordsCount = new HashMap<>();
            long lastNodeId = -1;
            for (int i = 0; i < Integer.MAX_VALUE; i++) {
                final LambdaQueryWrapper<Note> wrapper = Wrappers.lambdaQuery(Note.class).eq(Note::getWordsCount, 0).gt(Note::getNoteId, lastNodeId);
                wrapper.orderByAsc(Note::getNoteId);

                final Page<Note> page = new Page<>(1, size, false);
                final Page<Note> notePage = noteDao.selectPage(page, wrapper);
                final List<Note> records = notePage.getRecords();
                final int pageCount = CollUtil.size(records);
                if (pageCount == 0) {
                    break;
                }
                lastNodeId = records.get(pageCount - 1).getNoteId();
                if (lastNodeId <= 0) {
                    break;
                }

                final Set<String> docIds = records.stream().map(x -> StrUtil.trimToNull(x.getDocumentId()))
                        .filter(x -> x != null && !docWordsCount.containsKey(x)).collect(Collectors.toSet());

                final Map<String, Document> documentMap = new HashMap<>();
                if (CollUtil.isNotEmpty(docIds)) {
                    final List<Document> documentList = mongoTemplate.find(Query.query(Criteria.where("_id").in(docIds)), Document.class);
                    if (CollUtil.isNotEmpty(documentList)) {
                        for (Document document : documentList) {
                            documentMap.put(document.getId(), document);
                        }
                    }
                }

                final List<Note> updateData = new ArrayList<>();
                for (Note note : records) {
                    final String documentId = note.getDocumentId();
                    if (StrUtil.isBlank(documentId)) {
                        continue;
                    }
                    Integer count = docWordsCount.get(documentId);
                    if (count == null) {
                        final Document document = documentMap.get(documentId);
                        count = countWordsNum(document);
                        docWordsCount.put(documentId, count);
                    }
                    note.setWordsCount(count);
                    updateData.add(note);
                }

                updateNotes(updateData);
            }

            log.warn("补充字数任务完毕，耗时：{}", DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND));
        } finally {
            TASK_RUN_MAP.remove(ADD_WORD_COUNT);
        }
    }

    public static void statWordsCount(Note note, Document document) {
        if (note != null) {
            note.setWordsCount(countWordsNum(document));
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateNotes(Collection<Note> notes) {
        final int size = CollUtil.size(notes);
        if (size > 0) {
            noteService.updateBatchById(notes, size);
        }
    }

    public static int countWordsNum(Document document) {
        if (document == null) {
            return 0;
        }

        final JSONObject jsonObject = (JSONObject) JSON.toJSON(document);
        return countTotalWords(jsonObject);
    }


    /**
     * 计算文章字数
     *
     * @param val
     * @return
     */
    private static int countTotalWords(final Object val) {
        int count = 0;
        if (val == null) {
            return count;
        }

        if (val instanceof JSONObject) {
            final JSONObject jsonObj = (JSONObject) val;
            final String cls = jsonObj.getString("cls");
            if (StrUtil.isBlank(cls) || "Title".equalsIgnoreCase(cls)) {
                return count;
            }

            final Set<String> keySet = jsonObj.keySet();
            for (final String key : keySet) {
                final Object value = jsonObj.get(key);
                if (value == null) {
                    continue;
                }

                if ("content".equals(key)) {
                    // 超过两个空格(英文存在空格)的，全部替换为一个空格
                    final String content = ((String) value).replaceAll("\\s{2,}", StrUtil.SPACE);
                    count += content.length();
                } else if (value instanceof JSONObject) {
                    count += countTotalWords(value);
                } else if (value instanceof JSONArray) {
                    final JSONArray jsonArray = (JSONArray) value;
                    for (Object o : jsonArray) {
                        count += countTotalWords(o);
                    }
                }
            }
        } else if (val instanceof JSONArray) {
            final JSONArray jsonArray = (JSONArray) val;
            for (Object o : jsonArray) {
                count += countTotalWords(o);
            }
        }

        return count;
    }

}
