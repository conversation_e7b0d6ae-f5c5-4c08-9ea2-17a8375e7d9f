package org.biosino.nlp.modules.api.controller.bnlp;

import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.RelationService;
import org.biosino.nlp.modules.api.vo.RelationDataVo;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.project.dto.RelationAnnoJsonDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 关系标注 数据请求API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/relation")
public class RelationApiController {

  @Autowired
  private RelationService relationService;

  @Autowired
  private RelationLabelService relationLabelService;

  /**
   * 根据ID获取指定项目某个批次文献的关系标注数据
   *
   * @param articleId 文章ID
   * @param projectCode 项目标识码
   * @return 关系标注数据
   */
  @GetMapping("/findByArticleId")
  public R findAttrByArticleId(String articleId, String projectCode) {
    List<RelationAnnoJsonDTO> data = relationService.findByArticleId(articleId, projectCode);
    return R.success(data);
  }

  /**
   * 根据文章ID获取指定项目某个批次文献的属性标注数据
   *
   * @param articleId 文章ID
   * @param pattern 模板号
   * @param projectCode 项目标识码
   * @return 关系标注图形数据
   */
  @GetMapping("/findMapDataByArticleId")
  public R findMapDataByArticleId(String articleId, Integer pattern, String projectCode) {
    Graph vo = relationService.findMapDataByArticleId(articleId, pattern, projectCode);
    return R.success(vo);
  }

  /**
   * 查询项目某个批次的所有关系标注数据
   *
   * @param batchId 批次ID
   * @param projectCode 项目标识码
   * @return 关系标注数据
   */
  @GetMapping("/findByBatchId")
  public R findAttrByBatchId(Long batchId, String projectCode) {
    List<RelationAnnoJsonDTO> data = relationService.findByBatchId(batchId, projectCode);
    return R.success(data);
  }


  /**
   * 查询项目某个批次的中已审核的关系标注数据
   *
   * @param batchId 批次ID
   * @param projectCode 项目标识码
   * @return 关系标注数据
   */
  @GetMapping("/findApprovedByBatchId")
  public R findApprovedByBatchId(String batchId, String projectCode) {
    List<RelationDataVo> vo = relationService.findApprovedByBatchId(batchId, projectCode);
    return R.success(vo);
  }

  /**
   * 查询项目所有关系标注数据
   *
   * @param projectCode 项目标识码
   * @return 关系标注数据
   */
  @GetMapping("/findAll")
  public R findAllAttribute(String projectCode) {
    List<RelationAnnoJsonDTO> data = relationService.findAll(projectCode);
    return R.success(data);
  }

  /**
   * 查询关系标签
   *
   * @param id 标签ID
   * @return 关系标注数据
   */
  @GetMapping("/findLabelById")
  public R findLabelById(Integer id) {
    RelationLabel relationLabel = relationLabelService.findById(id);
    return R.success(relationLabel);
  }
}
