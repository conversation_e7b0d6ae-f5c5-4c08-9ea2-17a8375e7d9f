package org.biosino.nlp.modules.api.controller;

import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.ApiService;
import org.biosino.nlp.modules.api.vo.BatchVo;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.project.entity.mongo.PreAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 公共数据请求API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class ApiController {

    @Autowired
    private ApiService apiService;

    /**
     * 根据文章ID查询指定文章，没有版本返回最新的
     *
     * @param articleId 文章ID
     * @param version   文章版本
     * @return 文章
     */
    @GetMapping("/findArticleById")
    public R findArticleById(String articleId, @RequestParam(required = false) Integer version) {
        // 根据ID查询文章
        Document document = apiService.findArticleById(articleId, version);
        return R.success(document);
    }

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 标签信息
     */
    @GetMapping("/findLabelById")
    public R findLabelById(Long id) {
        EntityLabelVo vo = apiService.findLabelById(id);
        return R.success(vo);
    }

    /**
     * 根据实体标签代码，查询实体标签
     *
     * @param code        标签code
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findLabelByCode")
    public R findLabelByCode(Long code, String projectCode) {
        EntityLabelVo vo = apiService.findLabelByCode(code, projectCode);
        return R.success(vo);
    }

    /**
     * 根据projectCode查询该项目下所有批次的简要信息
     *
     * @param projectCode 项目的唯一请求标识符
     * @return 标签信息
     */
    @GetMapping("/findAllBatchInfo")
    public R findAllBatchInfo(String projectCode) {
        List<BatchVo> vo = apiService.findAllBatchInfo(projectCode);
        return R.success(vo);
    }

    /**
     * 根据ID获取指定项目某个批次文献的实体标注数据
     *
     * @param articleId   文章ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findEntityByArticleId")
    public R findEntityByArticleId(String articleId, String projectCode) {
        List<EntityDataVo> vo = apiService.findEntityByArticleId(articleId, projectCode);
        return R.success(vo);
    }

    /**
     * 查询项目某个批次的所有实体标注数据
     *
     * @param batchId     批次ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findEntityByBatchId")
    public R findEntityByBatchId(String batchId, String projectCode) {
        List<EntityDataVo> vo = apiService.findEntityByBatchId(batchId, projectCode);
        return R.success(vo);
    }

    /**
     * 查询项目所有实体标注数据
     *
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findAllEntity")
    public R findAllEntity(String projectCode) {
        List<EntityDataVo> vo = apiService.findAllEntity(projectCode);
        return R.success(vo);
    }


    /**
     * 查询当前文章有哪些预标注来源
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注来源
     */
    @GetMapping("/getSource/{articleId}/{version}")
    public R getSource(@PathVariable("articleId") String articleId, @PathVariable("version") Integer version) {
        // 查询当前文章有哪些预标注数据
        List<SourceVO> sourceMap = apiService.getSource(articleId, version);
        return R.success(sourceMap);
    }

    /**
     * 查询当前文章机器预标注数据
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注数据
     */
    @GetMapping("/getPreAnnotation/{articleId}/{version}/{source}")
    public R getPreAnnotation(@PathVariable("articleId") String articleId,
                              @PathVariable("version") Integer version,
                              @PathVariable("source") String source) {
        // 查询当前文章有哪些预标注数据
        List<PreAnnotation> preAnnotations = apiService.getPreAnnotation(articleId, version, source);
        return R.success(preAnnotations);
    }

    /**
     * 查询指定预标注数据
     *
     * @param articleId 文献id
     * @param version   版本
     * @param source    预标注来源
     * @param textId    textId
     * @param start     开始位置
     * @param end       介绍位置
     * @return 预标注数据
     */
    @GetMapping("/getPreAnnotationLabel/{articleId}/{version}/{source}/{textId}/{start}/{end}")
    public R getPreAnnotationLabel(@PathVariable("articleId") String articleId,
                                   @PathVariable("version") Integer version,
                                   @PathVariable("source") String source,
                                   @PathVariable("textId") String textId,
                                   @PathVariable("start") Integer start,
                                   @PathVariable("end") Integer end) {
        // 查询当前文章预标注数据
        String preAnnotations = apiService.getPreAnnotation(articleId, version, source, textId, start, end);
        return R.success(preAnnotations);
    }

    /**
     * 更新 pdf文档 接口
     *
     * @param file     pdf文件
     * @param username 验证账号
     * @param password 验证密码
     * @return 是否覆盖成功
     * @throws IOException
     */
    @RequestMapping("/updatePdf")
    public R updatePdf(@RequestParam("file") MultipartFile file, @RequestParam("username") String username, @RequestParam("password") String password) throws IOException {
        if ("admin".equals(username) && "admin".equals(password)) {
            Boolean flag = apiService.updatePdf(file);
            return flag ? R.ok() : R.error("请检查上传文件");
        } else {
            return R.error("账号密码错误");
        }
    }

    /**
     * 根据labelCode查询Project的情况
     */
    @RequestMapping("/getLabelUseInfo")
    public R getLabelUseInfo(String labelCode) {
        apiService.getLabelUseInfo(labelCode);
        return R.ok();
    }
}
