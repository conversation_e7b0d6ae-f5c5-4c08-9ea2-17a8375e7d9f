package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.note.dto.AttributeEntityDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AttributeEntityMapper {
    AttributeEntityMapper INSTANCE = Mappers.getMapper(AttributeEntityMapper.class);

    Attributes dtoToAttributes(AttributeEntityDTO dto);

    Attributes copyBean(Attributes attribute);
}
