package org.biosino.nlp.modules.api.controller.bnlp;

import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.EntityService;
import org.biosino.nlp.modules.project.dto.EntityAnnoJsonDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 实体标注 数据API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/entity")
public class EntityApiController {

    @Autowired
    private EntityService entityService;

    /**
     * 获取某篇文献的实体标注结果
     *
     * @param articleId   文章ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findByArticleId")
    public R findEntityByArticleId(String articleId, String projectCode) {
        EntityAnnoJsonDTO data = entityService.findEntityByArticleId(articleId, projectCode);
        return R.success(data);
    }

    /**
     * 查询项目某个批次的所有实体标注数据
     *
     * @param batchId     批次ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findByBatchId")
    public R findEntityByBatchId(Long batchId, String projectCode) {
        List<EntityAnnoJsonDTO> data = entityService.findEntityByBatchId(batchId, projectCode);
        return R.success(data);
    }

    /**
     * 查询项目所有实体标注数据
     *
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    @GetMapping("/findAll")
    public R findAllEntity(String projectCode) {
        List<EntityAnnoJsonDTO> data = entityService.findAllEntity(projectCode);
        return R.success(data);
    }


    /**
     * 根据实体标注ID实体标注数据
     *
     * @param id 实体标签的ID
     * @return 实体标签
     */
    @GetMapping("/findById")
    public R findById(String id) {
        EntityAnnoJsonDTO.Info data = entityService.findById(id);
        return R.success(data);
    }
}
