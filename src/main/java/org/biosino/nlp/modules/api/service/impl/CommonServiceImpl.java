package org.biosino.nlp.modules.api.service.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.service.CommonService;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    @Lazy
    private final ProjectService projectService;

    /**
     * 校验Token合法性
     *
     * @param token 项目的唯一token标识码
     */
    @Override
    public void checkToken(String token) {
        Project project = projectService.findByToken(token);
        if (project == null) {
            throw new RRException("Token认证失败");
        }
    }

    /**
     * 根据唯一识别码查询项目
     *
     * @param projectCode 项目的唯一token标识码
     */
    @Override
    public Project findProjectByCode(String projectCode) {
        Project project = projectService.findByToken(projectCode);
        if (project == null) {
            throw new RRException("该项目不存在");
        }
        return project;
    }
}
