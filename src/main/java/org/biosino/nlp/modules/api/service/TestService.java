package org.biosino.nlp.modules.api.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

@Slf4j
@Service
public class TestService {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private NoteService noteService;
    @Autowired
    private NoteTaskService noteTaskService;
    @Autowired
    private NoteDao noteDao;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private MongoOperations mongoOperations;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private RelationshipRepository relationshipRepository;

    public JSONObject readJson(String fileName) throws IOException {
        Resource resource = new ClassPathResource("file/" + fileName);
        InputStream is = resource.getInputStream();
        InputStreamReader isr = new InputStreamReader(is);
        BufferedReader br = new BufferedReader(isr);
        StringBuilder data = new StringBuilder();
        String lineData;
        while ((lineData = br.readLine()) != null) {
            data.append(lineData);
        }
        br.close();
        isr.close();
        is.close();
        return JSONObject.parseObject(data.toString());
    }

    public void fixEntityData(String projectCode) {
        log.warn("开始矫正项目：{}数据", projectCode);
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery().eq(Project::getCode, projectCode));

        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .ne(Note::getStep, NoteStepEnum.unmarked.getCode()));

        if (CollUtil.isEmpty(notes)) {
            return;
        }
        for (Note note : notes) {
            String documentId = note.getDocumentId();

            // 查找原文
            Document document = mongoOperations.findOne(Query.query(Criteria.where("_id").is(documentId)), Document.class);
            if (document == null) {
                log.error("矫正失败，原始文档不能为空");
                return;
            }
            Document.ItemsDTO itemsDTO = document.getBody().get(0).getItems().get(0).getItems().get(0);
            String itemId = itemsDTO.getId();
            String content = itemsDTO.getContent();
            if (!content.contains("  ")) {
                continue;
            }
            String cleanText = "";
            String dirtyText = "";
            List<int[]> whiteSpans = new ArrayList<>();
            boolean isText = true;
            String[] split = content.split("(?<= )|(?= )");
            List<String> splitArry = new ArrayList<>();
            for (String s : split) {
                if (!" ".equals(s)) {
                    splitArry.add(s);
                } else {
                    if (splitArry.size() == 0) {
                        splitArry.add(s);
                    } else {
                        String s1 = splitArry.get(splitArry.size() - 1);
                        if ("".equals(s1.trim())) {
                            s1 += s;
                            splitArry.set(splitArry.size() - 1, s1);
                        } else {
                            splitArry.add(s);
                        }
                    }
                }
            }
            for (String seg : splitArry) {
                if (isText) {
                    cleanText += seg;
                    dirtyText += seg;
                } else {
                    int dirtyStart = dirtyText.length();
                    dirtyText += seg;
                    int cleanStart = cleanText.length();
                    cleanText += " ";
                    whiteSpans.add(new int[]{dirtyStart, cleanStart, seg.length()});
                }
                isText = !isText;
            }
            assert dirtyText.equals(content);
            // 干净文本
//            System.out.println(cleanText);

            // 矫正实体
            List<Entity> entities = mongoOperations.find(Query.query(Criteria.where("note_id").is(note.getNoteId())), Entity.class);
            //TODO sw test
            /*for (Entity entity : entities) {
                int start = entity.getStart();
                int end = entity.getEnd();
                int whiteSpansBeforeStart = -1;
                int whiteSpansBeforeEnd = -1;
                for (int[] span : whiteSpans) {
                    int dirtyStart = span[0];
                    int whiteLen = span[2];
                    if (dirtyStart < start) {
                        whiteSpansBeforeStart++;
                        whiteSpansBeforeEnd++;
                        continue;
                    }
                    if (dirtyStart + whiteLen < end) {
                        whiteSpansBeforeEnd++;
                    } else {
                        break;
                    }
                }
                if (whiteSpansBeforeEnd > -1) {
                    end -= whiteSpans.subList(0, whiteSpansBeforeEnd + 1).stream().mapToInt(x -> x[2]).sum() - whiteSpansBeforeEnd - 1;
                }
                String text = entity.getText();
                start = end - text.length();
                String eid = entity.getTextId();
                assert eid.equals(itemId);
                assert text.equals(cleanText.substring(start, end));

                // 删除旧实体
                mongoOperations.remove(entity);

                // 添加新实体
                entity.setText(text);
                entity.setStart(start);
                entity.setEnd(end);
                String id = entity.getNoteId() + "_" + entity.getTextId() + "_" + entity.getStart() + "_" + entity.getEnd();
                entity.setId(id);
                mongoOperations.save(entity);
//                System.out.println(start + " " + end);

                // 更新文档
                itemsDTO.setContent(cleanText);
                mongoOperations.save(document);
                log.warn("矫正document数据:{}", documentId);
            }*/
        }
        log.warn("矫正项目：{}数据完成", projectCode);
    }

    public void fixAutoReviewed() {
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getAuditor, 2)
                .orderByAsc(NoteTask::getTaskId).orderByDesc(NoteTask::getMaster);
        List<NoteTask> tasks = noteTaskService.list(taskWrapper);
        if (CollUtil.isEmpty(tasks)) {
            return;
        }
        Set<Long> set = new HashSet<>();
        for (NoteTask task : tasks) {
            if (set.contains(task.getNoteId())) {
                continue;
            }
            task.setMaster(1);
            set.add(task.getNoteId());
            noteTaskService.updateById(task);
        }
    }

    public void caseToSingle(String projectCode, Long masterId) {
        Project project = projectService.getOne(Wrappers.<Project>lambdaQuery().eq(Project::getCode, projectCode));

        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .gt(Note::getPullCount, 0)
        );

        for (Note note : notes) {
            LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId());
            List<NoteTask> tasks = noteTaskService.list(taskWrapper);

            if (CollUtil.isEmpty(tasks)) {
                continue;
            }
            Integer step = tasks.get(0).getStep();

            if (tasks.size() == 1) {
                // 如果是已经是标注完的，把note也改为待审核
                if (step == 2 && note.getStep() == 1) {
                    noteDao.update(note, Wrappers.<Note>lambdaUpdate()
                            .eq(Note::getNoteId, note.getNoteId())
                            .set(Note::getStep, 2));
                }
                continue;
            }

            for (NoteTask task : tasks) {
                Long taskId = task.getTaskId();
                // 保留的标注人记录，不删除
                if (task.getAnnotator().equals(masterId)) {
                    step = task.getStep();

                    // 已经被自动审核的数据，删除审核信息
                    if (step == 4 && task.getAuditor() == 2L) {
                        step = NoteStepEnum.marked.getCode();
                        noteTaskDao.update(task, Wrappers.<NoteTask>lambdaUpdate()
                                .eq(NoteTask::getTaskId, taskId)
                                .set(NoteTask::getStep, step)
                                .set(NoteTask::getMaster, 0)
                                .set(NoteTask::getAuditEndTime, null)
                                .set(NoteTask::getAuditor, null)
                                .set(NoteTask::getCorrectRate, null)
                                .set(NoteTask::getRepulseMsg, null)
                                .set(NoteTask::getAuditStartTime, null));
                    }
                    continue;
                }
                // 删除操作的数据
                entityRepository.deleteByTaskId(taskId);
                attributesRepository.deleteByTaskId(taskId);
                relationshipRepository.deleteByTaskId(taskId);
                noteTaskDao.reallyDeleteByTaskId(taskId);
            }

            noteDao.update(note, Wrappers.<Note>lambdaUpdate()
                    .eq(Note::getNoteId, note.getNoteId())
                    .set(Note::getStep, step)
                    .set(Note::getPullCount, note.getPullCount() - tasks.size() + 1));
        }

        projectDao.update(project, Wrappers.<Project>lambdaUpdate()
                .eq(Project::getProjectId, project.getProjectId())
                .set(Project::getMarkRounds, 1));
    }

    public void fixNoteId(Long projectId) {

        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getProjectId, projectId);

        List<NoteTask> noteTasks = noteTaskDao.selectList(taskWrapper);

        if (CollUtil.isEmpty(noteTasks)) {
            return;
        }
        for (NoteTask noteTask : noteTasks) {

            List<Entity> entities = mongoOperations.find(Query.query(Criteria.where("task_id").is(noteTask.getTaskId())), Entity.class);

            if (CollUtil.isEmpty(entities)) {
                continue;
            }

            for (Entity entity : entities) {
                if (!Objects.equals(entity.getNoteId(), noteTask.getNoteId()) ||
                        !Objects.equals(entity.getBatchId(), noteTask.getBatchId()) ||
                        !Objects.equals(entity.getProjectId(), noteTask.getProjectId())) {

                    entity.setNoteId(noteTask.getNoteId());
                    entity.setBatchId(noteTask.getBatchId());
                    entity.setProjectId(noteTask.getProjectId());
                    mongoOperations.save(entity);
                }
            }
        }

    }
}
