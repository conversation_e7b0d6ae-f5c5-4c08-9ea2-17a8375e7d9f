package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.springframework.stereotype.Service;

@Service
public interface LabelService {

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 实体标签
     */
    EntityLabelVo findLabelById(Long id);

//    /**
//     * 根据实体标签代码，查询实体标签
//     *
//     * @param code        标签code
//     * @param projectCode 项目标识码
//     * @return 实体标签
//     */
//    EntityLabelVo findLabelByCode(String code, String projectCode);

}
