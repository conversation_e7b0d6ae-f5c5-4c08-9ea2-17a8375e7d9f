package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.RelationDataVo;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-1-27
 */
@Mapper
public interface RelationMapper {

    RelationMapper INSTANCE = Mappers.getMapper(RelationMapper.class);

    RelationDataVo copy(Relationship relationship);

    List<RelationDataVo> copy(List<Relationship> relationship);

    Relationship copy(RelationshipDTO relationshipDTO);

    Relationship copyBean(Relationship relationship);
}
