package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.api.vo.BatchVo;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.project.entity.mongo.PreAnnotation;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Service
public interface ApiService {

    /**
     * 校验Token合法性
     *
     * @param token 项目的唯一token标识码
     */
    void checkToken(String token);

    /**
     * 根据文章ArticleId查询指定文章，没有版本返回最新的
     *
     * @param articleId 文章ArticleId
     * @param version   文章版本
     * @return 文章
     */
    Document findArticleById(String articleId, Integer version);

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 实体标签
     */
    EntityLabelVo findLabelById(Long id);

    /**
     * 根据实体标签代码，查询实体标签
     *
     * @param code        标签code
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    EntityLabelVo findLabelByCode(Long code, String projectCode);

    /**
     * 根据token查询该项目下所有批次的简要信息
     *
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<BatchVo> findAllBatchInfo(String projectCode);

    /**
     * 根据ArticleId获取指定项目某个批次文献的实体标注数据
     *
     * @param articleId   文章ArticleId
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<EntityDataVo> findEntityByArticleId(String articleId, String projectCode);

    /**
     * 查询项目某个批次的所有实体标注数据
     *
     * @param batchId     批次ID
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<EntityDataVo> findEntityByBatchId(String batchId, String projectCode);

    /**
     * 查询项目所有实体标注数据
     *
     * @param projectCode 项目标识码
     * @return 实体标签
     */
    List<EntityDataVo> findAllEntity(String projectCode);


    /**
     * 查询当前文章有哪些预标注来源
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注来源
     */
    List<SourceVO> getSource(String articleId, Integer version);

    /**
     * 查询当前文章机器预标注数据
     *
     * @param articleId 文章ID
     * @param version   版本信息
     * @return 预标注数据
     */
    List<PreAnnotation> getPreAnnotation(String articleId, Integer version, String source);

    /**
     * 查询指定预标注数据
     *
     * @param articleId 文献id
     * @param version   版本
     * @param source    预标注来源
     * @param textId    textId
     * @param start     开始位置
     * @param end       介绍位置
     * @return 预标注数据
     */
    String getPreAnnotation(String articleId, Integer version, String source, String textId, Integer start, Integer end);

    Boolean updatePdf(MultipartFile file) throws IOException;

    List<LabelUseVo> getLabelUseInfo(String labelCode);
}
