package org.biosino.nlp.modules.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-1-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatternDataVo {

    private String id;
    private List<Item> item;


    @Data
    public static class Item implements Serializable {
        private static final long serialVersionUID = 1L;

        private String id;
        private Node subList;
        private Long relation;
        private Boolean negation;
        private List<String> annotations;
        private Node objList;
        private Integer order;
        private Boolean correct = true;

        @Data
        public static class Node implements Serializable {
            private static final long serialVersionUID = 1L;

            private Boolean isEntity;

            private List<String> entityIds;

            private String text;

            private Integer number;

            private Integer index;
        }
    }

}
