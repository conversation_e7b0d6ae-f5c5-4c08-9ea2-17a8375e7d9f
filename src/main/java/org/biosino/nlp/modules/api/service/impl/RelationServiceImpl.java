package org.biosino.nlp.modules.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.mapper.RelationMapper;
import org.biosino.nlp.modules.api.service.CommonService;
import org.biosino.nlp.modules.api.service.RelationService;
import org.biosino.nlp.modules.api.vo.RelationDataVo;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.service.RelationshipMapService;
import org.biosino.nlp.modules.note.service.RelationshipService;
import org.biosino.nlp.modules.project.dto.RelationAnnoJsonDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.service.DataExportService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RelationServiceImpl implements RelationService {

    private final CommonService commonService;
    private final RelationshipService relationshipService;
    private final RelationshipMapService relationshipMapService;
    private final BatchService batchService;
    private final NoteService noteService;
    private final DataExportService dataExportService;
    private final NoteTaskService noteTaskService;

    @Override
    public List<RelationAnnoJsonDTO> findByArticleId(String articleId, String projectCode) {
        if (StrUtil.isBlank(articleId)) {
            throw new RRException("articleId不能为空");
        }
        Project project = commonService.findProjectByCode(projectCode);
        Note note = noteService.getOne(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .eq(Note::getArticleId, articleId));
        if (note == null) {
            throw new RRException("该文章不存在");
        }
        NoteTask noteTask = noteTaskService.getOne(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster, MasterEnum.master.getValue()).eq(NoteTask::getNoteId, note.getNoteId()));
        if (noteTask == null) {
            throw new RRException("该标注任务不存在");
        }
        // List<Relationship> relationshipList = relationshipService.getRelationships(note.getNoteId());
        List<RelationAnnoJsonDTO> data = dataExportService.getRelationJson(noteTask);
        return data;

    }

    @Override
    public Graph findMapDataByArticleId(String articleId, Integer pattern, String projectCode) {
        if (StrUtil.isBlank(articleId)) {
            throw new RRException("articleId不能为空");
        }
        Project project = commonService.findProjectByCode(projectCode);
        Note note = noteService.getOne(Wrappers.<Note>lambdaQuery()
                .eq(Note::getProjectId, project.getProjectId())
                .eq(Note::getArticleId, articleId));
        if (note == null) {
            throw new RRException("该文章不存在");
        }
        return relationshipMapService.getData(note.getNoteId(), note.getNoteId(), pattern, PreSourceEnum.SELF.getId());
    }

    @Override
    public List<RelationAnnoJsonDTO> findByBatchId(Long batchId, String projectCode) {
        if (batchId == null) {
            throw new RRException("batchId不能为空");
        }
        Project project = commonService.findProjectByCode(projectCode);
        Batch batch = batchService.getOne(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, project.getProjectId())
                .eq(Batch::getBatchId, batchId));
        if (batch == null) {
            throw new RRException("该文章不存在");
        }
        List<NoteTask> noteTaskList = noteTaskService.list(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster,
                MasterEnum.master.getValue()).eq(NoteTask::getBatchId, batch.getBatchId()));
        // List<Relationship> relationshipList = relationshipService.getRelationshipByNoteIds(collect);
        List<RelationAnnoJsonDTO> data = dataExportService.getRelationJsonBatch(noteTaskList);

        return data;
    }


    @Override
    public List<RelationDataVo> findApprovedByBatchId(String batchId, String projectCode) {
        if (StrUtil.isBlank(batchId)) {
            throw new RRException("batchId不能为空");
        }
        Project project = commonService.findProjectByCode(projectCode);
        Batch batch = batchService.getOne(Wrappers.<Batch>lambdaQuery()
                .eq(Batch::getProjectId, project.getProjectId())
                .eq(Batch::getBatchId, batchId));
        if (batch == null) {
            throw new RRException("该文章不存在");
        }
        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery()
                        .select(Note::getNoteId)
                .eq(Note::getProjectId, project.getProjectId())
                .eq(Note::getBatchId, batch.getBatchId())
                .eq(Note::getInvalid, NoteInvalidEnum.normal.getCode())
                .eq(Note::getStep, NoteStepEnum.reviewed.getCode()));
        List<Long> collect = notes.stream().map(Note::getNoteId).collect(Collectors.toList());
        List<Relationship> relationshipList = relationshipService.getRelationshipByNoteIds(collect);
        return RelationMapper.INSTANCE.copy(relationshipList);
    }

    @Override
    public List<RelationAnnoJsonDTO> findAll(String projectCode) {
        Project project = commonService.findProjectByCode(projectCode);
        List<Note> notes = noteService.list(Wrappers.<Note>lambdaQuery().eq(Note::getProjectId, project.getProjectId()));
        if (CollUtil.isEmpty(notes)) {
            return null;
        }
        List<Long> noteIds = notes.stream().map(Note::getNoteId).collect(Collectors.toList());
        List<NoteTask> noteTaskList = noteTaskService.list(Wrappers.<NoteTask>lambdaQuery().eq(NoteTask::getMaster,
                MasterEnum.master.getValue()).in(NoteTask::getNoteId, noteIds));
        if (CollUtil.isEmpty(noteTaskList)) {
            throw new RRException("暂无数据");
        }
        return dataExportService.getRelationJsonBatch(noteTaskList);
    }


}
