package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.BatchVo;
import org.biosino.nlp.modules.project.entity.Batch;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-30 11:44
 */
@Mapper
public interface BatchMapper {

    BatchMapper INSTANCE = Mappers.getMapper(BatchMapper.class);

    BatchVo copy(Batch batch);

    List<BatchVo> copy(List<Batch> batches);

}
