package org.biosino.nlp.modules.api.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.DownloadUtil;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collections;

/**
 * 文档下载
 *
 * <AUTHOR>
 * @date 2021-01-20 16:36
 */
@Service
public class DocService {

    public ResponseEntity<Object> downloadTemplate(String fileName, String outName) throws IOException {
        verifyFileName(fileName);
        File docDir = Constant.getHomeDir(Constant.DirectoryEnum.doc);
        File file = new File(docDir, fileName);
        return download(file, outName);
    }

    private ResponseEntity<Object> download(File file) throws IOException {
        if (!file.exists()) {
            throw new RRException("文件找不到");
        }
        return download(file, file.getName());
    }

    private ResponseEntity<Object> download(File file, String fileName) throws IOException {
        if (!file.exists()) {
            throw new RRException("文件找不到");
        }

        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        fileName = URLEncoder.encode(fileName, "utf-8");

        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(Collections.singletonList("filename"));
        headers.setContentDispositionFormData("attachment", fileName);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.add("filename", fileName);

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }

    private void verifyFileName(final String filename) {
        if (StrUtil.isBlank(filename)) {
            throw new RRException("非法的文件名");
        }
        if (filename.contains("../") || filename.contains("..\\")) {
            throw new RRException("非法的文件名");
        }
    }

    public ResponseEntity<Resource> downloadInstructionPDF(String pdfName) {
        verifyFileName(pdfName);
        File pdfDir = Constant.getHomeDir(Constant.DirectoryEnum.pdf);
        File file = new File(pdfDir, pdfName);
        if (!file.exists()) {
            throw new RRException("没有该操作手册");
        }
        /*String fileName = URLEncoder.encode(pdfName, "utf-8");
        response.setContentType("application/octet-stream; charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        ServletOutputStream out = response.getOutputStream();
        out.write(FileUtils.readFileToByteArray(file));
        IoUtil.close(out);*/
        return DownloadUtil.previewPDF(file, pdfName);
    }

    public String test(String pdfName) {
        verifyFileName(pdfName);
        File pdfDir = Constant.getHomeDir(Constant.DirectoryEnum.pdf);
        File file = new File(pdfDir, pdfName);
        return file.getAbsolutePath();
    }

    public String testPath() {
        File pdfDir = Constant.getHomeDir(Constant.DirectoryEnum.pdf);
        return pdfDir.getAbsolutePath();
    }

    public void downloadReferencePDF(String articleId, HttpServletResponse resp) throws IOException {
        HttpUtil.download(Constant.bfmsApi + "/referencePDF/" + articleId, resp.getOutputStream(), true);
    }
}
