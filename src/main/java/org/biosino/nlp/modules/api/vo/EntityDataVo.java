package org.biosino.nlp.modules.api.vo;

import lombok.Data;
import org.biosino.nlp.modules.note.dto.MetaMapDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 实体标注结果数据VO
 *
 * <AUTHOR>
 */
@Data
public class EntityDataVo implements Serializable {
    private String pmid;
    private String textId;
    private Long labelId;
    private String labelCode;
    private String labelName;
    private List<Node> subLabel;
    private Integer start;
    private Integer end;
    private String text;
    private String conceptId;
    private String conceptName;
    private String annotate;
    private Long subLabels;
    private Long secondLabelId;
    private String secondLabelCode;
    private String secondLabelName;
    private MetaMapDTO metaMapDTO;
    private List<String> attr;

    @Data
    public static class Node implements Serializable {
        private Long labelId;
        private String labelCode;
        private String labelName;
    }
}
