package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.AnnotateDTO;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.project.dto.EntityAnnoExcelDTO;
import org.biosino.nlp.modules.project.dto.EntityAnnoJsonDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-30 12:35
 */
@Mapper
public interface EntityMapper {

    EntityMapper INSTANCE = Mappers.getMapper(EntityMapper.class);

    @Mapping(source = "articleId", target = "pmid")
    EntityDataVo copy(Entity batch);

    @Mapping(source = "articleId", target = "pmid")
    List<EntityDataVo> copy(List<Entity> batches);

    Entity annoDTOToEntity(AnnoDTO annoDTO);

    AnnoDTO entityToAnnoDTO(Entity e);

    Entity copyBean(Entity entity);

    List<EntityAnnoJsonDTO.EntityInfo> copyEntityInfo(List<AnnoDTO.EntityInfo> list);

    AnnoDTO.EntityInfo annoDtoToEntityInfo(AnnotateDTO annotateDTO);

    EntityAnnoExcelDTO copyToExcelDTO(Entity entity);

}
