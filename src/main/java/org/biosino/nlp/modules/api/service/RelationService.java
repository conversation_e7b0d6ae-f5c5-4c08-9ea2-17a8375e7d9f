package org.biosino.nlp.modules.api.service;

import org.biosino.nlp.modules.api.vo.RelationDataVo;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.project.dto.RelationAnnoJsonDTO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface RelationService {

    /**
     * 根据ArticleId获取指定项目某个批次文献的关系标注数据
     *
     * @param articleId   文章ArticleId
     * @param projectCode 项目标识码
     * @return 关系标注数据
     */
    List<RelationAnnoJsonDTO> findByArticleId(String articleId, String projectCode);

    /**
     * 根据ArticleId获取指定项目某个批次文献的关系图数据
     *
     * @param articleId   文章ArticleId
     * @param projectCode 项目标识码
     * @return 关系图数据
     */
    Graph findMapDataByArticleId(String articleId, Integer pattern, String projectCode);

    /**
     * 查询项目某个批次的所有关系标注数据
     *
     * @param batchId     批次ID
     * @param projectCode 项目标识码
     * @return 关系标注数据
     */
    List<RelationAnnoJsonDTO> findByBatchId(Long batchId, String projectCode);

    /**
     * 查询项目所有关系标注数据
     *
     * @param projectCode 项目标识码
     * @return 关系标注数据
     */
    List<RelationAnnoJsonDTO> findAll(String projectCode);

    /**
     * 查询项目某个批次的中已审核的关系标注数据
     *
     * @param batchId 批次ID
     * @param projectCode 项目标识码
     * @return 关系标注数据
     */
    List<RelationDataVo> findApprovedByBatchId(String batchId, String projectCode);
}
