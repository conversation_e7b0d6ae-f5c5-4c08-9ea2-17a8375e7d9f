package org.biosino.nlp.modules.api.service.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.mapper.EntityLabelMapper;
import org.biosino.nlp.modules.api.service.LabelService;
import org.biosino.nlp.modules.api.vo.EntityLabelVo;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LabelServiceImpl implements LabelService {

    private final EntityLabelService entityLabelService;

    /**
     * 根据实体标签ID，查询实体标签
     *
     * @param id 标签ID
     * @return 实体标签
     */
    @Override
    public EntityLabelVo findLabelById(Long id) {
        if (id == null) {
            throw new RRException("标签ID不能为空");
        }
        EntityLabel entityLabel = entityLabelService.getById(id);
        return EntityLabelMapper.INSTANCE.copy(entityLabel);
    }

//    @Override
//    public EntityLabelVo findLabelByCode(String code, String projectCode) {
//        EntityLabel entityLabel = entityLabelService.getOne(Wrappers.<EntityLabel>lambdaQuery()
//                .eq(EntityLabel::getProjectId, projectCode)
//                .eq(EntityLabel::getValue, code));
//        return EntityLabelMapper.INSTANCE.copy(entityLabel);
//    }

}
