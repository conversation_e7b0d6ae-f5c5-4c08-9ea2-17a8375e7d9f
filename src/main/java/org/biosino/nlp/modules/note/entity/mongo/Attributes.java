package org.biosino.nlp.modules.note.entity.mongo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "m_attributes")
public class Attributes extends BaseMongo implements Serializable {
    @Transient
    private static final long serialVersionUID = -1662201234574075282L;

    @Indexed
    @Field("task_id")
    private Long taskId;

    @Field("project_id")
    private Long projectId;

    @Field("batch_id")
    private Long batchId;

    @Field("article_id")
    private String articleId;

    @Indexed
    @Field("entity_id")
    private String entityId;
    @Indexed
    @Field("attr_label_id")
    private Long attrLabelId;

    @Field("annotator_id")
    private Long annotatorId;

    @Field("auditor_id")
    private Long auditorId;

    @Indexed
    @Field("attribute_id")
    private String attributeId;

    @Field("entity_md5")
    private String entityMd5;
    @Field("attr_md5")
    private String attrMd5;

    @Indexed
    private String content;

    @Field("create_time")
    private Date createTime;

//    private String md5;
}
