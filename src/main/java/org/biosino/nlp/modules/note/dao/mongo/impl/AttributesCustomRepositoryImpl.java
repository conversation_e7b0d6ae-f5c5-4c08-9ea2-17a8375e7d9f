package org.biosino.nlp.modules.note.dao.mongo.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.modules.labels.dao.AttributeLabelDao;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.note.dao.mongo.AttributesCustomRepository;
import org.biosino.nlp.modules.note.dto.AttrAnnoLabelDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class AttributesCustomRepositoryImpl implements AttributesCustomRepository {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private AttributeLabelDao attributeLabelDao;

    @Override
    public Class<Attributes> getCls() {
        return Attributes.class;
    }

    @Override
    public MongoTemplate getMongoTemplate() {
        return mongoTemplate;
    }

    @Override
    public List<Attributes> findByEntityId(String entityId) {
        if (StrUtil.isBlank(entityId)) {
            return new ArrayList<>();
        }

        return mongoTemplate.find(Query.query(baseCriteria()
                .and("entity_id").is(entityId)), getCls());
    }

    @Override
    public List<Attributes> findDelByEntityId(String entityId, Long annotatorId) {
        if (StrUtil.isBlank(entityId)) {
            return new ArrayList<>();
        }

        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("entity_id").is(entityId));
        // 仅查询当前标注员的标注信息(包含审核员删除的内容)
        EntityCustomRepositoryImpl.initDelCondition(condition, baseCriteria(), annotatorId);

        return mongoTemplate.find(Query.query(andQuery(condition)), getCls());
    }

    @Override
    public List<Attributes> findByEntityIdHasAttributeId(String entityId) {
        return mongoTemplate.find(Query.query(baseCriteria()
                .and("attribute_id").exists(true)
                .and("entity_id").is(entityId)), getCls());
    }

    @Override
    public List<Attributes> findByEntityIdAndAttrLabelId(String entityId, Long attrLabelId) {
        return mongoTemplate.find(Query.query(baseCriteria()
                .and("attr_label_id").is(attrLabelId)
                .and("entity_id").is(entityId)), getCls());
    }

    @Override
    public List<Attributes> findByEntityIdOrAttrId(String entityId, String attId) {
        Criteria criteria = new Criteria().orOperator(
                Criteria.where("attribute_id").is(attId),
                Criteria.where("entity_id").is(entityId)
        );
        return mongoTemplate.find(Query.query(baseCriteria().andOperator(criteria)), getCls());
    }

    @Override
    public List<Attributes> findByAttributeId(String attributeId) {
        if (StrUtil.isBlank(attributeId)) {
            return new ArrayList<>();
        }
        return mongoTemplate.find(Query.query(baseCriteria()
                .and("attribute_id").is(attributeId)), getCls());
    }

    @Override
    public List<Attributes> findDelByAttributeId(String attributeId, Long annotatorId) {
        if (StrUtil.isBlank(attributeId)) {
            return new ArrayList<>();
        }
        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("attribute_id").is(attributeId));
        // 仅查询当前标注员的标注信息(包含审核员删除的内容)
        EntityCustomRepositoryImpl.initDelCondition(condition, baseCriteria(), annotatorId);

        return mongoTemplate.find(Query.query(andQuery(condition)), getCls());
    }

    @Override
    public boolean isAttrExists(String attributeId) {
        return mongoTemplate.exists(Query.query(baseCriteria()
                .and("attribute_id").is(attributeId)), getCls());
    }

    @Override
    public long countByEntityIdAndAttrLabelIdAndContentAndAttributeId(String entityId, Long attrLabelId, String content, String attribute_id) {
        Criteria criteria = baseCriteria();
        attribute_id = StrUtil.trimToNull(attribute_id);
        if (attribute_id == null) {
            //自定义属性,不同属性之间可以重复。属性标注全局不能重复
            criteria.and("attr_label_id").is(attrLabelId)
                    .and("entity_id").is(entityId);
        }
        criteria.and("attribute_id").is(attribute_id)
                .and("content").is(content);
        return mongoTemplate.count(Query.query(criteria), getCls());
    }

    @Override
    public List<Attributes> findAllEntityIdIn(Collection<String> ids) {
        return mongoTemplate.find(Query.query(baseCriteria()
                .and("entity_id").in(ids)), getCls());
    }

    @Override
    public List<String> findTwoMd5(Long taskId, Long userId) {
        Query query = Query.query(baseCriteria().and("task_id").is(taskId));
        if (userId != null) {
            query.addCriteria(Criteria.where("annotator_id").is(userId));
        }
        query.fields().include("entity_md5").include("attr_md5");
        query.with(Sort.by("entity_md5", "attr_md5"));
        List<Attributes> attributes = mongoTemplate.find(query, Attributes.class);
        if (CollUtil.isEmpty(attributes)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (Attributes attribute : attributes) {
            result.add(attribute.getEntityMd5() + "_" + attribute.getAttrMd5());
        }
        return result;
    }

    /*@Override
    public void deleteAllByEntityId(String entityId) {
        Query query = Query.query(baseCriteria().and("entity_id").is(entityId));
        mongoTemplate.updateMulti(query, deleteUpdate(), getCls());
    }*/

    /*@Override
    public void deleteByEntityId(String entityId) {
        Query query = Query.query(baseCriteria().and("entity_id").is(entityId));
        mongoTemplate.updateMulti(query, deleteUpdate(), getCls());
    }*/

    @Override
    public boolean existsByAttrLabelId(Long attrLabelId) {
        return mongoTemplate.exists(Query.query(baseCriteria()
                .and("attr_label_id").is(attrLabelId)
                .and("task_id").ne(null)
                .and("annotator_id").ne(null)), getCls());
    }

    @Override
    public long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted) {
        Query query = new Query();
        query.addCriteria(Criteria.where("task_id").is(taskId));
        if (deleted != null) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        query.addCriteria(Criteria.where("annotator_id").is(annotatorId));
        query.addCriteria(Criteria.where("auditor_id").is(auditorId));
        return mongoTemplate.count(query, Attributes.class);
    }

    @Override
    public Map<String, Integer> countMapByEntityIds(Collection<String> ids) {
        // 聚合统计entityId出现的次数
        AggregationResults<Map> results = mongoTemplate.aggregate(Aggregation.newAggregation(
                Aggregation.match(baseCriteria().and("entity_id").in(ids)),
                Aggregation.group("entityId").count().as("count")
        ), getCls(), Map.class);
        Map<String, Integer> counts = new HashMap<>();
        for (String id : ids) {
            counts.put(id, 0);
        }
        for (Map<String, Object> result : results) {
            String id = result.get("_id").toString();
            Integer count = (Integer) result.get("count");
            counts.put(id, count);
        }
        return counts;
    }

    @Override
    public void deleteAllByNoteId(Long noteId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("note_id").is(noteId));
        mongoTemplate.remove(query, Attributes.class);
    }

    @Override
    public Map<String, LinkedHashSet<AttrAnnoLabelDTO>> findAttrAnnoLabelByAttributeIds(final List<String> attributeIds) {
        final Map<String, LinkedHashSet<AttrAnnoLabelDTO>> map = new LinkedHashMap<>();
        if (CollUtil.isEmpty(attributeIds)) {
            return map;
        }
        final Query query = Query.query(baseCriteria().and("attribute_id").in(attributeIds));
        query.fields().include("entity_id").include("attribute_id").include("attr_label_id");
        List<Attributes> attributesList = mongoTemplate.find(query, getCls());
        if (CollUtil.isNotEmpty(attributesList)) {
            //按照输入集合顺序排序
            attributesList = attributesList.stream().sorted((o1, o2) -> {
                int index1 = attributeIds.indexOf(o1.getAttributeId());
                int index2 = attributeIds.indexOf(o2.getAttributeId());
                return Integer.compare(index1, index2);
            }).collect(Collectors.toList());

            final List<Long> attrLabelIds = attributesList.stream().map(Attributes::getAttrLabelId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            final Map<Long, AttributeLabel> attributeLabelMap = attributeLabelDao.selectList(Wrappers.<AttributeLabel>lambdaQuery().in(AttributeLabel::getId, attrLabelIds))
                    .stream().collect(Collectors.toMap(AttributeLabel::getId, x -> x));
            for (Attributes attribute : attributesList) {
                final Long attrLabelId = attribute.getAttrLabelId();
                final AttributeLabel attributeLabel = attributeLabelMap.get(attrLabelId);
                if (attributeLabel == null) {
                    continue;
                }
                final AttrAnnoLabelDTO dto = new AttrAnnoLabelDTO();
                dto.setAttrLabelId(attrLabelId);
                dto.setAttrLabelName(attributeLabel.getName());
                dto.setEntityId(attribute.getEntityId());
                final String attributeId = attribute.getAttributeId();
                dto.setAttributeId(attributeId);

                LinkedHashSet<AttrAnnoLabelDTO> attrAnnoLabelDTOS = map.get(attributeId);
                if (attrAnnoLabelDTOS == null) {
                    attrAnnoLabelDTOS = new LinkedHashSet<>();
                }
                attrAnnoLabelDTOS.add(dto);
                map.put(attributeId, attrAnnoLabelDTOS);
            }
        }
        return map;
    }


    @Override
    public long countByUserId(Long projectId, Long userId, Long roleId, Long taskId) {
        Query query = Query.query(baseCriteria().and("project_id").is(projectId));
        if (taskId != null) {
            query.addCriteria(Criteria.where("task_id").is(taskId));
        }
        if (roleId == RoleEnum.annotator.getId()) {
            query.addCriteria(Criteria.where("annotator_id").is(userId));
        }
        if (roleId == RoleEnum.auditor.getId()) {
            query.addCriteria(Criteria.where("auditor_id").is(userId));
        }
        return mongoTemplate.count(query, Attributes.class);
    }

    @Override
    public void deleteByAttributeId(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Query query = Query.query(Criteria.where("attribute_id").in(ids));
        mongoTemplate.updateMulti(query, deleteUpdate(), getCls());
    }

    /**
     * 删除属性关系(真实删除)
     */
    @Override
    public void deleteByAttributeIdInFact(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Query query = Query.query(Criteria.where("attribute_id").in(ids));
        mongoTemplate.remove(query, Attributes.class);
    }

    @Override
    public void deleteByImportLogId(Long id) {
        // 根据importLogId删除数据
        if (id == null) {
            return;
        }
        mongoTemplate.remove(Query.query(Criteria.where("import_log_id").is(id)), getCls());
    }

    @Override
    public Map<String, Boolean> findEntityAndAttrMap(Set<Long> otherTaskIds, final Set<String> md5Set) {
        final Set<Long> allTaskIds = new HashSet<>(otherTaskIds);

        final List<Attributes> attributes = mongoTemplate.find(Query.query(baseCriteria()
                .and("task_id").in(allTaskIds)
                .and("attr_md5").exists(true)
                .and("entity_md5").in(md5Set)), getCls());

        final Map<String, Map<Long, TreeSet<String>>> map = new HashMap<>();
        for (Attributes attribute : attributes) {
            final String entityMd5 = attribute.getEntityMd5();
            Map<Long, TreeSet<String>> m2 = map.computeIfAbsent(entityMd5, k -> new HashMap<>());
            final Long taskId1 = attribute.getTaskId();
            TreeSet<String> attrMd5Set = m2.computeIfAbsent(taskId1, k -> new TreeSet<>());
            attrMd5Set.add(attribute.getAttrMd5());
        }

        final Map<String, Boolean> mapHasAttr = new HashMap<>();
        for (Map.Entry<String, Map<Long, TreeSet<String>>> entry : map.entrySet()) {
            final String entityMd5 = entry.getKey();
            final Map<Long, TreeSet<String>> valueMap = entry.getValue();
            Collection<TreeSet<String>> values = valueMap.values();
            if (CollUtil.size(values) == CollUtil.size(allTaskIds)) {
                Set<TreeSet<String>> uniqueValues = new HashSet<>(values);
                if (uniqueValues.size() == 1) {
                    // 所有元素相同
                    mapHasAttr.put(entityMd5, true);
                } else {
                    // 存在不同的元素
                    mapHasAttr.put(entityMd5, false);
                }
            } else {
                mapHasAttr.put(entityMd5, false);
            }

        }

        final Map<String, Boolean> result = new HashMap<>();
        for (String s : md5Set) {
            if (mapHasAttr.containsKey(s)) {
                result.put(s, Boolean.TRUE.equals(mapHasAttr.get(s)));
            } else {
                result.put(s, true);
            }
        }
        return result;
    }

}
