package org.biosino.nlp.modules.note.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.labels.dao.RelationLabelDao;
import org.biosino.nlp.modules.labels.dao.RelationPatternDao;
import org.biosino.nlp.modules.labels.dao.UmlsConceptDao;
import org.biosino.nlp.modules.labels.entity.*;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.project.dao.BatchDao;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.service.LoadDocumentService;
import org.biosino.nlp.modules.project.service.ProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 删除的方法
 */
@Service
public class DeletedService {

    @Autowired
    private NoteDao noteDao;
    @Autowired
    private BatchDao batchDao;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private UmlsConceptDao umlsConceptDao;
    @Autowired
    private RelationPatternDao relationPatternDao;
    @Autowired
    private RelationLabelDao relationLabelDao;
    @Autowired
    private LoadDocumentService loadDocument;
    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private EntityLabelService entityLabelService;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private RelationshipRepository relationshipRepository;
    @Autowired
    private CommentService commentService;

    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Long projectId, Long userId) {
        Project project = projectDao.selectById(projectId);
        if (project == null) {
            throw new RRException("此项目不存在");
        }
        if (!project.getCreatorId().equals(userId)) {
            throw new RRException("没有权限进行此操作");
        }
        projectDao.deleteById(projectId);

        // 删除批次下的所有批次
        LambdaQueryWrapper<Batch> wrapper = Wrappers.<Batch>lambdaQuery().eq(Batch::getProjectId, projectId);
        List<Batch> batchList = batchDao.selectList(wrapper);
        if (CollUtil.isNotEmpty(batchList)) {
            for (Batch batch : batchList) {
                batchDao.deleteById(batch.getBatchId());
            }
        }

        // 删除实体标签
        LambdaQueryWrapper<EntityLabel> queryWrapper = Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getProjectId, projectId);
        List<EntityLabel> entityLabels = entityLabelService.list(queryWrapper);
        for (EntityLabel entityLabel : entityLabels) {
            entityLabelService.removeById(entityLabel.getId());
            // 删除属性标签
            attributeLabelService.remove(Wrappers.<AttributeLabel>lambdaQuery().eq(AttributeLabel::getEntityLabelId, entityLabel.getId()));
        }

        // 删除关系标签和关系模板
        relationPatternDao.delete(Wrappers.<RelationPattern>lambdaQuery().eq(RelationPattern::getProjectId, projectId));
        relationLabelDao.delete(Wrappers.<RelationLabel>lambdaQuery().eq(RelationLabel::getProjectId, projectId));

        // 删除UMLS Concept
        umlsConceptDao.delete(Wrappers.<UmlsConcept>lambdaQuery().eq(UmlsConcept::getProjectId, projectId));

        // 删除参与者
        projectUserService.remove(Wrappers.<ProjectUser>lambdaQuery().eq(ProjectUser::getProjectId, projectId));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(Long batchId) {
        Batch batch = batchDao.selectById(batchId);
        if (batch == null) {
            throw new RRException("此批次不存在");
        }
        batchDao.deleteById(batchId);
        loadDocument.deletedByBatchId(batchId);

        // 删除批次下的所有讨论区记录
        commentService.deleteCommentsByBatchId(batchId);

        // 删除批次下的所有信息
        LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId);
        List<Note> noteList = noteDao.selectList(wrapper);
        if (CollUtil.isEmpty(noteList)) {
            return;
        }
        for (Note note : noteList) {
            resetArticle(note.getNoteId(), true);
        }
    }

    /**
     * 重置文章
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetArticle(Long noteId, Boolean deleted) {
        Note note = noteDao.selectById(noteId);
        // 删除有关的关系
        relationshipRepository.deleteByNoteIdAndSource(noteId, PreSourceEnum.SELF.getId());
        // 删除有关的实体和属性之间的关系
        attributesRepository.deleteByBatchIdAndArticleId(note.getBatchId(), note.getArticleId());
        // 删除有关的实体和属性
        entityRepository.deleteByNoteIdAndSource(noteId, PreSourceEnum.SELF.getId());
        // 删除讨论区记录
        commentService.deleteCommentsByNoteId(noteId);

        // 删除有关NoteTask
        noteTaskDao.reallyDeleteByNoteId(noteId);

        // 如果删除
        if (deleted) {
            noteDao.deleteById(noteId);
            loadDocument.deletedByArticleId(note.getBatchId(), note.getArticleId());
            Batch batch = batchDao.selectById(note.getBatchId());
            if (batch != null) {
                if (batch.getTotalArticle() != null && batch.getTotalArticle() > 0) {
                    batch.setTotalArticle(batch.getTotalArticle() - 1);
                }
                batchDao.updateById(batch);
            }
            return;
        }
        // 如果是重置文章
        note.setPullCount(0);
        note.setStep(NoteStepEnum.unmarked.getCode());
        note.setInvalid(NoteInvalidEnum.normal.getCode());
        note.setUpdateTime(new Date());
        noteDao.updateById(note);
    }
}
