package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Collection;
import java.util.List;

public interface DocumentRepository extends MongoRepository<Document, String>, DocumentCustomRepository {
    List<Document> findAllByIdIn(Collection<String> documentIds);
}
