package org.biosino.nlp.modules.note.entity.mongo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.nlp.modules.note.dto.AnnotationsDTO;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 * NLP文章表，用于存放具体的文章数据（文档集）
 *
 * <AUTHOR>
 * @date 2020/11/26
 */
@Data
@org.springframework.data.mongodb.core.mapping.Document(collection = "m_document")
public class Document implements Serializable {
    private static final long serialVersionUID = 1000001L;

    @Id
    private String id;

    @Field(name = "article_id")
    private String articleId;
    private String source;
    private Integer version;
    private String key;

    private String cls;
    private TitleDTO title;
    private String language;

    @Field(name = "abstract")
    @JSONField(name = "abstract")
    private AbstractDTO abstracts;
    private List<BodyDTO> body;
    private List<InfoDTO> info;
    private List<FiguresDTO> figures;
    private List<TablesDTO> tables;

    @Data
    @NoArgsConstructor
    public static class TitleDTO implements Serializable {
        private String cls;
        private String content;
        @Field(name = "id")
        private String id;
        private Integer level;
    }

    @Data
    @NoArgsConstructor
    public static class AbstractDTO implements Serializable {
        private String cls;
        private TitleDTO title;
        @Field(name = "id")
        private String id;
        private List<ItemsDTO> items;
    }

    @Data
    @NoArgsConstructor
    public static class ItemsDTO implements Serializable {
        private String cls;
        private String content;
        @Field(name = "id")
        private String id;
        private List<FormatsDTO> formats;

        private List<AnnotationsDTO> annotations;
        private List<ItemsDTO> items;
    }

    @Data
    @NoArgsConstructor
    public static class BodyDTO implements Serializable {
        private String cls;
        private TitleDTO title;
        @Field(name = "id")
        private String id;
        private List<ItemsDTO> items;
    }

    @Data
    @NoArgsConstructor
    public static class FormatsDTO implements Serializable {
        private String cls;
        private Integer start;
        private Integer end;
        private String label;
        private String href;
    }

    @Data
    @NoArgsConstructor
    public static class InfoDTO implements Serializable {
        private String cls;
        private TitleDTO title;
        @Field(name = "id")
        private String id;
        private List<ItemsDTO> items;
    }

    @Data
    @NoArgsConstructor
    public static class FiguresDTO implements Serializable {
        private String cls;
        private String src;
        private String ref;
        private String data;
        @Field(name = "id")
        private String id;
        private CaptionDTO caption;
    }

    @Data
    @NoArgsConstructor
    public static class CaptionDTO implements Serializable {
        private String cls;
        private TitleDTO title;
        @Field(name = "id")
        private String id;
        private List<ItemsDTO> items;
    }

    @Data
    @NoArgsConstructor
    public static class TablesDTO implements Serializable {
        private String cls;
        private String name;
        private CaptionDTO caption;
        @Field(name = "id")
        private String id;
        private List<List<Td>> thead;
        private List<List<Td>> tbody;

        @Data
        @NoArgsConstructor
        public static class Td implements Serializable {
            private String cls;
            private String rowspan;
            private String colspan;
            private String content;
            @Field(name = "id")
            private String id;
            private List<FormatsDTO> formats;
            private List<AnnotationsDTO> annotations;
        }
    }
}
