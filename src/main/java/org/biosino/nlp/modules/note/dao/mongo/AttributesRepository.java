package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.springframework.data.mongodb.repository.MongoRepository;

public interface AttributesRepository extends MongoRepository<Attributes, String>, AttributesCustomRepository {
    void deleteByTaskId(Long taskId);

    void deleteByTaskIdAndAuditorId(Long taskId, Long userId);

    void deleteByBatchIdAndArticleId(Long batchId, String articleId);
}
