package org.biosino.nlp.modules.note.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 20:53 2023/3/15
 * @Description
 **/

@Data
@ApiModel("分页参数")
public class PageParam implements Serializable {

    private static final Integer PAGE_NO = 0;

    private static final Integer PAGE_SIZE = 10;

    @ApiModelProperty(value = "页码，从 0 开始", required = true, example = "0")
    @NotNull(message = "页码不能为空")
    @Min(value = 0, message = "页码最小值为 0")
    private Integer current = PAGE_NO;

    @ApiModelProperty(value = "每页条数，最大值为 1000", required = true, example = "10")
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Max(value = 1000, message = "每页条数最大值为 1000")
    private Integer size = PAGE_SIZE;


    /**
     * 排序列
     */
    @ApiModelProperty(value = "排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向desc或者asc")
    private String isAsc = "desc";

    public void setIsAsc(String isAsc) {
        if (StrUtil.isNotEmpty(isAsc)) {
            // 兼容前端排序类型
            if ("ascending".equals(isAsc)) {
                isAsc = "asc";
            } else if ("descending".equals(isAsc)) {
                isAsc = "desc";
            }
            this.isAsc = isAsc;
        }
    }

    public String getOrderBy() {
        if (StrUtil.isEmpty(orderByColumn)) {
            return "";
        }
        return StrUtil.toUnderlineCase(orderByColumn) + " " + isAsc;
    }
}
