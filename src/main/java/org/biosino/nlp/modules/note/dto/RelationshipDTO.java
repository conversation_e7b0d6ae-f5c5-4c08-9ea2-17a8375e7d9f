package org.biosino.nlp.modules.note.dto;

import lombok.Data;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-26 16:19
 */
@Data
public class RelationshipDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    @NotNull
    private Long noteId;

    @NotNull
    private Long taskId;

    private String articleId;

    private String md5;

    private List<Relationship.RelationItem> items;

    @NotNull
    private Long patternId;

    private Long importLogId;

    private Long annotatorId;

    private Long auditorId;
}
