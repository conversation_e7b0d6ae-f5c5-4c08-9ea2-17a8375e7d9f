package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-26 15:52
 */
@Repository
public interface RelationshipRepository extends MongoRepository<Relationship, String>, RelationshipCustomRepository {

    List<Relationship> findAllByNoteIdAndSourceAndDeletedOrderByCreateTime(Long noteId, int source, Boolean deleted);

    List<Relationship> findAllByNoteIdAndDeletedOrderByCreateTime(Long noteId, <PERSON>olean deleted);

    List<Relationship> findAllByBatchIdAndDeletedOrderByCreateTime(Long noteId, Boolean deleted);

    List<Relationship> findAllByNoteIdInAndDeletedOrderByCreateTime(List<Long> noteIds, Boolean deleted);

    List<Relationship> findAllByProjectIdAndDeletedOrderByCreateTime(Long projectId, Boolean deleted);

    boolean existsByPatternIdAndDeleted(Integer patternId, Boolean deleted);

    void deleteAllByProjectIdAndImportLogId(Long projectId, Long userImport);

    boolean existsByTaskIdAndNoteIdAndAnnotatorIdAndAuditorIdAndSourceAndMd5AndDeleted(Long taskId, Long noteId, Long annotatorId, Long auditorId, int source, String md5, Boolean deleted);

    List<Relationship> findAllByTaskIdAndPatternIdAndDeleted(Long taskId, Integer patternId, Boolean deleted);

    List<Relationship> findAllByTaskIdAndDeleted(Long taskId, boolean deleted);

    boolean existsByTaskIdAndMd5AndDeleted(Long masterTaskId, String md5, boolean deleted);

    void deleteByTaskId(Long taskId);

    void deleteByNoteIdAndSource(Long noteId, Integer source);

    void deleteByTaskIdAndAuditorId(Long taskId, Long userId);

}
