package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.biosino.nlp.common.enums.*;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.DateUtils;
import org.biosino.nlp.common.utils.IdUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.common.validator.ValidatorUtils;
import org.biosino.nlp.modules.api.mapper.AttributeEntityMapper;
import org.biosino.nlp.modules.api.mapper.EntityMapper;
import org.biosino.nlp.modules.api.mapper.RelationMapper;
import org.biosino.nlp.modules.api.service.LabelService;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.entity.UmlsConcept;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.UmlsConceptService;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.CommentRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.*;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.*;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.enums.OptionEnum;
import org.biosino.nlp.modules.note.service.AnnoCacheService;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.validata.AddEntity;
import org.biosino.nlp.modules.note.vo.AddEntityVO;
import org.biosino.nlp.modules.note.vo.EntityListTreeVO;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.dto.NoteDTO;
import org.biosino.nlp.modules.project.entity.ImportLog;
import org.biosino.nlp.modules.project.service.ImportLogService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.biosino.nlp.modules.semantic.entity.SemanticType;
import org.biosino.nlp.modules.semantic.service.SemanticTypeService;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.biosino.nlp.common.utils.BnlpUtils.*;
import static org.biosino.nlp.common.utils.Constant.LIMIT_ONE;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("noteService")
@RequiredArgsConstructor
public class NoteServiceImpl extends ServiceImpl<NoteDao, Note> implements NoteService {

    private static final int TIMEOUT = 3 * 1000;
    /**
     * 获取UMLS数据, 示例链接地址 https://www.biosino.org/bnlp/utils-api/metamap/?text=metadata&retMax=10&pass=43898b
     */
    @Value("${api.meta-map-url:https://idc.biosino.org/bnlp/utils-api/}")
    private String UMLS_API_URL;

    //@Autowired
    private final ProjectService projectService;
    //@Autowired
    private final DocumentService documentService;
    //@Autowired
    private final EntityRepository entityRepository;
    //@Autowired
    private final RelationshipRepository relationshipRepository;
    //@Autowired
    private final AttributesRepository attributesRepository;
    //@Autowired
    private final CommentRepository commentRepository;
    //@Autowired
    private final AttributeLabelService attributeLabelService;
    //@Autowired
    private final EntityLabelService entityLabelService;
    //@Autowired
    private final SemanticTypeService semanticTypeService;
    //@Autowired
    private final UmlsConceptService umlsConceptService;
    //@Autowired
    private final CommonService commonService;
    //@Autowired
    private final NoteTaskDao noteTaskDao;
    //@Autowired
    private final AnnoCacheService annoCacheService;
    //@Autowired
    private final SysUserService sysUserService;
    //@Autowired
    private final MongoTemplate mongoTemplate;

    private final ImportLogService importLogService;

    private final ProjectDao projectDao;

    /**
     * 自动分配文章
     *
     * @param batchId 批次ID
     * @param userId  用户ID
     * @return 分配的文章ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Note assignArticle(Long batchId, Long userId, Long roleId) {
        // 标注员
        if (roleId == RoleEnum.annotator.getId()) {
            // 先查询是否有遗留的未标注完的记录
            LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                    .eq(Note::getBatchId, batchId)
                    .eq(Note::getStep, NoteStepEnum.noting.getCode())
                    .last(LIMIT_ONE);

            Note note = baseMapper.selectOne(wrapper);

            // 如果没有标注中的数据，则寻找分配新的未标注的数据
            if (note == null) {
                wrapper = Wrappers.<Note>lambdaQuery()
                        .eq(Note::getBatchId, batchId)
                        .eq(Note::getStep, NoteStepEnum.unmarked.getCode())
                        .last(LIMIT_ONE);
                note = baseMapper.selectOne(wrapper);

                // 将Note中的记录改为标注中
                if (note == null) {
                    return null;
                }
                note.setStep(NoteStepEnum.noting.getCode());
                baseMapper.updateById(note);
            }
            return note;
        }
        // 审核员
        if (roleId == RoleEnum.auditor.getId()) {
            LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                    .eq(Note::getBatchId, batchId)
                    .eq(Note::getStep, NoteStepEnum.reviewing.getCode())
                    .last(LIMIT_ONE);
            Note note = baseMapper.selectOne(wrapper);

            if (note == null) {
                wrapper = Wrappers.<Note>lambdaQuery()
                        .eq(Note::getBatchId, batchId)
                        .eq(Note::getStep, NoteStepEnum.marked.getCode())
                        .last(LIMIT_ONE);
                note = baseMapper.selectOne(wrapper);
                if (note == null) {
                    wrapper = Wrappers.<Note>lambdaQuery()
                            .eq(Note::getBatchId, batchId)
                            .eq(Note::getStep, NoteStepEnum.marked.getCode())
                            .last(LIMIT_ONE);
                    note = baseMapper.selectOne(wrapper);
                    if (note == null) {
                        return null;
                    }
                }
                note.setStep(NoteStepEnum.reviewing.getCode());
                baseMapper.updateById(note);
            }
            return note;
        }
        return null;
    }

    /**
     * 判断id是否有效
     *
     * @return 无效返回true
     */
    public static boolean isInvalidLong(final Long l) {
        return l == null || l == 0L;
    }

    /**
     * 多人标注, 判断当前用户是否有审核员权限
     */
    private boolean isPrjAuditor(final Long projectId) {
        final SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return false;
        }
        return CollUtil.contains(projectDao.findMultiAuditUserIdByPrjAndRoleId(projectId, RoleEnum.auditor.getId()), user.getUserId());
    }

    /**
     * 查询填充好回显数据的文章
     */
    @Override
    public Document getDocumentArticle(final String documentId, final Long taskId, Integer source, final Long noteId, final boolean showAnnoOnly,
                                       final Long annotatorId, final Long currImportLogId, final String discussionId) {
        if (StrUtil.isBlank(documentId)) {
            throw new RRException("非法的documentId");
        }
        final Document document = documentService.getDocumentById(documentId);
        final PreSourceEnum sourceEnum = findBySource(source);
        if (isInvalidLong(taskId) && sourceEnum == PreSourceEnum.SELF) {
            return document;
        }
        final Note note = this.getById(noteId);
        if (note == null) {
            throw new RRException("未查询到note数据");
        }
        final String articleId = note.getArticleId();
        final Long projectId = note.getProjectId();
        final Integer step = note.getStep();

        // 审核中
        final boolean isReviewing = NoteStepEnum.reviewing.getCode().equals(step);
        // 是否需要给多标注员相同标注添加特殊标记
        boolean showSameEntity = false;
        if (isReviewing) {
            showSameEntity = isPrjAuditor(projectId);
        }

        // 是否为预标注
        final boolean isPre = !PreSourceEnum.SELF.equals(sourceEnum);
        Long queryTaskId = taskId;
        if (showAnnoOnly) {
            // 查看标注员原标注信息时，需要查询其自己的taskId
            final NoteTask noteTask = noteTaskDao.selectById(taskId);
            if (!noteTask.getAnnotator().equals(annotatorId)) {
                final LambdaQueryWrapper<NoteTask> taskWrapper2 = Wrappers.<NoteTask>lambdaQuery()
                        .eq(NoteTask::getProjectId, noteTask.getProjectId())
                        .eq(NoteTask::getBatchId, noteTask.getBatchId())
                        .eq(NoteTask::getNoteId, noteTask.getNoteId())
                        .eq(NoteTask::getAnnotator, annotatorId);
                queryTaskId = noteTaskDao.selectOne(taskWrapper2).getTaskId();
            }
        }

        List<Entity> noteList;

        // 如果是历史版本模式，过滤只显示讨论相关的实体和属性
        if (StrUtil.isNotBlank(discussionId)) {
            noteList = filterHistoricalEntities(discussionId);
        } else {
            noteList = entityRepository.findDocumentArticle(queryTaskId, noteId, sourceEnum, showAnnoOnly, annotatorId, currImportLogId, articleId, projectId);
        }

        Map<String, List<AnnotationsDTO>> map = new HashMap<>(16);
        if (CollUtil.isNotEmpty(noteList)) {
            final Map<String, Long> entityLabelMap = new HashMap<>();
            if (isPre) {
                // 预标注时，需要查出自标注数据，用于判断预标注是否被使用
                final List<Entity> selfEntityList = entityRepository.findDocumentArticle(taskId, noteId, PreSourceEnum.SELF, currImportLogId, articleId, projectId);
                if (CollUtil.isNotEmpty(selfEntityList)) {
                    for (Entity entity : selfEntityList) {
                        final Long labelId = entity.getLabelId();
                        final Integer isAttr = entity.getIsAttr();
                        String key = genEntityStrMd5(entity.getEntityInfos(), labelId, isAttr);
                        if (AttrAnnoEnum.is_attr.getCode() == isAttr) {
                            if (labelId != null) {
                                entityLabelMap.put(key, labelId);
                            } else {
                                EntityAttrDTO dto = findAttrLabelInfoStr(entity.getId(), isAttr, false, false, null);
                                Long labelIdOfAttrAnno = dto.getLabelIdOfAttrAnno();
                                if (labelIdOfAttrAnno == null) {
                                    labelIdOfAttrAnno = -1L;
                                }
                                entityLabelMap.put(key, labelIdOfAttrAnno);
                            }
                        } else {
                            entityLabelMap.put(key, labelId);
                        }
                    }
                }
            }

            // 获取所有便签数据，用于鼠标悬浮title显示
            final Set<Long> labelIds = noteList.stream().filter(x -> x.getLabelId() != null && (AttrAnnoEnum.not_attr.getCode() == x.getIsAttr()))
                    .map(Entity::getLabelId).collect(Collectors.toSet());
            final Map<Long, List<AttributeLabel>> entityLabelInfoMap = attributeLabelService.findAllEnableMapByEntityLabelIdIn(labelIds);

            Map<String, Boolean> md5SameMap = null;
            if (showSameEntity && taskId != null) {
                Set<String> md5Set = new HashSet<>();
                for (Entity entity : noteList) {
                    // 实体标注
                    final boolean isEntityFlag = AttrAnnoEnum.not_attr.getCode() == entity.getIsAttr();
                    final boolean isSelfFlag = PreSourceEnum.SELF.getId() == entity.getSource();
                    if (taskId.equals(entity.getTaskId()) && isEntityFlag && isSelfFlag && !entity.isDeleted()) {
                        // 管理员审核时，多位标注员标注完全一致则添加标志
                        final String md5 = entity.getMd5();
                        if (md5 != null) {
                            md5Set.add(md5);
                        }
                    }
                }

                if (CollUtil.isNotEmpty(md5Set)) {
                    final Map<Long, Set<String>> otherTaskAndMd5Map = entityRepository.findOtherSameEntityMd5(noteId, md5Set);
                    if (CollUtil.isNotEmpty(otherTaskAndMd5Map)) {
                        // 对多个md5集合取交集
                        md5Set = findCommonElements(new ArrayList<>(otherTaskAndMd5Map.values()));
                        if (CollUtil.isNotEmpty(md5Set)) {
                            md5SameMap = attributesRepository.findEntityAndAttrMap(otherTaskAndMd5Map.keySet(), md5Set);
                        }
                    }
                }
            }


            for (Entity entity : noteList) {
                final boolean isEntityFlag = AttrAnnoEnum.not_attr.getCode() == entity.getIsAttr();
                final boolean isSelfFlag = PreSourceEnum.SELF.getId() == entity.getSource();

                final Long labelId = entity.getLabelId();

                List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();
                if (CollUtil.isEmpty(entityInfos)) {
                    continue;
                }
                final String id = entity.getId();
                final Integer isAttr = entity.getIsAttr();
                EntityAttrDTO attrDto = new EntityAttrDTO();
                EntityAttrDTO entityDto = new EntityAttrDTO();
                final boolean isAttrFlag = AttrAnnoEnum.is_attr.getCode() == isAttr;
                /*entityInfos.stream().map(x -> {
                    if (x.getContent().contains("院在全麻下")) {
                        System.out.println(x);
                    }
                    return x;
                }).count();*/
                if (isAttrFlag) {
                    attrDto = findAttrLabelInfoStr(id, isAttr, showAnnoOnly, annotatorId);
                } else {
                    if (showAnnoOnly) {
                        entityDto = annoCacheService.findDelAttrIds(id, entityLabelInfoMap.get(labelId), annotatorId);
                    } else {
                        entityDto = annoCacheService.findAttrIds(id, entityLabelInfoMap.get(labelId));
                    }
                }

                final int size = entityInfos.size();
                final String batchAnnotateId = entity.getBatchAnnotateId();
                for (int i = 0; i < size; i++) {
                    final AnnoDTO.EntityInfo entityInfo = entityInfos.get(i);
                    final String textId = entityInfo.getTextId();
                    if (StrUtil.isEmpty(textId)) {
                        continue;
                    }

                    List<AnnotationsDTO> annotationsDTOS = map.get(textId);
                    if (annotationsDTOS == null) {
                        annotationsDTOS = new ArrayList<>();
                    }

                    Long labelIdOfAttrAnno = null;
                    String attrIds = null;
                    String entityIds = null;
                    String attrLabelInfo = null;
                    String attrLabelUsedInfo = null;
                    if (isAttrFlag) {
                        if (i == 0) {
                            attrLabelInfo = attrDto.getAttrLabelInfo();
                        }
                        labelIdOfAttrAnno = attrDto.getLabelIdOfAttrAnno();

                        entityIds = attrDto.getEntityIds();
                    } else {
                        if (i == 0) {
                            attrLabelInfo = entityDto.getAttrLabelInfo();
                            attrLabelUsedInfo = entityDto.getAttrLabelUsedInfo();
                        }
                        attrIds = entityDto.getAttrIds();
                    }

                    if (CollUtil.isNotEmpty(entityLabelMap)) {
                        //预标注 额外处理
                        String key = genEntityStrMd5(entity.getEntityInfos(), labelId, entity.getIsAttr());
                        Long label = entityLabelMap.get(key);
                        if (label != null) {
                            labelIdOfAttrAnno = label;
                        }
                    }

                    final Integer questionLogo = entity.getDoubters() == null ? null : 1;

                    boolean showMultiSameEntity = false;
                    if (md5SameMap != null && isEntityFlag && isSelfFlag) {
                        showMultiSameEntity = Boolean.TRUE.equals(md5SameMap.get(entity.getMd5()));
                    }

                    // 获取实体标注包含的属性
                    annotationsDTOS.add(new AnnotationsDTO(entityInfo.getStart(), entityInfo.getEnd(), labelId, textId, entityInfo.getUniqueid(), id, entity.getAnnotate(),
                            isAttr, attrIds, entityIds, attrLabelInfo, labelIdOfAttrAnno,
                            entity.getSource(), size, batchAnnotateId, attrLabelUsedInfo, questionLogo, showMultiSameEntity));
                    map.put(textId, annotationsDTOS);
                }
            }
        }
        fillAnnotation(document, map);
        return document;
    }

    /**
     * 获取多个集合中的公共元素集合，求交集
     */
    public static <T> Set<T> findCommonElements(List<Set<T>> sets) {
        final int size = CollUtil.size(sets);
        if (size < 2) {
            // 集合数量若少于2，则无法求交集，返回一个空的集合
            return Collections.emptySet();
        }

        // 使用第一个集合作为基准集合
        Set<T> commonElements = new HashSet<>(sets.get(0));

        // 依次与后续集合求交集
        for (int i = 1; i < size; i++) {
            commonElements.retainAll(sets.get(i));
        }

        return commonElements;
    }

    /**
     * 查询m_document文档中所有段落id和content
     */
    @Override
    public Map<String, String> findIdAndContentMap(String documentId) {
        if (StrUtil.isBlank(documentId)) {
            throw new RRException("非法的documentId");
        }
        Document document = documentService.getDocumentById(documentId);
        return extractIdAndContentByDoc(document);
    }

    public static Map<String, String> extractIdAndContentByDoc(Document document) {
        // 返回map按照前端显示顺序：abstracts、body、figures、tables
        // 由于递归后最终结构为倒序，put顺序得反过来，最后将idToContentMap倒序排序
        final Map<String, String> idToContentMap = new LinkedHashMap<>();
        List<Document.TablesDTO> tables = document.getTables();
        if (CollUtil.isNotEmpty(tables)) {
            extractIdAndContent4Array((JSONArray) JSONArray.toJSON(tables), idToContentMap);
        }

        List<Document.FiguresDTO> figures = document.getFigures();
        if (CollUtil.isNotEmpty(figures)) {
            extractIdAndContent4Array((JSONArray) JSONArray.toJSON(figures), idToContentMap);
        }

        List<Document.BodyDTO> body = document.getBody();
        if (CollUtil.isNotEmpty(body)) {
            extractIdAndContent4Array((JSONArray) JSONArray.toJSON(body), idToContentMap);
        }

        Document.AbstractDTO abstracts = document.getAbstracts();
        if (abstracts != null) {
            extractIdAndContent((JSONObject) JSONObject.toJSON(abstracts), idToContentMap);
        }

        List<Map.Entry<String, String>> list = new ArrayList<>(idToContentMap.entrySet());
        Collections.reverse(list);
        // 创建一个新的 LinkedHashMap，将倒序排列的键值对放入其中
        Map<String, String> reversedMap = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : list) {
            reversedMap.put(entry.getKey(), entry.getValue());
        }
        return reversedMap;
    }

    private static void extractIdAndContent(JSONObject obj, Map<String, String> idToContentMap) {
        if (obj == null) {
            return;
        }
        for (Map.Entry<String, Object> entry : obj.entrySet()) {

            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof JSONObject) {

                extractIdAndContent((JSONObject) value, idToContentMap);

            } else if (value instanceof JSONArray) {

                extractIdAndContent4Array((JSONArray) value, idToContentMap);

            } else if (key.equals("id") && obj.containsKey("cls") && obj.containsKey("content")) {

                String id = obj.getString("id");
                if (StrUtil.isNotBlank(id)) {
                    idToContentMap.put(id, obj.getString("content"));
                }

            }
        }
    }

    private static void extractIdAndContent4Array(JSONArray array, Map<String, String> idToContentMap) {
        if (array == null) {
            return;
        }
        Collections.reverse(array);
        for (Object arrayElement : array) {
            if (arrayElement instanceof JSONObject) {
                extractIdAndContent((JSONObject) arrayElement, idToContentMap);
            } else if (arrayElement instanceof JSONArray) {
                extractIdAndContent4Array((JSONArray) arrayElement, idToContentMap);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void loadPreRelation(Long taskId, Long noteId, Long roleId, Long userId) {
        Date now = new Date();
        if (taskId == null || noteId == null || userId == null) {
            return;
        }
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        // 如果是如果不是标注员角色，或者已经到如果证明已经导入过直接return
        if (!roleId.equals(RoleEnum.annotator.getId()) || noteTask.getPreRelationImported().equals(PreRelationImportedEnum.finish.getValue())) {
            return;
        }
        noteTask.setPreRelationImported(PreRelationImportedEnum.finish.getValue());
        noteTaskDao.updateById(noteTask);

        try {
            List<Entity> entityList = new ArrayList<>();
            List<Attributes> attributesList = new ArrayList<>();
            List<Relationship> relationList = new ArrayList<>();

            List<Relationship> relationshipList = relationshipRepository.findPreAnnoRelation(noteId);
            // 提取出所有涉及实体id
            Set<String> entitySet = relationshipList.stream()
                    .flatMap(r -> r.getItems().stream())
                    .flatMap(item -> Stream.of(item.getSubject(), item.getObjects()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());

            // 加载所有实体的属性标注
            List<Attributes> attributes = attributesRepository.findAllEntityIdIn(entitySet);
            // 对属性标注根据entityId进行分组
            Map<String, List<Attributes>> entityAttrMap = attributes.stream()
                    .collect(Collectors.groupingBy(Attributes::getEntityId));
            //  提取划词属性
            List<String> attrEntityIds = attributes.stream().map(Attributes::getAttributeId).collect(Collectors.toList());
            // 查询所有的划词属性实体
            List<Entity> attrEntityList = entityRepository.findAllByIdInAndDeleted(attrEntityIds, false);
            Map<String, Entity> attrEntityMap = attrEntityList.stream().collect(Collectors.toMap(Entity::getId, x -> x));
            // 查询所有的关系实体
            Map<String, Entity> entityIdMap = entityRepository.findAllByIdInAndDeleted(entitySet, false).stream()
                    .collect(Collectors.toMap(Entity::getId, x -> x));

            for (Relationship relationship : relationshipList) {
                Relationship data = RelationMapper.INSTANCE.copyBean(relationship);
                // 重新设置一个id
                data.setId(IdUtil.simpleUUID());
                data.setTaskId(taskId);
                int preSource = PreSourceEnum.SELF.getId();
                data.setSource(preSource);
                data.setCreateTime(now);
                data.setUpdateTime(now);
                data.setImportLogId(null);
                data.setAnnotatorId(userId);

                List<Relationship.RelationItem> newItems = new ArrayList<>();
                for (Relationship.RelationItem item : relationship.getItems()) {
                    Relationship.RelationItem newItem = new Relationship.RelationItem();

                    newItem.setOrder(item.getOrder());
                    newItem.setNegation(item.getNegation());
                    newItem.setAnnotations(item.getAnnotations());
                    newItem.setRelation(item.getRelation());

                    List<String> subject = item.getSubject();
                    boolean allSubjectIsEntity = subject.stream().allMatch(s -> s.length() == 32);
                    if (allSubjectIsEntity) {
                        List<String> newSubject = createNewEntities(subject, entityIdMap, entityAttrMap, attrEntityMap, entityList, attributesList, taskId, userId, now);
                        newItem.setSubject(newSubject);
                    } else {
                        newItem.setSubject(item.getSubject());
                    }
                    List<String> objects = item.getObjects();
                    boolean allObjectsIsEntity = objects.stream().allMatch(s -> s.length() == 32);
                    if (allObjectsIsEntity) {
                        List<String> newObjects = createNewEntities(objects, entityIdMap, entityAttrMap, attrEntityMap, entityList, attributesList, taskId, userId, now);
                        newItem.setObjects(newObjects);
                    } else {
                        newItem.setObjects(item.getObjects());
                    }
                    newItems.add(newItem);


                }
                String md5 = RelationshipServiceImpl.genRelationStrMd5(newItems, data.getPatternId(), entityList);
                boolean exists = relationshipRepository.existsByTaskIdAndNoteIdAndAnnotatorIdAndAuditorIdAndSourceAndMd5AndDeleted(taskId, noteId, userId, null, preSource, md5, false);
                if (exists) {
                    continue;
                }
                data.setMd5(md5);
                data.setItems(newItems);
                relationList.add(data);
            }
            attributesRepository.saveAll(attributesList);
            entityRepository.saveAll(entityList);
            relationshipRepository.saveAll(relationList);
        } catch (Exception e) {
            // 将此noteTask的preRelationImported变为finish
            noteTask.setPreRelationImported(PreRelationImportedEnum.wait.getValue());
            noteTaskDao.updateById(noteTask);
            throw new RRException("关系预标注数据导入文章失败");
        }

    }

    @Override
    @CacheEvict(value = "existSource_cache", allEntries = true)
    public List<SourceVO> setMaster(PreImportDTO dto) {
        final Long taskId = dto.getTaskId();
        if (isInvalidLong(taskId)) {
            throw new RRException("taskId不存在");
        }
        final Long roleId = dto.getRoleId();
        if (isInvalidLong(roleId) || RoleEnum.auditor.getId() != roleId) {
            throw new RRException("当前角色无设置蓝本权限");
        }
        final NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("标注任务不存在");
        }
        setMasterTask(noteTask, true);
        return existSource(this.getById(noteTask.getNoteId()), roleId, dto.getAnnotatorId(), taskId);
    }

    /**
     * 实体清单树排序规则
     * 按照标注的段落id数字部分最小值，以及标注的起始位置最小值排序
     */
    public static Comparator<Entity> entityTreeComparator() {
        // 排序规则段落textId->起点值
        return ((e1, e2) -> {
            List<AnnoDTO.EntityInfo> entityInfoList1 = e1.getEntityInfos();
            List<AnnoDTO.EntityInfo> entityInfoList2 = e2.getEntityInfos();
            int start1 = getMinIntTextId(entityInfoList1);
            int start2 = getMinIntTextId(entityInfoList2);
            if (start1 < 0 || start2 < 0) {
                // 存在负数，则表示段落id中不包含数字
                String startStr1 = getMinStrTextId(entityInfoList1);
                String startStr2 = getMinStrTextId(entityInfoList2);
                if (!startStr1.equals(startStr2)) {
                    return startStr1.compareTo(startStr2);
                } else {
                    return getMinStart(entityInfoList1) - getMinStart(entityInfoList2);
                }
            } else {
                if (start1 != start2) {
                    return start1 - start2;
                } else {
                    return getMinStart(entityInfoList1) - getMinStart(entityInfoList2);
                }
            }
        });
    }

    /**
     * 取出一个实体（包含复合实体）中段落id中数值最小的值
     */
    private static int getMinIntTextId(List<AnnoDTO.EntityInfo> entityInfoList) {
        int min = Integer.MAX_VALUE;
        for (AnnoDTO.EntityInfo entityInfo : entityInfoList) {
            int i = getTextIdByStr(entityInfo.getTextId());
            if (i < min) {
                min = i;
            }
        }
        return min;
    }

    /**
     * 取出段落id（textId）不包含数字，按照字符串自然顺序排序的第一个
     */
    private static String getMinStrTextId(List<AnnoDTO.EntityInfo> entityInfoList) {
        return entityInfoList.stream().sorted(Comparator.comparing(AnnoDTO.EntityInfo::getTextId)).collect(Collectors.toList()).get(0).getTextId();
    }

    /**
     * 取出段落id（textId）中的所有数字，并拼接为一个整数
     */
    private static int getTextIdByStr(final String str) {
        try {
            Matcher matcher = PatternPool.NUMBERS.matcher(str);
            StringBuilder sb = new StringBuilder();
            while (matcher.find()) {
                sb.append(matcher.group());
            }
            return Integer.parseInt(sb.toString());
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 取出一个实体（包含复合实体）中起始位置中数值最小的值
     */
    private static int getMinStart(List<AnnoDTO.EntityInfo> entityInfoList) {
        int minStart = Integer.MAX_VALUE;
        for (AnnoDTO.EntityInfo entityInfo : entityInfoList) {
            if (entityInfo.getStart() < minStart) {
                minStart = entityInfo.getStart();
            }
        }
        return minStart;
    }

    @Override
    public List<EntityListTreeVO> getEntityListTree(final Long taskId, final Integer source) {
        final List<EntityListTreeVO> treeLevel1 = new ArrayList<>();
        if (source == null || isInvalidLong(taskId)) {
            return treeLevel1;
        }
        final PreSourceEnum sourceEnum = findBySource(source);
        //entityTreeCreateTimeComparator()
        final List<Entity> entityList = entityRepository.findByTaskIdAndSource(taskId, sourceEnum).stream()
                .sorted(entityTreeComparator()).collect(Collectors.toList());
        //一级菜单数据（实体标注）
        if (CollUtil.isNotEmpty(entityList)) {
            //所有实体标注对应的标签id集合
            final List<Long> labelIds = new ArrayList<>();
            //所有属性标注id集合
            final List<String> attrAnnoIds = new ArrayList<>();
            //所有属性标注集合
            final List<Entity> attrAnnoList = new ArrayList<>();

            for (Entity entity : entityList) {
                final Integer isAttr = entity.getIsAttr();
                final String id = entity.getId();
                if (AttrAnnoEnum.is_attr.getCode() == isAttr) {
                    //属性标注
                    attrAnnoIds.add(id);
                    attrAnnoList.add(entity);
                } else {
                    //实体标注
                    final Long labelId = entity.getLabelId();
                    if (labelId == null) {
                        throw new RRException("实体标注数据异常");
                    }
                    labelIds.add(labelId);

                    // 构建树结构一级节点
                    EntityListTreeVO vo = new EntityListTreeVO(id, null, getAllContent(entity.getEntityInfos()), labelId);
                    treeLevel1.add(vo);
                }
            }
            if (CollUtil.isNotEmpty(treeLevel1)) {
                // 处理二级菜单数据（实体标注）
                final Map<String, EntityListTreeVO> treeLevel2 = new LinkedHashMap<>();
                final Map<String, LinkedHashSet<String>> entityAttrRelation = new HashMap<>();
                if (CollUtil.isNotEmpty(attrAnnoList)) {
                    final Map<String, LinkedHashSet<AttrAnnoLabelDTO>> attrAnnoLabelMap = attributesRepository.findAttrAnnoLabelByAttributeIds(attrAnnoIds);
                    for (LinkedHashSet<AttrAnnoLabelDTO> dtos : attrAnnoLabelMap.values()) {
                        for (AttrAnnoLabelDTO dto : dtos) {
                            String entityId = dto.getEntityId();
                            LinkedHashSet<String> set = entityAttrRelation.get(entityId);
                            if (set == null) {
                                set = new LinkedHashSet<>();
                            }
                            set.add(dto.getAttributeId());
                            entityAttrRelation.put(entityId, set);
                        }

                    }
                    for (Entity entity : attrAnnoList) {
                        final String id = entity.getId();
                        LinkedHashSet<AttrAnnoLabelDTO> dtos = attrAnnoLabelMap.get(id);
                        if (CollUtil.isEmpty(dtos)) {
                            continue;
                        }
                        for (AttrAnnoLabelDTO dto : dtos) {
                            if (id.equals(dto.getAttributeId())) {
                                final String entityId = dto.getEntityId();
                                final EntityListTreeVO vo = new EntityListTreeVO(id, dto.getAttrLabelName(), getAllContent(entity.getEntityInfos()));
                                treeLevel2.put(initLevel2Key(entityId, id), vo);
                            }
                        }
                    }
                }
                //获取所有实体标注的标签id和对应名称
                Map<Long, String> entityLabelMap = entityLabelService.list(Wrappers.<EntityLabel>lambdaQuery().in(EntityLabel::getId, labelIds))
                        .stream().collect(Collectors.toMap(EntityLabel::getId, EntityLabel::getName));
                for (EntityListTreeVO vo : treeLevel1) {
                    final Long labelId = vo.getLabelId();
                    //补充标签名称
                    vo.setLabel(entityLabelMap.get(labelId));
                    final String id = vo.getId();
                    //补充二级节点
                    LinkedHashSet<String> attributeIds = entityAttrRelation.get(id);
                    if (CollUtil.isNotEmpty(attributeIds)) {
                        final List<EntityListTreeVO> children = new ArrayList<>();
                        for (String attributeId : attributeIds) {
                            EntityListTreeVO child = treeLevel2.get(initLevel2Key(id, attributeId));
                            if (child != null) {
                                children.add(child);
                            }
                        }
                        vo.setChildren(CollUtil.isEmpty(children) ? null : children);
                    }
                }
            }
        }
        return treeLevel1;
    }

    private String initLevel2Key(final String entityId, final String attributeId) {
        return entityId + "_" + attributeId;
    }

    private List<String> getAllContent(List<AnnoDTO.EntityInfo> entityInfos) {
        if (CollUtil.isEmpty(entityInfos)) {
            return new ArrayList<>();
        }
        return entityInfos.stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList());
    }

    private List<String> createNewEntities(List<String> objects, Map<String, Entity> entityIdMap, Map<String, List<Attributes>> entityAttrMap, Map<String, Entity> attrEntityMap, List<Entity> entityList, List<Attributes> attributesList, Long taskId, Long userId, Date now) {
        List<String> newObjects = new ArrayList<>();
        for (String x : objects) {
            // 把每一个实体重新导入
            String entityId = IdUtil.simpleUUID();
            Entity entity = EntityMapper.INSTANCE.copyBean(entityIdMap.get(x));
            entity.setId(entityId);
            entity.setTaskId(taskId);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setSource(PreSourceEnum.SELF.getId());
            entity.setImportLogId(null);
            entity.setAnnotatorId(userId);
            List<AnnoDTO.EntityInfo> collect = entity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
            entity.setEntityInfos(collect);
            // md5与原来的一致，无需重新生成
            // String md5 = NoteServiceImpl.genEntityStrMd5(collect, entity.getLabelId(), AttrAnnoEnum.not_attr.getCode());
            // entity.setMd5(md5);

            // 找到这个实体下面的属性
            if (entityAttrMap.containsKey(x)) {
                List<Attributes> list = entityAttrMap.get(x);
                // 构建新的属性
                for (Attributes attr : list) {
                    Attributes newAttr = AttributeEntityMapper.INSTANCE.copyBean(attr);
                    newAttr.setId(IdUtil.simpleUUID());
                    newAttr.setEntityId(entityId);
                    newAttr.setTaskId(taskId);
                    newAttr.setAnnotatorId(userId);
                    newAttr.setCreateTime(now);
                    newAttr.setUpdateTime(now);
                    // newAttr.setEntityMd5(md5);
                    if (attr.getAttributeId() != null) {
                        Entity attrEntity = attrEntityMap.get(attr.getAttributeId());
                        Entity newAttrEntity = EntityMapper.INSTANCE.copyBean(attrEntity);
                        String newAttrEntityId = IdUtil.simpleUUID();

                        newAttrEntity.setId(newAttrEntityId);
                        newAttrEntity.setCreateTime(now);
                        newAttrEntity.setUpdateTime(now);
                        newAttrEntity.setSource(PreSourceEnum.SELF.getId());
                        newAttrEntity.setImportLogId(null);
                        newAttrEntity.setTaskId(taskId);
                        newAttrEntity.setAnnotatorId(userId);
                        List<AnnoDTO.EntityInfo> newAttrEntityInfo = attrEntity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                        newAttrEntity.setEntityInfos(newAttrEntityInfo);

                        // md5与原来的一致，无需重新生成
                        // String newAttrEntityMd5 = NoteServiceImpl.genEntityStrMd5(newAttrEntityInfo, null, AttrAnnoEnum.is_attr.getCode());
                        // newAttrEntity.setMd5(newAttrEntityMd5);
                        // newAttr.setAttrMd5(newAttrEntityMd5);

                        newAttr.setAttributeId(newAttrEntityId);
                        entityList.add(newAttrEntity);
                    }
                    attributesList.add(newAttr);
                }
            }

            // 添加新的实体
            entityList.add(entity);
            newObjects.add(entityId);
        }
        return newObjects;
    }

    private PreSourceEnum findBySource(Integer source) {
        return PreSourceEnum.findById(source).orElseThrow((() -> new RRException("非法的标注来源")));
    }

    private EntityAttrDTO findAttrLabelInfoStr(String attributeId, Integer isAttr, final boolean showAnnoOnly, final Long annotatorId) {
        return findAttrLabelInfoStr(attributeId, isAttr, true, showAnnoOnly, annotatorId);
    }

    private EntityAttrDTO findAttrLabelInfoStr(String attributeId, Integer isAttr, final boolean queryLabel, final boolean showAnnoOnly, final Long annotatorId) {
        final EntityAttrDTO dto = new EntityAttrDTO();
        if (AttrAnnoEnum.not_attr.getCode() == isAttr) {
            // 实体标注不查询
            return dto;
        }
        // 属性标注对应的实体标注的标签id
        Long labelIdOfAttrAnno = null;
        List<Attributes> attributesList;
        if (showAnnoOnly) {
            attributesList = annoCacheService.findDelByAttributeId(attributeId, annotatorId);
        } else {
            attributesList = annoCacheService.findByAttributeId(attributeId);
        }

        final Set<String> entityIds = new HashSet<>();
        if (CollUtil.isNotEmpty(attributesList)) {
            Optional<Entity> optionalEntity;
            for (final Attributes attribute : attributesList) {
                final String entityId = attribute.getEntityId();
                if (StrUtil.isBlank(entityId)) {
                    continue;
                }
                entityIds.add(entityId);

                if (labelIdOfAttrAnno == null) {
                    if (showAnnoOnly) {
                        optionalEntity = entityRepository.findDelById(entityId, annotatorId);
                    } else {
                        optionalEntity = annoCacheService.findEntityByIdCus(entityId);
                    }

                    if (optionalEntity.isPresent()) {
                        labelIdOfAttrAnno = optionalEntity.get().getLabelId();
                    }
                }
            }

            if (queryLabel) {
                // 包含自定义标签
                // 已分配属性名称拼接字符串
                annoCacheService.findAttrLabelInfo(attributesList.stream().map(Attributes::getAttrLabelId).distinct().collect(Collectors.toList()), dto, null);
            }

        }
        dto.setLabelIdOfAttrAnno(labelIdOfAttrAnno);
        dto.setEntityIds(entityIds.stream().collect(NoteServiceImpl.attrJoin()));
        return dto;
    }

    /**
     * 属性标注id连接器
     */
    public static Collector<CharSequence, ?, String> attrJoin() {
        return Collectors.joining(",");
    }

    /**
     * 添加实体标注
     */
    @Override
    public AddEntityVO addEntity(final AnnoDTO annoDTO, final Long userId) {
        return addEntity(annoDTO, userId, false);
    }

    private AddEntityVO addEntity(final AnnoDTO annoDTO, final Long userId, final boolean importFromPre) {
        // 补丁：防止前端传入的参数错误
        NoteTask noteTask = noteTaskDao.selectById(annoDTO.getTaskId());
        annoDTO.setProjectId(noteTask.getProjectId());
        annoDTO.setBatchId(noteTask.getBatchId());
        annoDTO.setNoteId(noteTask.getNoteId());

        // 将新增的记录存放到MongoDB m_entity中
        final Integer isAttr = annoDTO.getIsAttr();
        if (isAttr != null && isAttr > 0) {
            //属性标注
            annoDTO.setLabelId(null);
            annoDTO.setIsAttr(AttrAnnoEnum.is_attr.getCode());
        } else {
            if (annoDTO.getLabelId() == null) {
                throw new RRException("请先选择标签");
            }
            annoDTO.setIsAttr(AttrAnnoEnum.not_attr.getCode());
        }
        Entity entity = EntityMapper.INSTANCE.annoDTOToEntity(annoDTO);
        final Long labelId = entity.getLabelId();
        //去重、不排序, 并生成md5值
        List<AnnoDTO.EntityInfo> entityInfos = annoDTO.getEntityInfos().stream().peek(x -> x.setUniqueid(null)).distinct().collect(Collectors.toList());
        final String md5 = genEntityStrMd5(entityInfos, labelId, entity.getIsAttr());
        //根据生成的md5值，判断是否存在重复标注
        final Long noteTaskId = entity.getTaskId();
        final PreSourceEnum sourceEnum = PreSourceEnum.SELF;

        initEntity(entity, entityInfos, sourceEnum, md5, annoDTO.getRoleId(), userId, new Date());
        // 批量标注
        final Boolean multiple = annoDTO.getMultiple();
        if (multiple != null && multiple) {
            return batchAddEntity(entity, sourceEnum, annoDTO.getRoleId(), userId, importFromPre);
        }

        final int source = sourceEnum.getId();
        final List<Entity> existEntityList = entityRepository.findByTaskIdAndMd5AndSource(noteTaskId, md5, source);
        final AddEntityVO vo = new AddEntityVO();
        Entity restoreEntity = null;
        if (CollUtil.isNotEmpty(existEntityList)) {
            for (Entity entityExist : existEntityList) {
                if (!entityExist.isDeleted()) {
                    // 未删除
                    if (importFromPre) {
                        //预标注导入时，重复则不导入
                        vo.setExistEntity(entityExist);
                        return vo;
                    } else {
                        throw new RRException("该标注已存在");
                    }

                } else {
                    // 已删除
                    if (entityExist.getAnnotatorId() != null && entityExist.getAuditorId() != null) {
                        // 审核员误删恢复
                        restoreEntity = entityExist;
                    }
                }
            }
        }

        OptionEnum opt;
        if (restoreEntity != null) {
            // 审核员误删恢复
            restoreEntity.setDeleted(DeleteEnum.not_delete.getVal());
            restoreEntity.setAuditorId(null);
            entity = restoreEntity;
            opt = OptionEnum.update;
        } else {
            opt = OptionEnum.add;
        }

        // 单个标注
        entityRepository.save(entity);
        // 记录新增的操作日志
        commonService.addLog(entity.getNoteId(), entity.getTaskId(), opt.getCode(), getAddEntityMsg(entity), userId);

        return genFinalResult(vo, entity, importFromPre);
    }

    private AddEntityVO genFinalResult(AddEntityVO vo, final Entity entity, final boolean importFromPre) {
        if (!importFromPre) {
            // map.put("savedEntityId", entity.getId());
            vo.setUniIds(entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getUniqueid).collect(Collectors.toList()));
            vo.setDocAllInfo(initNewDoc(entity.getNoteId(), entity.getTaskId()));
        } else {
            vo.setExistEntity(entity);
        }
        return vo;
    }

    private void initEntity(final Entity entity, List<AnnoDTO.EntityInfo> entityInfos, final PreSourceEnum sourceEnum, final String md5,
                            final Long roleId, final Long userId, final Date now) {
        // 补充全局唯一id
        entityInfos = entityInfos.stream()
                .peek(x -> x.setUniqueid(IdUtils.id())).collect(Collectors.toList());
        entity.setEntityInfos(entityInfos);

        entity.setId(IdUtils.id());
        setUserId(entity, userId, roleId);

        entity.setSource(sourceEnum.getId());
        // 将标注信息
        entity.setMd5(md5);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
    }

    private static List<AnnotateDTO> getAnnotation(Object obj, String text) {
        text = StrUtil.trimToNull(text);
        if (StrUtil.isBlank(text)) {
            return new ArrayList<>();
        }
        List<AnnotateDTO> annotateList = new ArrayList<>();
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不是标注文本
                if (BATCH_IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (CONTENT.equals(name)) {
                    String textId = (String) ReflectUtil.getFieldValue(currentObj, "id");
                    List<AnnotateDTO> annotates = createAnnotate(textId, text, value.toString());
                    if (CollUtil.isNotEmpty(annotates)) {
                        annotateList.addAll(annotates);
                    }
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
        return annotateList;
    }

    private static List<AnnotateDTO> createAnnotate(String textId, String text, String content) {
        List<AnnotateDTO> annotateList = new ArrayList<>();

        //  定义该变量用于记录匹配"love"的元素前面的长度
        int frontLength = 0;

        int textLength = text.length();

        //只要该str字符串中有匹配"love"的元素，才进行以下操作
        while (content.contains(text)) {

            //定义该变量用于记录匹配"love"的元素在当前字符串的位置
            int index = content.indexOf(text);

            //匹配"love"的元素位置等于frontLength加上index；加1为了从1开始计数，更加直观：
            int start = index + frontLength;
            int end = index + frontLength + textLength;
            AnnotateDTO annotate = new AnnotateDTO();
            annotate.setTextId(textId);
            annotate.setContent(text);
            annotate.setStart(start);
            annotate.setEnd(end);
            annotateList.add(annotate);

            frontLength += (index + textLength);
            //将字符串中匹配"love"元素的前面部分及其本身截取，留下后面的部分
            content = content.substring(index + textLength);
        }
        return annotateList;
    }

    private AddEntityVO batchAddEntity(final Entity entity, final PreSourceEnum sourceEnum, final Long roleId, final Long userId, final boolean importFromPre) {
        final List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();
        if (entityInfos.size() > 1) {
            throw new RRException("复合标注不能进行批量操作");
        }

        final Long taskId = entity.getTaskId();
        final Map<String, Entity> md5Map = entityRepository.findMd5ByTaskIdAndSource(taskId, sourceEnum);
        final Note note = this.getById(entity.getNoteId());
        final Document document = documentService.getDocumentById(note.getDocumentId());
        final List<AnnotateDTO> annotateDTOList = getAnnotation(document, entityInfos.get(0).getContent());
        final Long labelId = entity.getLabelId();
        if (CollUtil.isNotEmpty(annotateDTOList)) {
            final List<Entity> addData = new ArrayList<>();
            final Date date = new Date();
            final String batchAnnotateId = IdUtils.id();
            final String inputMd5 = entity.getMd5();
            for (AnnotateDTO annotateDTO : annotateDTOList) {
                final List<AnnoDTO.EntityInfo> batchEntityInfo = CollUtil.newArrayList(EntityMapper.INSTANCE.annoDtoToEntityInfo(annotateDTO));
                final String md5 = genEntityStrMd5(batchEntityInfo, labelId, entity.getIsAttr());
                final Entity entityExist = md5Map.get(md5);
                if (entityExist != null && !entityExist.isDeleted()) {
                    continue;
                }

                Entity newEntity;
                if (entityExist != null && entityExist.isDeleted() && entityExist.getAnnotatorId() != null && entityExist.getAuditorId() != null) {
                    // 审核员误删除的数据恢复
                    newEntity = entityExist;
                    newEntity.setDeleted(DeleteEnum.not_delete.getVal());
                    newEntity.setAuditorId(null);
                } else {
                    newEntity = EntityMapper.INSTANCE.copyBean(entity);
                    if (!md5.equals(inputMd5)) {
                        //输入标注已经初始化
                        initEntity(newEntity, batchEntityInfo, sourceEnum, md5, roleId, userId, date);
                    }
                    newEntity.setBatchAnnotateId(batchAnnotateId);
                }
                md5Map.put(md5, new Entity());
                // 批量标注批次id, 用于批量删除
                addData.add(newEntity);
            }
            if (CollUtil.isNotEmpty(addData)) {
//                entityRepository.insert(addData);
                entityRepository.saveAll(addData);
                // 记录日志
                for (Entity e : addData) {
                    commonService.addLog(e.getNoteId(), e.getTaskId(), OptionEnum.add.getCode(), getAddEntityMsg(e), userId);
                }
            }
        }

        return genFinalResult(new AddEntityVO(), entity, importFromPre);
    }

    public static Comparator<AnnoDTO.EntityInfo> sortComparator() {
        // 排序规则段落textId->起点值textId->终点值end
        return ((o1, o2) -> {
            final String textId1 = o1.getTextId();
            final String textId2 = o2.getTextId();
            if (!textId1.equals(textId2)) {
                return textId1.compareTo(textId2);
            } else {
                final Integer start1 = o1.getStart();
                final Integer start2 = o2.getStart();
                if (!start1.equals(start2)) {
                    return start1.compareTo(start2);
                } else {
                    final Integer end1 = o1.getEnd();
                    final Integer end2 = o2.getEnd();
                    return end1.compareTo(end2);
                }
            }
        });
    }

    /**
     * 生成实体信息md5值
     */
    public static String genEntityStrMd5(final List<AnnoDTO.EntityInfo> entityInfos, final Long labelId, Integer isAttr) {
        // 排序后，转化为json字符串，并生成md5值
        final String builder = JSON.toJSONString(CollUtil.sort(new ArrayList<>(entityInfos), sortComparator()), AnnoDTO.ENTITY_JSON_FILTER) +
                "labelId" + labelId +
                "isAttr" + isAttr;
//                "deleted" + deleted +
//                "source" + source;
        return SecureUtil.md5(builder);
    }

    private static void setUserId(Entity entity, final Long userId, final Long roleId) {
        final Optional<RoleEnum> roleEnumOpt = RoleEnum.findById(roleId);
        if (roleEnumOpt.isPresent()) {
            RoleEnum role = roleEnumOpt.get();
            switch (role) {
                case annotator:
                    entity.setAnnotatorId(userId);
                    break;
                case auditor:
                    entity.setAuditorId(userId);
                    break;
                default:
                    throw new RRException("该用户角色没有标注权限");
            }
        } else {
            throw new RRException("角色信息错误");
        }
    }

    /**
     * 新增标注的日志信息
     */
    @Override
    public String getAddEntityMsg(Entity entity) {
        if (entity.getIsAttr() == AttrAnnoEnum.not_attr.getCode()) {
            Long labelId = entity.getLabelId();
            EntityLabel entityLabel = entityLabelService.getById(labelId);
            return "标注文本: " + getEntityAllContent(entity) + ", 标签: "
                    + entityLabel.getName() + " (ID:" + entity.getId() + ")";
        } else {
            return "属性文本: " + getEntityAllContent(entity) + ", " + " (ID:" + entity.getId() + ")";
        }
    }

    /**
     * 更新实体标注批注
     */
    @Override
    public Document updateEntityAnnotate(AnnoDTO annoDTO, Long userId) {
        final Entity entity = entityRepository.findByUniqueid(annoDTO.getEntityId()).orElseThrow(() -> new RRException("未查询到该实体"));
        updateAnnotate(entity, new Date(), annoDTO.getAnnotate(), userId, annoDTO.getRoleId(), annoDTO.getQuestionFlag());
        return initNewDoc(entity.getNoteId(), entity.getTaskId());
    }

    private void updateAnnotate(final Entity entity, final Date date, String annotate, Long userId, Long roleId) {
        updateAnnotate(entity, date, annotate, userId, roleId, false);
    }

    private void updateAnnotate(final Entity entity, final Date date, String annotate, Long userId, Long roleId, boolean questionFlag) {
        entity.setAnnotate(StrUtil.trimToNull(annotate));
//        setUserId(entity, userId, roleId);
        entity.setUpdateTime(date);
        if (questionFlag) {
            // 质疑人
            entity.setDoubters(userId);
        }
        entityRepository.save(entity);
        annoCacheService.removeEntityCache(entity.getId());

        if (questionFlag) {
            // 更新t_note_task质疑实体数量
            updateTaskQueries(entity.getTaskId());
        }
    }

    private void updateTaskQueries(final Long taskId) {
        if (taskId != null) {
            final NoteTask noteTask = noteTaskDao.selectById(taskId);
            if (noteTask != null) {
                noteTask.setQueries(entityRepository.countQueryByTaskId(taskId));
                noteTask.setUpdateTime(new Date());
                noteTaskDao.updateById(noteTask);
            }
        }
    }

    /**
     * 批量更新实体标注批注
     */
    @Override
    public Document updateMultipleEntityAnnotate(AnnoDTO annoDTO, Long userId) {
        final Entity entity = entityRepository.findByUniqueid(annoDTO.getEntityId()).orElseThrow(() -> new RRException("未查询到该实体"));

        String batchAnnotateId = entity.getBatchAnnotateId();

        final Date date = new Date();
        // 如果不是批量标注的，则采用非批量删除方式删掉
        if (StrUtil.isBlank(batchAnnotateId)) {
            updateAnnotate(entity, date, annoDTO.getAnnotate(), userId, annoDTO.getRoleId());
        } else {
            List<Entity> entityList = getBatchEntities(batchAnnotateId);
            for (Entity oldEntity : entityList) {
                updateAnnotate(oldEntity, date, annoDTO.getAnnotate(), userId, annoDTO.getRoleId());
            }
        }
        return initNewDoc(entity.getNoteId(), entity.getTaskId());
    }

    /**
     * 更新实体标注（主要是更新umls标签）
     */
    @Override
    public void updateEntityUmls(final AnnoDTO annoDTO, final String uniqueid, final Long userId) {
        final String conceptId = annoDTO.getConceptId();
        final String conceptText = annoDTO.getConceptText();
        final Long roleId = annoDTO.getRoleId();
        Optional<Entity> entityOptional = entityRepository.findByUniqueid(uniqueid);
        if (!entityOptional.isPresent()) {
            log.error("未查询到该实体{}", uniqueid);
            throw new RRException("未查询到该实体: " + uniqueid);
        }
        Entity entity = entityOptional.get();
        entity.setConceptType(annoDTO.getConceptType());
        entity.setConceptId(StrUtil.trimToNull(conceptId));
        entity.setConceptText(StrUtil.trimToNull(conceptText));
        setUserId(entity, userId, roleId);
        entity.setUpdateTime(new Date());
        entityRepository.save(entity);

        // 记录修改UMLS的日志
        String msg = getUmlsLogMsg(entity);
        commonService.addLog(entity.getNoteId(), entity.getTaskId(), OptionEnum.update.getCode(), msg, userId);
    }

    /**
     * 更新实体标注（更新umls标签）
     */
    @Override
    public void updateMultipleEntityUmls(AnnoDTO annoDTO, Long userId) {
        String entityId = annoDTO.getEntityId();
        Optional<Entity> entityOptional = entityRepository.findByUniqueid(entityId);
        if (!entityOptional.isPresent()) {
            log.error("未查询到该实体{}", entityId);
            throw new RRException("未查询到该实体: " + entityId);
        }
        Entity entity = entityOptional.get();

        String batchAnnotateId = entity.getBatchAnnotateId();
        // 如果不是批量标注的，则采用非批量删除方式删掉
        if (StrUtil.isBlank(batchAnnotateId)) {
            updateEntityUmls(annoDTO, entity.getEntityInfos().get(0).getUniqueid(), userId);
            return;
        }

        List<Entity> entityList = getBatchEntities(batchAnnotateId);

        for (Entity oldEntity : entityList) {
            updateEntityUmls(annoDTO, oldEntity.getEntityInfos().get(0).getUniqueid(), userId);
        }
    }

    private String getEntityAllContent(Entity entity) {
        return "[" + entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining("， ")) + "]";
    }

    private String getUmlsLogMsg(Entity entity) {
        return "标注文本: " + getEntityAllContent(entity) + ", UMLS: "
                + StrUtil.nullToEmpty(entity.getConceptId())
                + " (ID:" + entity.getId() + ")";
    }

    /**
     * 查询某个实体标注（用户切换标签回显）
     */
    @Override
    public Entity getEntity(final String id, final String source) {
        if (StrUtil.isBlank(id)) {
            throw new RRException("id不能为空");
        }
        Optional<Entity> optional = entityRepository.findByUniqueid(id);
        Entity entity = null;
        if (optional.isPresent()) {
            entity = optional.get();
            entity.setLabel(entityLabelService.getById(entity.getLabelId()));

            AnnoDTO.EntityInfo currEntity = null;
            for (AnnoDTO.EntityInfo entityInfo : entity.getEntityInfos()) {
                if (entityInfo.getUniqueid().equals(id)) {
                    currEntity = entityInfo;
                    break;
                }
            }
            final List<AnnoDTO.EntityInfo> sameEntity = entityRepository.findSameEntity(entity, currEntity);
            entity.setAllEntityInfos(sameEntity);
        }
        return entity;
    }

    /**
     * 判断当前文章存在哪些预标注数据
     */
    @Override
    @Cacheable(cacheNames = "existSource_cache")
    public List<SourceVO> existSource(final Note note, final Long roleId, final Long annotatorId, final Long taskId) {
        if (note.getProjectId() == null) {
            return null;
        }
        /*Project project = projectService.getById(note.getProjectId());
        String sourcesArgs = project.getPreSources();
        final List<Integer> sourceIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(sourcesArgs)) {
            final List<String> sourceList = CollUtil.newArrayList(sourcesArgs.split(","));
            final Map<String, PreSourceEnum> allInMap = PreSourceEnum.findAllInMap();
            for (String s : sourceList) {
                final PreSourceEnum sourceEnum = allInMap.get(s);
                if (sourceEnum == null) {
                    throw new RRException("未知的预标注类型");
                }
                sourceIdList.add(sourceEnum.getId());
            }
        }*/

        final List<SourceVO> sourceVoList = new ArrayList<>();
        final PreSourceEnum self = PreSourceEnum.SELF;

        final boolean isReviewed = Objects.equals(NoteStepEnum.reviewed.getCode(), note.getStep());
        final boolean isAnnotator = Objects.equals(RoleEnum.annotator.getId(), roleId);
        if (isAnnotator && !isReviewed) {
            //标注员，且标注状态不是"已合格"
            sourceVoList.add(new SourceVO(self.getTitle(), self.getId()));
        } else {
            //请求参数是否包含标注员
            final boolean hasAnno = !isInvalidLong(annotatorId);
            //当前角色是否为审核员
            final boolean isAuditor = RoleEnum.auditor.getId() == roleId;
            if (isAuditor && !hasAnno) {
                // 审核员
                throw new RRException("审核时，必须选择标注员");
            }

            final List<NoteTask> noteTasks = noteTaskDao.selectList(Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId()));
            if (CollUtil.isEmpty(noteTasks)) {
                //不存在task的情况
                sourceVoList.add(new SourceVO(self.getTitle(), self.getId()));
            } else {
                final int masterValue = MasterEnum.master.getValue();
                final long masterCount = noteTasks.stream().filter(x -> x.getMaster() == masterValue).count();
                SourceVO firstUserVo = null;
                final List<SourceVO> userList = new ArrayList<>();
                for (NoteTask noteTask : noteTasks) {
                    final Long itemTaskId = noteTask.getTaskId();
                    if (isAuditor && masterCount == 0 && itemTaskId.equals(taskId)) {
                        // 审核员默认将第一次打开的标注任务设置为蓝本任务
                        setMasterTask(noteTask, false);
                    }
                    final Long annotator = noteTask.getAnnotator();
                    final String annotatorName = sysUserService.findNameById(annotator);
                    final Integer master = noteTask.getMaster();
                    if (StrUtil.isNotBlank(annotatorName)) {
                        if (master == masterValue) {
                            firstUserVo = new SourceVO(annotatorName, self.getId(), itemTaskId, annotator, master, noteTask.getInvalid());
                        } else {
                            userList.add(new SourceVO(annotatorName, self.getId(), itemTaskId, annotator, master, noteTask.getInvalid()));
                        }
                    }
                }
                if (firstUserVo != null) {
                    sourceVoList.add(firstUserVo);
                }
                if (CollUtil.isNotEmpty(userList)) {
                    sourceVoList.addAll(userList);
                }
            }
        }

        // 预标注来源
        final List<Long> importLogIds = entityRepository.findAllPreSourceByArticleId(note.getArticleId(), note.getProjectId());
        if (CollUtil.isNotEmpty(importLogIds)) {
            final LambdaQueryWrapper<ImportLog> importLogQuery = Wrappers.lambdaQuery(ImportLog.class);
            importLogQuery.in(ImportLog::getId, importLogIds);
            importLogQuery.in(ImportLog::getType, ImportTypeEnum.allPreTypeIds());
            importLogQuery.eq(ImportLog::getEnabled, StatusEnum.enable.getValue());
            importLogQuery.orderByDesc(ImportLog::getCreateTime);
            final List<ImportLog> importLogs = importLogService.list(importLogQuery);

            final PreSourceEnum customize = PreSourceEnum.CUSTOMIZE;
            if (CollUtil.isNotEmpty(importLogs)) {
                for (ImportLog importLog : importLogs) {
                    final SourceVO sourceVO = new SourceVO(FileUtil.mainName(importLog.getName()), customize.getId());
                    sourceVO.setImportLogId(importLog.getId());
                    sourceVoList.add(sourceVO);
                }
            }

            /*for (Integer id : source) {
                if (id.equals(customize.getId())) {
                    sourceVoList.add(new SourceVO(customize.getTitle(), customize.getId()));
                }
                if (CollUtil.contains(sourceIdList, id)) {
                    Optional<PreSourceEnum> optional = PreSourceEnum.findById(id);
                    if (optional.isPresent()) {
                        PreSourceEnum preSourceEnum = optional.get();
                        sourceVoList.add(new SourceVO(preSourceEnum.getTitle(), id));
                    } else {
                        // sourceVoList.add(new SourceVO("s_" + id, id));
                        throw new RRException("数据来源未知");
                    }
                }
            }*/
        }

        //补充多用户标注任务的用户信息
        return sourceVoList.stream().distinct().collect(Collectors.toList());
    }

    //@Transactional(rollbackFor = Exception.class)
    public void setMasterTask(NoteTask noteTask, final boolean resetOthers) {
        final Date now = new Date();
        final int masterValue = MasterEnum.master.getValue();
        noteTask.setMaster(masterValue);
        if (resetOthers) {
            final List<NoteTask> noteTasks = noteTaskDao.selectList(Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, noteTask.getNoteId()));
            final Long taskId = noteTask.getTaskId();
            for (NoteTask task : noteTasks) {
                task.setMaster(task.getTaskId().equals(taskId) ? masterValue : MasterEnum.not_master.getValue());
                task.setUpdateTime(now);
                noteTaskDao.updateById(task);
            }
        } else {
            noteTask.setUpdateTime(now);
            noteTaskDao.updateById(noteTask);
        }
    }

    @Override
    public Map<String, NoteDTO> findAIdAndDIdByProjectId(Long projectId) {
        List<Note> list = this.baseMapper.findAIdAndDIdByProjectId(projectId);
        List<NoteDTO> noteDTOS = new ArrayList<>();
        list.stream().distinct().forEach(note -> {
            NoteDTO noteDTO = new NoteDTO();
            noteDTO.setArticleId(note.getArticleId());
            noteDTO.setBatchId(note.getBatchId());
            noteDTO.setDocumentId(note.getDocumentId());
            noteDTO.setNoteId(note.getNoteId());
            noteDTOS.add(noteDTO);
        });
        Map<String, NoteDTO> result = new HashMap<>(16);
        noteDTOS.stream().distinct().forEach(x -> {
            result.put(x.getArticleId(), x);
        });
        return result;
    }

    /**
     * 获取UMLS Concept数据
     */
    @Override
    public List<MetaMapDTO> getUmls(String conceptName, String conceptId, Long projectId) {
        List<MetaMapDTO> metaMapDTOS = new ArrayList<>();
        String result = null;
        Set<String> map = new HashSet<>();
        if (StrUtil.isBlank(conceptName)) {
            return null;
        }

        //先查询本地是否配置了消岐数据，优先使用本地消岐
        final List<UmlsConcept> umlsConceptList = umlsConceptService.findByConceptName(conceptName, projectId);
        if (CollUtil.isNotEmpty(umlsConceptList)) {
            for (UmlsConcept umlsConcept : umlsConceptList) {
                metaMapDTOS.add(umlsConceptToDTO(umlsConcept));
            }
            return metaMapDTOS.stream().distinct().collect(Collectors.toList());
        }

        // 调用接口获取数据
        String url = UMLS_API_URL + "metamap/?text=" + conceptName + "&retMax=10&pass=" + DateUtils.getMd5Date();
        try {
            result = HttpUtil.get(url, TIMEOUT);

            if (StrUtil.isNotBlank(result)) {
                JSONArray dataArr = JSON.parseObject(result).getJSONArray("data");
                if (CollUtil.isEmpty(dataArr)) {
                    return null;
                }
                JSONObject data = dataArr.getJSONObject(0);
                if (ObjectUtil.isNull(data)) {
                    return null;
                }
                JSONArray uttrListArr = data.getJSONArray("uttrList");
                if (CollUtil.isEmpty(uttrListArr)) {
                    return null;
                }
                JSONObject uttrList = uttrListArr.getJSONObject(0);
                if (ObjectUtil.isNull(uttrList)) {
                    return null;
                }
                JSONArray mappings = uttrList.getJSONArray("mappings");
                if (CollUtil.isEmpty(mappings)) {
                    return null;
                }
                // 转化为List并进行数据处理 去重
                metaMapDTOS = mappings.toJavaList(MetaMapDTO.class).stream()
                        .distinct().collect(Collectors.toList());
                for (MetaMapDTO metaMapDTO : metaMapDTOS) {
                    metaMapDTO.setRemote(true);
                    metaMapDTO.setConceptType(ConceptTypeEnum.umls.getValue());
                    if (metaMapDTO.getSemanticTypes().size() > 0) {
                        List<String> newTypes = new ArrayList<>();
                        for (String type : metaMapDTO.getSemanticTypes()) {
                            SemanticType semanticType = semanticTypeService.getBaseMapper()
                                    .selectOne(Wrappers.<SemanticType>lambdaQuery().eq(SemanticType::getSimple, type));
                            // 处理SemanticTypes
                            if (semanticType != null && StrUtil.isNotBlank(semanticType.getName())) {
                                newTypes.add(semanticType.getName());
                            }
                        }
                        map.add(metaMapDTO.getConceptId());
                        metaMapDTO.setSemanticTypes(newTypes);
                    }
                }
            }
        } catch (JSONException e) {
            log.warn("getUmls方法解析返回结果失败，请检查json格式：{}", result);
            throw new RRException("UMLS Concept请求异常");
        } catch (Exception e) {
            log.warn("getUmls接口请求超时：{}", url);
            throw new RRException("请求超时");
        }
        if (StrUtil.isNotBlank(conceptId) && !map.contains(conceptId)) {
            List<MetaMapDTO> mapDTOList = getUmlsById(conceptId, projectId);
            mapDTOList.addAll(metaMapDTOS);
            return mapDTOList;
        }
        return metaMapDTOS;
    }

    private MetaMapDTO umlsConceptToDTO(UmlsConcept umlsConcept) {
        final MetaMapDTO metaMapDTO = new MetaMapDTO();
        metaMapDTO.setConceptId(umlsConcept.getConceptId());
        metaMapDTO.setConceptName(umlsConcept.getConceptName());
        metaMapDTO.setPreferredName(umlsConcept.getPreferredName());
        ArrayList<String> list = new ArrayList<>();
        list.add(umlsConcept.getSemanticTypes());
        metaMapDTO.setSemanticTypes(list);
        metaMapDTO.setRemote(false);
        metaMapDTO.setConceptType(ConceptTypeEnum.self.getValue());
        return metaMapDTO;
    }

    /**
     * 根据conceptId获取UMLS Concept数据
     */
    @Override
    public List<MetaMapDTO> getUmlsById(final String conceptId, final Long projectId) {
        if (StrUtil.isBlank(conceptId)) {
            return null;
        }
        List<MetaMapDTO> metaMapDTOS = new ArrayList<>();
        UmlsConcept umlsConcept = umlsConceptService.findByConceptId(conceptId, projectId);
        if (umlsConcept != null) {
            metaMapDTOS.add(umlsConceptToDTO(umlsConcept));
            return metaMapDTOS;
        }

        // 调用接口获取数据
        String remoteResult = null;
        String url = UMLS_API_URL + "umls-proxy/forward/content/current/CUI/" + conceptId + "?pass=" + DateUtils.getMd5Date();
        try {
            log.debug(url);
            remoteResult = HttpUtil.get(url, 5 * 1000);
            if (StrUtil.isBlank(remoteResult)) {
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(remoteResult);
            JSONObject result = jsonObject.getJSONObject("result");
            if (result == null) {
                return null;
            }
            MetaMapDTO mapDTO = new MetaMapDTO();
            mapDTO.setConceptId(conceptId);
            String name = result.getString("name");
            mapDTO.setPreferredName(name);

            JSONArray semanticTypes = result.getJSONArray("semanticTypes");
            List<String> list = new ArrayList<>();
            for (Object semanticTypeObj : semanticTypes) {
                JSONObject semanticType = (JSONObject) semanticTypeObj;
                String semantic = semanticType.getString("name");
                list.add(semantic);
            }
            mapDTO.setSemanticTypes(list);
            metaMapDTOS.add(mapDTO);
        } catch (JSONException e) {
            log.warn("getUmlsById方法解析返回结果失败，请检查json格式：{}", remoteResult);
        } catch (Exception e) {
            log.warn("getUmlsById接口请求超时：{}", url);
            throw new RRException("请求超时");
        }
        return metaMapDTOS;
    }

    /**
     * entity的标签和umls的数据
     *
     * @param ids 实体ID集合
     */
    @Override
    public List<EntityDataVo> getEntityAndUmls(List<String> ids) {
        List<Entity> entities = entityRepository.findAllByIdInAndDeleted(ids, false);
        List<Long> entityLabelIds = entities.stream().map(Entity::getLabelId).collect(Collectors.toList());
        Map<String, List<Attributes>> entityAttrMap = attributesRepository.findAllEntityIdIn(ids).stream().collect(Collectors.groupingBy(Attributes::getEntityId));
        // 加载所有的属性标注
        Map<Long, EntityLabel> entityLabelMap = entityLabelService.listByIds(entityLabelIds).stream().collect(Collectors.toMap(EntityLabel::getId, x -> x));
        Map<Long, String> attrLabelMap = attributeLabelService.list(Wrappers.<AttributeLabel>lambdaQuery().in(AttributeLabel::getEntityLabelId, entityLabelIds))
                .stream().collect(Collectors.toMap(AttributeLabel::getId, AttributeLabel::getName));

        List<EntityDataVo> dataVos = new ArrayList<>();
        for (Entity entity : entities) {
            EntityDataVo dataVo = new EntityDataVo();
            String id = entity.getId();
            EntityLabel label = entityLabelMap.get(entity.getLabelId());
            dataVo.setText(entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.joining(" / ")));
            dataVo.setLabelId(label.getId());
            dataVo.setLabelName(label.getName());
            dataVo.setTextId(id);
            if (entityAttrMap.containsKey(id)) {
                Map<Long, List<Attributes>> map = entityAttrMap.get(id).stream().collect(Collectors.groupingBy(Attributes::getAttrLabelId));
                List<String> attr = new ArrayList<>();
                map.forEach((k, v) -> {
                    attr.add(StrUtil.format("<strong>{}</strong>：{}", attrLabelMap.get(k), v.stream().map(Attributes::getContent).collect(Collectors.joining(";  "))));
                });
                dataVo.setAttr(attr);
            }

            dataVos.add(dataVo);
        }
        return dataVos;
    }

    @Override
    public Note findFirstByArticleIdAndProjectId(String articleId, Long projectId) {
        LambdaQueryWrapper<Note> queryWrapper =
                Wrappers.<Note>lambdaQuery().eq(Note::getArticleId, articleId)
                        .eq(Note::getProjectId, projectId).last(LIMIT_ONE);
        List<Note> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public Note findReviewedByArticleIdAndBatchId(String articleId, Long batchId) {
        LambdaQueryWrapper<Note> queryWrapper =
                Wrappers.<Note>lambdaQuery().eq(Note::getArticleId, articleId)
                        .eq(Note::getStep, NoteStepEnum.reviewed.getCode())
                        .eq(Note::getBatchId, batchId).last(LIMIT_ONE);
        List<Note> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<Note> findByArticleIdAndProjectId(String articleId, Long projectId) {
        LambdaQueryWrapper<Note> queryWrapper =
                Wrappers.<Note>lambdaQuery().eq(Note::getArticleId, articleId)
                        .eq(Note::getProjectId, projectId);
        List<Note> list = this.list(queryWrapper);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public R preImportToEntity(PreImportDTO dto, final Long userId) {
        final Long taskId = dto.getTaskId();
        if (isInvalidLong(taskId)) {
            throw new RRException("taskId 不能为空");
        }
        final PreSourceEnum sourceEnum = findBySource(dto.getSource());
        List<Entity> entityList;
//        this.getById(dto)
        Note note = this.getById(dto.getNoteId());
        if (note == null) {
            throw new RRException("未查询到note数据");
        }
        String articleId = note.getArticleId();
        Long projectId = note.getProjectId();

        if (sourceEnum.equals(PreSourceEnum.SELF)) {
            entityList = entityRepository.findDocumentArticle(taskId, dto.getNoteId(), sourceEnum, null, articleId, projectId);
        } else {
            entityList = entityRepository.findDocumentArticle(null, dto.getNoteId(), sourceEnum, dto.getImportLogId(), articleId, projectId);
        }
        final Long roleId = dto.getRoleId();
        final Long masterTaskId = dto.getMasterTaskId();
        for (Entity entity : entityList) {
            final String preEntityId = entity.getId();
            final Entity savedEntity = preToEntity(entity, taskId, masterTaskId, roleId, userId);
            if (savedEntity == null) {
                continue;
            }
            importPreAttrRelation(savedEntity, preEntityId, taskId, masterTaskId, roleId, userId);
        }
        return R.ok();
    }

    @Override
    public String selectedImportToOriginal(final PreImportDTO dto, Long userId) {
        final Long taskId = dto.getTaskId();
        if (isInvalidLong(taskId)) {
            throw new RRException("taskId 不能为空");
        }

        final String currAnnoUniId = dto.getCurrAnnoUniId();
        final PreSourceEnum sourceEnum = findBySource(dto.getSource());
        final Long masterTaskId = dto.getMasterTaskId();
        if (isInvalidLong(masterTaskId) && sourceEnum.equals(PreSourceEnum.SELF)) {
            throw new RRException("标注来源参数异常");
        }
        final Entity preEntity;
        if (StrUtil.isNotBlank(dto.getCurrAnnoUniId())) {
            preEntity = entityRepository.findByUniqueid(currAnnoUniId).orElseThrow(() -> new RRException("未查询到标注数据"));
        } else {
            preEntity = entityRepository.findById(dto.getEntityId()).orElseThrow(() -> new RRException("未查询到标注数据"));
        }

        if (preEntity.getSource() != sourceEnum.getId()) {
            throw new RRException("标注来源异常");
        }
        if (sourceEnum == PreSourceEnum.CUSTOMIZE) {
            final Long importLogId = preEntity.getImportLogId();
            if (importLogId == null) {
                throw new RRException("预标注数据错误，不存在import log id");
            }
            if (!importLogId.equals(dto.getImportLogId())) {
                throw new RRException("预标注import log id参数错误");
            }
        }

        final String preEntityId = preEntity.getId();
        // 保存标注信息
        final Long roleId = dto.getRoleId();
        final Entity savedEntity = preToEntity(preEntity, taskId, masterTaskId, roleId, userId);

        if (savedEntity == null) {
            throw new RRException("导入的实体不能为空白字符串");
        }

        importPreAttrRelation(savedEntity, preEntityId, taskId, masterTaskId, roleId, userId);

        return savedEntity.getId();
    }

    private void importPreAttrRelation(final Entity savedEntity, final String preEntityId, final Long taskId,
                                       final Long masterTaskId, final Long roleId, final Long userId) {

        List<AnnoDTO.EntityInfo> entityInfos = savedEntity.getEntityInfos();
        if (CollUtil.isEmpty(entityInfos)) {
            return;
        }

        for (AnnoDTO.EntityInfo entityInfo : entityInfos) {
            if (StrUtil.isBlank(entityInfo.getContent())) {
                return;
            }
        }
        Integer isAttr = savedEntity.getIsAttr();
        if (AttrAnnoEnum.not_attr.getCode() == isAttr) {
            // 实体标注和属性标注关系
            final List<Attributes> attributesList = attributesRepository.findByEntityId(preEntityId);
            final String savedEntityId = savedEntity.getId();
            if (CollUtil.isNotEmpty(attributesList)) {
                deleteAllAttrByEntity(savedEntityId, roleId, userId);
                final Date date = new Date();
                for (Attributes attributes : attributesList) {
                    attributes.setId(IdUtils.id());
                    if (!isInvalidLong(masterTaskId)) {
                        attributes.setTaskId(masterTaskId);
                    } else {
                        attributes.setTaskId(taskId);
                    }
                    attributes.setEntityId(savedEntityId);
                    attributes.setAnnotatorId(savedEntity.getAnnotatorId());
                    attributes.setAuditorId(savedEntity.getAuditorId());
                    attributes.setCreateTime(date);
                    attributes.setUpdateTime(date);
                    final String attributeId = attributes.getAttributeId();
                    if (StrUtil.isNotBlank(attributeId)) {
                        String newAttributeId = null;
                        Optional<Entity> entityOptional = entityRepository.findById(attributeId);
                        if (entityOptional.isPresent()) {
                            // 预标注属性标注
                            final Entity preAttrEntity = entityOptional.get();
                            if (AttrAnnoEnum.is_attr.getCode() == preAttrEntity.getIsAttr()) {
                                newAttributeId = preToEntity(preAttrEntity, taskId, masterTaskId, roleId, userId).getId();
                            }
                        }
                        if (newAttributeId == null) {
                            throw new RRException("预标注数据异常，entityId:" + preEntityId + ",attributeId:" + attributeId);
                        }
                        attributes.setAttributeId(newAttributeId);
                    }
                }
                if (CollUtil.isNotEmpty(attributesList)) {
                    attributesRepository.saveAll(attributesList);
                    annoCacheService.removeAttributesCache(attributesList);
                }
            }
        }
    }

    private Entity preToEntity(final Entity entity, final Long taskId, final Long masterTaskId, final Long roleId, final Long userId) {
        AnnoDTO annoDTO = EntityMapper.INSTANCE.entityToAnnoDTO(entity);
        List<AnnoDTO.EntityInfo> entityInfos = annoDTO.getEntityInfos();
        if (CollUtil.isEmpty(entityInfos)) {
            return null;
        }
        for (AnnoDTO.EntityInfo entityInfo : entityInfos) {
            String content = entityInfo.getContent();
            if (StrUtil.isBlank(content)) {
                return null;
            }
        }
        if (!isInvalidLong(masterTaskId)) {
            annoDTO.setTaskId(masterTaskId);
        } else {
            annoDTO.setTaskId(taskId);
        }
        annoDTO.setRoleId(roleId);
        ValidatorUtils.validateEntity(annoDTO, AddEntity.class);
        AddEntityVO addEntityVO = addEntity(annoDTO, userId, true);
        return addEntityVO.getExistEntity();
    }

    @Override
    public R deleteEntity(String uniqueid, Long roleId, Long userId, boolean deleteAttrAsWell, boolean forceDelAttr) {
        return deleteEntity(uniqueid, roleId, userId, deleteAttrAsWell, true, forceDelAttr);
    }

    /**
     * 删除实体（属性）标注
     */
    private R deleteEntity(String uniqueid, Long roleId, Long userId, final boolean deleteAttrAsWell, final boolean findNewData, final boolean forceDelAttr) {
        final Optional<Entity> entityOptional = entityRepository.findByUniqueid(uniqueid);
        Entity entity;
        if (entityOptional.isPresent()) {
            entity = entityOptional.get();
            if (entity.getSource() != PreSourceEnum.SELF.getId()) {
                return R.error("禁止删除预标注实体");
            }
        } else {
            return R.error("标注信息不存在");
        }

        final String entityId = entity.getId();
        Integer isAttr = entity.getIsAttr();
        if (isAttr == AttrAnnoEnum.is_attr.getCode()) {
            final List<Attributes> attrList = attributesRepository.findByAttributeId(entityId);
            if (CollUtil.isNotEmpty(attrList)) {
                if (forceDelAttr) {
                    deleteAttributes(attrList, roleId, userId);
                } else {
                    final List<Map<String, String>> usedEntities = new ArrayList<>();
                    final List<Entity> entities = entityRepository.findByIds(attrList.stream().map(Attributes::getEntityId).filter(StrUtil::isNotBlank).collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(entities)) {
                        for (Entity item : entities) {
                            List<AnnoDTO.EntityInfo> entityInfos = item.getEntityInfos();
                            final Map<String, String> row = new HashMap<>();
                            row.put("name", StrUtil.join(" / ", entityInfos.stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList())));
                            usedEntities.add(row);
                        }
                    }
                    return R.error(2001, "该属性标注正在使用中").put("usedEntities", usedEntities);
                }
            }
            /*if (isAttrExists(entityId)) {
                return R.error("删除失败，该属性标注正在使用中");
            }*/
        } else {
            // 查询该实体是否在关系标注中被使用
            if (relationshipRepository.isRelationExists(entity.getTaskId(), entityId)) {
                return R.error("删除失败，该实体已在关系标注中使用");
            }
        }
        List<Attributes> attributesList = attributesRepository.findByEntityId(entityId);
        // 删除属性关系
        if (CollUtil.isNotEmpty(attributesList)) {
            if (deleteAttrAsWell) {
                // 删除实体标注时，同时删除对应属性标注
                for (Attributes attributes : attributesList) {
                    String attributeId = attributes.getAttributeId();
                    if (StrUtil.isNotBlank(attributeId)) {
                        Optional<Entity> optional = entityRepository.findByIdCus(attributeId);
                        optional.ifPresent(value -> deleteEntity(value, roleId, userId));
                    }
                }
            }
            deleteAttributes(attributesList, roleId, userId);
        }

        deleteEntity(entity, roleId, userId);

        // 记录删除日志
        String msg = "标注文本: " + getEntityAllContent(entity) + " (ID:" + entityId + ")";
        final Long taskId = entity.getTaskId();
        commonService.addLog(entity.getNoteId(), taskId, OptionEnum.delete.getCode(), msg, userId);

        if (findNewData) {
            return R.success(initNewDoc(entity.getNoteId(), taskId));
        } else {
            return R.ok();
        }
    }

    private void deleteEntity(final Entity entity, final Long roleId, final Long userId) {
        setUserId(entity, userId, roleId);
        entity.setUpdateTime(new Date());
        entity.setDeleted(DeleteEnum.delete.getVal());
        entityRepository.save(entity);
        annoCacheService.removeEntityCache(entity.getId());
    }

    private void deleteAllAttrByEntity(final String entityId, final Long roleId, final Long userId) {
        deleteAttributes(attributesRepository.findByEntityId(entityId), roleId, userId);
    }

    @Override
    public void deleteAttributes(List<Attributes> deleteList, final Long roleId, final Long userId) {
        if (CollUtil.isNotEmpty(deleteList)) {
            Date date = new Date();
            for (Attributes attribute : deleteList) {
                AttributeEntityServiceImpl.setAttrUserId(attribute, userId, roleId);
                attribute.setDeleted(DeleteEnum.delete.getVal());
                attribute.setUpdateTime(date);
            }
            attributesRepository.saveAll(deleteList);
            annoCacheService.removeAttributesCache(deleteList);
        }
    }

    @Override
    public Document initNewDoc(Long noteId, Long taskId) {
        final Note note = this.baseMapper.selectById(noteId);
        return getDocumentArticle(note.getDocumentId(), taskId, PreSourceEnum.SELF.getId(), noteId, false, null, null, null);
    }

    /**
     * 批量删除实体标注
     */
    @Override
    public R deleteMultipleEntity(AnnoDTO annoDTO, Long userId) {
        String uniqueid = annoDTO.getEntityId();
        Optional<Entity> entityOptional = entityRepository.findByUniqueid(uniqueid);
        if (!entityOptional.isPresent()) {
            log.error("未查询到该实体{}", uniqueid);
            return R.error("未查询到该实体" + uniqueid);
        }
        final Entity entity = entityOptional.get();

        final String batchAnnotateId = entity.getBatchAnnotateId();

        final boolean deleteAttrAsWell = Boolean.TRUE.equals(annoDTO.getDeleteAttrAsWell());
//        deleteAttrAsWell = deleteAttrAsWell != null && deleteAttrAsWell;
        final Long roleId = annoDTO.getRoleId();
        // 如果不是批量标注的，则采用非批量删除方式删掉
        if (StrUtil.isBlank(batchAnnotateId)) {
            return deleteEntity(entity.getEntityInfos().get(0).getUniqueid(), roleId, userId, deleteAttrAsWell, Boolean.TRUE.equals(annoDTO.getForceDelAttr()));
        }

        List<Entity> entityIdList = getBatchEntities(batchAnnotateId);
        for (Entity optionalEntity : entityIdList) {
            R r = deleteEntity(optionalEntity.getEntityInfos().get(0).getUniqueid(), roleId, userId, deleteAttrAsWell, Boolean.TRUE.equals(annoDTO.getForceDelAttr()));
            if (((int) r.get("code")) != 0) {
                return r;
            }
        }
        return R.success(initNewDoc(entity.getNoteId(), entity.getTaskId()));
    }

    private List<Entity> getBatchEntities(String batchAnnotateId) {
        // 查询出所有相同批次标注的实体
        return entityRepository.findBatchEntities(batchAnnotateId);
    }

    /**
     * 填充文章回显数据
     */
    void fillAnnotation(Object obj, Map<String, List<AnnotationsDTO>> map) {
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不能标注
                if (IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (ID.equals(name) && map.containsKey(value.toString())) {
                    ReflectUtil.setFieldValue(currentObj, ANNOTATION, map.get(value.toString()));
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
    }

    /**
     * 恢复审核员误删除的标注
     */
    @Override
    public void restoreDeletedEntry() {
        final long start = System.currentTimeMillis();
        log.warn("开始恢复审核员误删除的标注");
        final List<Entity> entities = mongoTemplate.find(new Query(Criteria.where("deleted").is(DeleteEnum.delete.getVal())
                .and("source").is(PreSourceEnum.SELF.getId())
                .and("task_id").exists(true)
                .and("md5").exists(true)
                .and("annotator_id").exists(true)
                .and("auditor_id").exists(true)), Entity.class);
        log.info("已存在的审核员删除实体总数量：{}", entities.size());
        int count = 0;
        final int size = CollUtil.size(entities);
        if (size > 0) {
            for (int i = 0; i < size; i++) {
                if (i % 500 == 0) {
                    log.warn("恢复审核员删除实体进度：{}/{}", i, size);
                }
                final Entity entity = entities.get(i);
                final Long taskId = entity.getTaskId();
                final Entity one = mongoTemplate.findOne(new Query(Criteria.where("task_id").is(taskId)
                        .and("md5").is(entity.getMd5())
                        .and("deleted").is(DeleteEnum.not_delete.getVal())
                        .and("auditor_id").exists(true)
                        .and("annotator_id").exists(false)
                        .and("source").is(entity.getSource())), Entity.class);

                if (one != null) {
                    count++;
                    entity.setAuditorId(null);
                    entity.setDeleted(DeleteEnum.not_delete.getVal());
                    mongoTemplate.save(entity);
                    mongoTemplate.remove(one);
                }
            }
        }
        log.warn("恢复审核员误删除finish, 恢复总数：{}, 耗时：{}", count, DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND));
    }

    @Override
    public Entity findAnnotateByUniId(String uniqueId) {
        if (StrUtil.isBlank(uniqueId)) {
            throw new RRException("uniqueId不能为空");
        }

        Optional<Entity> entityOpt = entityRepository.findByUniqueid(uniqueId);
        if (entityOpt.isPresent()) {
            Entity entity = entityOpt.get();
            final List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();
            final List<String> allEntityNames = new ArrayList<>();
            if (CollUtil.isNotEmpty(entityInfos)) {
                for (AnnoDTO.EntityInfo info : entityInfos) {
                    String content = info.getContent();
                    if (content != null) {
                        allEntityNames.add(content);
                    }
                }
            }
            entity.setEntityInfos(entityInfos);
            entity.setAllEntityNames(allEntityNames);

            // 如果是属性, 则把关联的实体的标签ID找出来
            if (AttrAnnoEnum.is_attr.getCode() == entity.getIsAttr()) {
                List<Attributes> attributeIds = attributesRepository.findByAttributeId(entity.getId());
                if (CollUtil.isNotEmpty(attributeIds)) {
                    Attributes attributes = attributeIds.get(0);
                    Long attrLabelId = attributes.getAttrLabelId();
                    if (attrLabelId != null) {
                        AttributeLabel attributeLabel = attributeLabelService.findById(attrLabelId);
                        entity.setAttributeLabel(attributeLabel);
                    }
                }
            }
            return entity;
        }

        throw new RRException("未找到该实体或属性标注");
    }


    @Override
    public Entity getEntityNameById(String entityId) {
        if (StrUtil.isBlank(entityId)) {
            throw new RRException("实体ID不能为空");
        }

        Optional<Entity> entityOpt = entityRepository.findById(entityId);
        if (entityOpt.isPresent()) {
            Entity entity = entityOpt.get();
            final List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();
            final List<String> allEntityNames = new ArrayList<>();
            if (CollUtil.isNotEmpty(entityInfos)) {
                for (AnnoDTO.EntityInfo info : entityInfos) {
                    String content = info.getContent();
                    if (content != null) {
                        allEntityNames.add(content);
                    }
                }
            }

            final Entity result = new Entity();
            result.setId(entity.getId());
            result.setProjectId(entity.getProjectId());
            result.setBatchId(entity.getBatchId());
            result.setNoteId(entity.getNoteId());
            result.setTaskId(entity.getTaskId());
            result.setLabelId(entity.getLabelId());
            result.setEntityInfos(entityInfos);
            result.setAnnotate(entity.getAnnotate());
            result.setAllEntityNames(allEntityNames);
            return result;
        }

        throw new RRException("未找到该实体或属性标注");
    }

    @Override
    public List<Note> findAllByBatchIdAndStepAndNotInvalid(Long batchId, Integer step) {
        if (batchId == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, batchId).eq(step != null, Note::getStep, step)
                .eq(Note::getInvalid, NoteInvalidEnum.normal.getCode()));
    }

    @Override
    public List<Note> findAllByBatchIdAndArticleIdInAndNotInvalid(Long batchId, List<String> articleIds) {
        if (batchId == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<Note>lambdaQuery().eq(Note::getBatchId, batchId)
                .in(CollUtil.isNotEmpty(articleIds), Note::getArticleId, articleIds)
                .eq(Note::getInvalid, NoteInvalidEnum.normal.getCode()));
    }

    /**
     * 根据讨论ID过滤历史版本相关的实体和属性
     */
    private List<Entity> filterHistoricalEntities(String discussionId) {
        try {
            // 根据讨论ID查找评论记录
            Comment comment = commentRepository.findById(discussionId)
                    .orElseThrow(() -> new RRException("讨论记录不存在"));

            // 获取相关的实体ID列表（包括已删除的）
            List<String> entityIds = comment.getEntityIds();

            List<Entity> entityList = new ArrayList<>();
            Iterable<Entity> optionalEntitys = entityRepository.findAllById(entityIds);
            if (CollUtil.isNotEmpty(optionalEntitys)) {
                for (Entity optionalEntity : optionalEntitys) {
                    entityList.add(optionalEntity);
                }
            }
            return entityList;
        } catch (Exception e) {
            log.error("过滤历史版本实体失败: {}", e.getMessage(), e);
            // 如果过滤失败，返回空列表以确保安全
            return new ArrayList<>();
        }
    }

}
