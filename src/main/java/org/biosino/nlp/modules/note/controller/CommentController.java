package org.biosino.nlp.modules.note.controller;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.note.dto.QuestionDTO;
import org.biosino.nlp.modules.note.entity.mongo.Comment;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.CommentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标注功能区 - 讨论区
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Api("提问评论接口")
@RestController
@RequestMapping("/comment")
@RequiredArgsConstructor
public class CommentController extends AbstractController {

    private final CommentService commentService;
    private final NoteService noteService;

    /**
     * 提交提问
     */
    @PostMapping("/submitQuestion")
    public R submitQuestion(@RequestBody QuestionDTO questionDTO) {
        // 创建Comment对象
        Comment comment = new Comment();
        comment.setProjectId(questionDTO.getProjectId());
        comment.setBatchId(questionDTO.getBatchId());
        comment.setNoteId(questionDTO.getNoteId());
        comment.setTaskId(questionDTO.getTaskId());
        comment.setQuestionText(questionDTO.getQuestionText());
        comment.setViolateRule(questionDTO.getViolateRule());
        comment.setQuestionerId(getUserId());

        // 根据当前选中的uniqueId查找相关的实体和属性
        String currentUniqueId = questionDTO.getCurrentUniqueId();
        if (currentUniqueId != null) {
            try {
                Entity entity = noteService.findAnnotateByUniId(currentUniqueId);

                comment.setEntityId(entity.getId());

                // 通过entity_id到m_attributes找出所有关联记录
                List<String> attributeRecordIds = commentService.findAttributeRecordIdsByEntityId(entity.getId());
                // 存储m_attributes记录的ID
                comment.setAttributeIds(attributeRecordIds);

                //  通过这些m_attributes记录的attribute_id，回到m_entity找到所有相关实体
                List<String> relatedEntityIds = commentService.findEntityIdsByAttributeRecords(entity.getId(), attributeRecordIds);
                // 存储m_entity记录的ID
                comment.setEntityIds(relatedEntityIds);

            } catch (Exception e) {
                return R.error("查找相关实体和属性失败：" + e.getMessage());
            }
        }

        Comment result = commentService.createQuestion(comment);
        return R.success(result);
    }

    /**
     * 获取实体或属性的详细信息（用于提问弹窗）
     */
    @GetMapping("/getEntityOrAttributeForQuestion")
    public R getEntityOrAttributeForQuestion(@RequestParam String uniqueId) {
        Entity result = noteService.findAnnotateByUniId(uniqueId);
        return R.success(result);
    }

    /**
     * 获取实体或属性的详细信息（用于在讨论区回显名称）
     */
    @GetMapping("/getEntityNameById")
    public R getEntityNameById(@RequestParam String entityId) {
        Entity result = noteService.getEntityNameById(entityId);
        return R.success(result);
    }

    /**
     * 回复提问
     */
    @PostMapping("/replyQuestion")
    public R replyQuestion(@RequestParam String commentId,
                           @RequestParam String replyText) {
        Comment result = commentService.replyQuestion(commentId, replyText, getUserId());
        return R.success(result);
    }

    /**
     * 根据任务ID查询提问列表
     */
    @GetMapping("/getQuestionsByTaskId")
    public R getQuestionsByTaskId(@RequestParam Long taskId) {
        List<Comment> questions = commentService.getQuestionsByTaskId(taskId);

        // 处理每个提问，添加实体名称和用户名称
        List<Comment> processedQuestions = commentService.processQuestionsWithDetails(questions);

        return R.success(processedQuestions);
    }

    /**
     * 更新提问状态
     */
    @PostMapping("/updateQuestionStatus")
    public R updateQuestionStatus(@RequestParam String commentId,
                                  @RequestParam Integer status) {
        Comment result = commentService.updateQuestionStatus(commentId, status);
        return R.success(result);
    }
}
