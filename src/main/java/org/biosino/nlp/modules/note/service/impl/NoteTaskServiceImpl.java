package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.MasterEnum;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Constant;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.DocumentRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.AssignTaskDTO;
import org.biosino.nlp.modules.note.dto.TaskDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.enums.NoteInvalidEnum;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.CommentService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.vo.*;
import org.biosino.nlp.modules.project.dao.BatchDao;
import org.biosino.nlp.modules.project.dao.ProjectDao;
import org.biosino.nlp.modules.project.entity.Attachment;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.entity.ProjectUser;
import org.biosino.nlp.modules.project.service.AttachmentService;
import org.biosino.nlp.modules.project.service.ProjectUserService;
import org.biosino.nlp.modules.project.service.impl.BatchServiceImpl;
import org.biosino.nlp.modules.project.service.impl.ProjectServiceImpl;
import org.biosino.nlp.modules.sys.dao.SysUserDao;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.nlp.common.utils.Constant.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("noteTaskService")
public class NoteTaskServiceImpl extends ServiceImpl<NoteTaskDao, NoteTask> implements NoteTaskService {

    @Autowired
    private NoteDao noteDao;
    @Autowired
    private BatchDao batchDao;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private SysUserDao userDao;
    @Autowired
    private DocumentRepository documentRepository;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private RelationshipRepository relationshipRepository;
    @Autowired
    private AttachmentService attachmentService;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private CommentService commentService;

    @Override
    public List<ProjectBatchVO> queryProjectList(Long userId, Long roleId) {
        if (userId == null || roleId == null) {
            throw new RRException("查询项目批次列表失败，请检查用户参数！");
        }
        return noteTaskDao.queryProjectList(userId, roleId);
    }

    @Override
    public IPage<TaskListVO> queryTaskList(TaskDTO task) {
        String activeStep = task.getActiveStep();
        List<Integer> step = CollUtil.newArrayList(NoteStepEnum.findStep(activeStep));
        Integer markRounds = getProjectByBatchId(task.getBatchId()).getMarkRounds();
        task.setMarkRounds(markRounds);

        task.setStep(step);
        if (ALL.equals(task.getActiveStep())) {
            return noteTaskDao.queryAllTaskList(new Query<TaskListVO>().getPage(task, "updateTime", false), task);
        }
        if (task.getRoleId() == RoleEnum.annotator.getId()) {
            return noteTaskDao.queryAnnotatorTaskList(new Query<TaskListVO>().getPage(task, "updateTime", false), task);
        }
        if (task.getRoleId() == RoleEnum.auditor.getId()) {
            // 查询打回中的语句需要单独执行
            if (NoteStepEnum.repulse.name().equals(task.getActiveStep())) {
                return noteTaskDao.queryRepulseTaskList(new Query<TaskListVO>().getPage(task, "updateTime", false), task);
            }
            return noteTaskDao.queryAuditorTaskList(new Query<TaskListVO>().getPage(task, "updateTime", false), task);
        }
        return null;
    }

    @Override
    public PageIdsListVO queryTaskList4VO(final TaskDTO taskDTO, final Long userId) {
        final PageIdsListVO pageIdsListVO = new PageIdsListVO();
        taskDTO.setUserId(userId);

        Long batchId = taskDTO.getBatchId();
        Batch batch = batchDao.selectById(batchId);
        pageIdsListVO.setProjectId(batch.getProjectId());
        pageIdsListVO.setFreeAnnotation(batch.getFree());
        pageIdsListVO.setMode(batch.getMode());

        pageIdsListVO.setPageIdsVOList(allEntityPageIds(taskDTO));

        IPage<TaskListVO> page = queryTaskList(taskDTO);
        List<TaskListVO> records = page.getRecords();
        Long roleId = taskDTO.getRoleId();
        Long annotatorId = taskDTO.getAnnotatorId();
        for (TaskListVO record : records) {
            if (roleId == RoleEnum.annotator.getId()) {
                if (userId.equals(annotatorId) &&
                        (NoteStepEnum.noting.getCode().equals(record.getStep())
                                || NoteStepEnum.unmarked.getCode().equals(record.getStep())
                                || NoteStepEnum.repulse.getCode().equals(record.getStep()))) {
                    record.setEditable(true);
                }
                continue;
            }
            if (roleId == RoleEnum.auditor.getId()) {
                if (record.getAuditorId() == null) {
                    record.setEditable(true);
                    continue;
                }
                if (userId.equals(record.getAuditorId()) &&
                        (NoteStepEnum.marked.getCode().equals(record.getStep())
                                || NoteStepEnum.reviewing.getCode().equals(record.getStep())
                                || NoteStepEnum.corrected.getCode().equals(record.getStep()))) {
                    record.setEditable(true);
                }
                continue;
            }
            record.setEditable(false);
        }

        pageIdsListVO.setListData(new PageUtils(page));
        return pageIdsListVO;
    }

    private List<PageIdsVO> allEntityPageIds(final TaskDTO taskDTO) {
        final TaskDTO dto = BatchServiceImpl.initBaseDTO4Ids(taskDTO, TaskDTO.class);
        IPage<TaskListVO> page = queryTaskList(dto);
        // 获取当前页数据
        List<TaskListVO> records = page.getRecords();
        final Set<PageIdsVO> set = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(records)) {
            for (TaskListVO item : records) {
                final PageIdsVO pageIdsVO = new PageIdsVO();
                pageIdsVO.setBatchId(dto.getBatchId());
                pageIdsVO.setNoteId(item.getNoteId());
                set.add(pageIdsVO);
            }
        }

        return new ArrayList<>(set);
    }

    @Override
    public List<UserVO> queryUserList(Long batchId, Long roleId) {
        if (batchId == null || roleId == null) {
            throw new RRException("暂无参与的项目和批次");
        }
        return noteTaskDao.queryUserList(batchId, roleId);
    }

    /**
     * 根据noteId查询当前角色下的任务,并补充额外信息
     *
     * @return 标注任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskDetailVO getTaskByNote(AssignTaskDTO dto) {
        TaskDetailVO vo = handleTaskInfo(dto);
        /*// 判断是否存在规范文件
        Long docId = vo.getProject().getDocId();
        if (docId != null) {
            final Attachment attachment = attachmentService.getById(docId);
            if (attachment != null) {
                final File file = ProjectServiceImpl.getSpecFile(attachment.getFileName());
                if (file.exists() && file.isFile()) {
                    vo.setHasSpecFile(true);
                }
            }
        }*/
        return vo;
    }

    /**
     * 根据noteId查询当前角色下的任务
     *
     * @return 标注任务
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskDetailVO handleTaskInfo(AssignTaskDTO dto) {
        Long userId = dto.getUserId();
        Long roleId = dto.getRoleId();
        Long noteId = dto.getNoteId();

        if (noteId == null) {
            throw new RRException("文章NoteID不能为空");
        }
        Note note = noteDao.selectById(noteId);
        if (note == null) {
            throw new RRException("未查询到该文章");
        }
        final String documentId = note.getDocumentId();
        final Document document = documentRepository.findById(documentId).orElseThrow(() -> new RRException("未查询到该文章对应的Document"));

        // 设置文章基本信息
        final TaskDetailVO vo = new TaskDetailVO();
        vo.setDocumentSource(document.getSource());
        Project project = projectDao.selectById(note.getProjectId());
        Batch batch = batchDao.selectById(note.getBatchId());
        vo.setProject(project);
        vo.setProjectName(project.getName());
        vo.setBatchName(batch.getName());
        vo.setStep(note.getStep());
        vo.setStatus(NoteStepEnum.findStep(note.getStep()));
        vo.setArticleId(note.getArticleId());

        NoteTask noteTask;
        // 如果是只读状态，说明是已有的task，直接返回
        if (!dto.getEditable()) {
            LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId())
                    .eq(NoteTask::getAnnotator, dto.getAnnotatorId());
            noteTask = noteTaskDao.selectOne(taskWrapper);

            if (noteTask == null) {
                return vo;
            }

            // 如果是已审核数据，则查找是master的标注员task
            if (NoteStepEnum.reviewed.getCode().equals(noteTask.getStep())) {
                LambdaQueryWrapper<NoteTask> taskWrapper2 = Wrappers.<NoteTask>lambdaQuery()
                        .eq(NoteTask::getNoteId, note.getNoteId())
                        .eq(NoteTask::getMaster, MasterEnum.master.getValue());
                noteTask = noteTaskDao.selectOne(taskWrapper2);
            }
            getTaskDetailVO(vo, noteTask);
            vo.setTask(noteTask);
            return vo;
        }
        Date currentDate = new Date();
        if (roleId == RoleEnum.annotator.getId()) {
            // 查询当前标注员是否有和该文章的关联
            LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId())
                    .eq(NoteTask::getAnnotator, userId)
                    .last(LIMIT_ONE);
            noteTask = noteTaskDao.selectOne(taskWrapper);
            if (noteTask != null) {
                getTaskDetailVO(vo, noteTask);
                return vo;
            }
            Integer markRounds = project.getMarkRounds();
            // 当前Note可能已经被其他标注员拉取，分配满了，自动给他分配下一篇
            if (markRounds.equals(note.getPullCount())) {
                dto.setBatchId(note.getBatchId());
                vo.setTask(assignAnnotatorTask(dto));
                return vo;
            }
            // 没有关联说明是未标注的文章，建立新关联
            noteTask = new NoteTask();
            noteTask.setProjectId(note.getProjectId());
            noteTask.setBatchId(note.getBatchId());
            noteTask.setNoteId(note.getNoteId());
            noteTask.setStep(NoteStepEnum.noting.getCode());
            noteTask.setAnnotator(userId);
            noteTask.setAnnoStartTime(currentDate);
            noteTask.setCreateTime(currentDate);
            noteTask.setUpdateTime(currentDate);
            noteTaskDao.insert(noteTask);

            vo.setStep(note.getStep());
            vo.setInvalid(noteTask.getInvalid());
            vo.setStatus(NoteStepEnum.findStep(note.getStep()));
            vo.setTaskStep(noteTask.getStep());
            vo.setTaskStatus(NoteStepEnum.findStep(noteTask.getStep()));
            note.setPullCount(note.getPullCount() + 1);
            updateNote(note, NoteStepEnum.noting);
            vo.setTask(noteTask);
            return vo;
        }

        // 审核员
        if (roleId == RoleEnum.auditor.getId()) {
            Long annotatorId = dto.getAnnotatorId();
            if (annotatorId == null) {
                throw new RRException("请先在任务列表页面选择标注员后重试");
            }
            // 查询当前审核员是否和该文章有历史关联
            LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId())
                    .eq(NoteTask::getAnnotator, annotatorId)
                    .eq(NoteTask::getAuditor, userId).last(LIMIT_ONE);
            noteTask = noteTaskDao.selectOne(taskWrapper);
            if (noteTask != null) {
                if (note.getStep().equals(NoteStepEnum.marked.getCode())) {
                    // 把别人的相同文章一起置为审核中 或 已修正
                    LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                            .eq(NoteTask::getNoteId, note.getNoteId());
                    List<NoteTask> tasks = noteTaskDao.selectList(otherTaskWrapper);
                    // 集中修改这个note的状态
                    for (NoteTask task : tasks) {
                        task.setAuditor(userId);
                        task.setAuditStartTime(currentDate);
                        if (noteTask.getTaskId().equals(task.getTaskId())) {
                            noteTask = task;
                        }
                        updateTask(task, NoteStepEnum.reviewing);
                    }
                    updateNote(note, NoteStepEnum.reviewing);
                }
                // 被打回，已修改的情况，也许其他人还没改好，所以整个还得是打回中
                if (note.getStep().equals(NoteStepEnum.noting.getCode()) && noteTask.getStep().equals(NoteStepEnum.corrected.getCode())) {
                    noteTask.setStep(NoteStepEnum.repulse.getCode());
                }
                vo.setStep(note.getStep());
                vo.setStatus(NoteStepEnum.findStep(note.getStep()));
                getTaskDetailVO(vo, noteTask);
                return vo;
            }
            if (NoteStepEnum.marked.getCode().equals(note.getStep())) {
                // 可能是待审核状态，没有关联任何标注员
                LambdaQueryWrapper<NoteTask> task2Wrapper = Wrappers.<NoteTask>lambdaQuery()
                        .eq(NoteTask::getNoteId, note.getNoteId())
                        .eq(NoteTask::getStep, NoteStepEnum.marked.getCode())
                        .isNull(NoteTask::getAuditor);
                List<NoteTask> tasks = noteTaskDao.selectList(task2Wrapper);
                if (CollUtil.isNotEmpty(tasks)) {
                    // 集中修改这个NoteTask的状态
                    for (NoteTask task : tasks) {
                        task.setStep(NoteStepEnum.reviewing.getCode());
                        task.setAuditor(userId);
                        task.setAuditStartTime(currentDate);
                        task.setUpdateTime(currentDate);
                        noteTaskDao.updateById(task);
                        if (task.getAnnotator().equals(annotatorId)) {
                            noteTask = task;
                        }
                    }
                    updateNote(note, NoteStepEnum.reviewing);
                    vo.setStep(note.getStep());
                    vo.setStatus(NoteStepEnum.findStep(note.getStep()));
                    vo.setTask(noteTask);
                    if (noteTask != null) {
                        getTaskDetailVO(vo, noteTask);
                    }
                    return vo;
                }
            }
            // 重新自动分配下一篇
            dto.setBatchId(note.getBatchId());
            vo.setTask(assignAuditorTask(dto));
            return vo;
        }

        // 项目管理员 和 项目观察员
        // 找出标注这个文章的审核员
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, note.getNoteId())
                .isNotNull(NoteTask::getAuditor).last(LIMIT_ONE);
        noteTask = noteTaskDao.selectOne(taskWrapper);
        // 审核员没有则找出标注的标注员
        if (noteTask == null) {
            LambdaQueryWrapper<NoteTask> task2Wrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, note.getNoteId())
                    .isNotNull(NoteTask::getAnnotator).last(LIMIT_ONE);
            noteTask = noteTaskDao.selectOne(task2Wrapper);
        }
        return getTaskDetailVO(vo, noteTask);
    }

    /**
     * 如果有task则优先取task的状态
     */
    private static TaskDetailVO getTaskDetailVO(TaskDetailVO vo, NoteTask noteTask) {
        if (noteTask == null) {
            return vo;
        }
        vo.setInvalid(noteTask.getInvalid());
        vo.setRepulseMsg(noteTask.getRepulseMsg());
        vo.setTaskStep(noteTask.getStep());
        vo.setTaskStatus(NoteStepEnum.findStep(noteTask.getStep()));
        vo.setTask(noteTask);
        return vo;
    }

    /**
     * 标注员提交完成任务
     *
     * @param force 是否强制提交，忽略标注轮数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAnnotatorTask(Long taskId, boolean force) {
        if (taskId == null) {
            throw new RRException("任务ID不能为空");
        }
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("未查询到该任务");
        }
        Integer taskStep = noteTask.getStep();
        if (NoteStepEnum.repulse.getCode().equals(taskStep)) {
            reSubmitAnnotatorTask(taskId, force);
            return;
        }
        if (NoteStepEnum.noting.getCode().equals(taskStep)) {
            noteTask.setAnnoEndTime(new Date());
            noteTask.setMd5(getTaskMd5(noteTask));
            updateTask(noteTask, NoteStepEnum.marked);
        }

        // 强制提交时，把所有未提交的也都完成
        if (force) {
            LambdaQueryWrapper<NoteTask> notingTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, noteTask.getNoteId())
                    .eq(NoteTask::getStep, NoteStepEnum.noting.getCode());

            List<NoteTask> notingTasks = noteTaskDao.selectList(notingTaskWrapper);
            for (NoteTask notingTask : notingTasks) {
                notingTask.setAnnoEndTime(new Date());
                notingTask.setMd5(getTaskMd5(notingTask));
                updateTask(notingTask, NoteStepEnum.marked);
            }
        }

        // 查询其他人的是否已经也是完成标注，如果全部都完成标注，则修改Note表中文章的总状态
        LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, noteTask.getNoteId())
                .eq(NoteTask::getStep, NoteStepEnum.marked.getCode());

        Integer completedCount = noteTaskDao.selectCount(otherTaskWrapper);

        Project project = projectDao.selectById(noteTask.getProjectId());
        int markRounds = project.getMarkRounds();

        // 强制提交 或者 标注轮数达标
        if (force || markRounds == completedCount) {
            Note note = noteDao.selectById(noteTask.getNoteId());
            updateNote(note, NoteStepEnum.marked);

            // 多人标注情况下，判断如果相同，则自动审核
            if (completedCount > 1 && project.getAutoReview()) {
                autoReview(note);
            }
        }
    }

    /**
     * 判断当前提交的任务，所有人标注的内容是否相同，相同则自动完成审核
     */
    private void autoReview(Note note) {
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, note.getNoteId())
                .eq(NoteTask::getStep, NoteStepEnum.marked.getCode());
        List<NoteTask> tasks = noteTaskDao.selectList(taskWrapper);

        if (CollUtil.isEmpty(tasks)) {
            return;
        }
        NoteTask masterTask = tasks.get(0);
        masterTask.setMaster(MasterEnum.master.getValue());

        // 蓝本标注员标注正确的 实体数、属性数和关系数
        long entityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long attrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long relationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long annoNum = entityNum + attrNum + relationNum;

        Date currentDate = new Date();

        String md5 = tasks.get(0).getMd5();
        for (NoteTask task : tasks) {
            String taskMd5 = task.getMd5();
            if (taskMd5 != null && !taskMd5.equals(md5)) {
                return;
            }
            task.setAuditor(Constant.AUTO_AUDITOR);
            task.setAuditStartTime(currentDate);
            task.setAuditEndTime(currentDate);
            task.setUpdateTime(currentDate);

            task.setCorrectTotal(annoNum);
            task.setCorrectRate(100.00F);
            task.setCorrectEntity(entityNum);
            task.setCorrectAttr(attrNum);
            task.setCorrectRelation(relationNum);
            // 自动审核，应标数等于标对数
            task.setNeedTotal(annoNum);

            task.setStep(NoteStepEnum.reviewed.getCode());
        }

        for (NoteTask task : tasks) {
            noteTaskDao.updateById(task);
        }
        updateNote(note, NoteStepEnum.reviewed);
    }

    /**
     * 计算当前用户标注的所有内容的MD5值
     */
    private String getTaskMd5(NoteTask noteTask) {
        // 查询是否有关系
        List<String> md5List = getTaskMd5List(noteTask.getTaskId());
        if (md5List == null) {
            if (NoteInvalidEnum.invalid.getCode().equals(noteTask.getInvalid())) {
                return SecureUtil.md5(noteTask.getInvalid().toString());
            }
            return "";
        }
        return SecureUtil.md5(noteTask.getInvalid() + "#" + CollUtil.join(md5List, "_"));
    }

    /**
     * 标注员重新提交改正的任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void reSubmitAnnotatorTask(Long taskId, boolean force) {
        if (taskId == null) {
            throw new RRException("任务ID不能为空");
        }
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("未查询到该任务");
        }
        Date currentDate = new Date();
        noteTask.setMd5(getTaskMd5(noteTask));
        noteTask.setAnnoEndTime(currentDate);
        updateTask(noteTask, NoteStepEnum.corrected);

        // 查询其他人的是否已经也是完成修改，如果全部都完成修正，则修改Note表中文章的总状态为待审核
        LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, noteTask.getNoteId())
                .notIn(NoteTask::getStep, CollUtil.newArrayList(NoteStepEnum.marked.getCode(), NoteStepEnum.corrected.getCode()));

        int notCompletedCount = noteTaskDao.selectCount(otherTaskWrapper);

        // 如果全部都是已标注和已修正的数据，则说明全部都修改完了
        if (notCompletedCount == 0) {
            Note note = noteDao.selectById(noteTask.getNoteId());
            updateNote(note, NoteStepEnum.marked);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchReview(Long[] ids, Long annotatorId, Long userId) {
        List<Long> taskIds = new ArrayList<>();
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .in(NoteTask::getNoteId, CollUtil.newArrayList(ids))
                .in(NoteTask::getStep, CollUtil.newArrayList(NoteStepEnum.marked.getCode(),
                        NoteStepEnum.reviewing.getCode(), NoteStepEnum.corrected.getCode()));
        List<NoteTask> noteTasks = noteTaskDao.selectList(taskWrapper);
        if (CollUtil.isEmpty(noteTasks)) {
            return;
        }
        Date currentDate = new Date();
        for (NoteTask noteTask : noteTasks) {
            noteTask.setStep(NoteStepEnum.reviewing.getCode());
            if (noteTask.getAnnotator().equals(annotatorId)) {
                noteTask.setMaster(MasterEnum.master.getValue());
                taskIds.add(noteTask.getTaskId());
            }
            noteTask.setAuditor(userId);
            if (noteTask.getAuditStartTime() == null) {
                noteTask.setAuditStartTime(currentDate);
            }
            noteTaskDao.updateById(noteTask);
        }
        for (Long taskId : taskIds) {
            submitAuditorTask(taskId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTask(Long roleId, Long taskId, Long userId) {
        Date currentDate = new Date();

        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getTaskId, taskId);
        NoteTask task = noteTaskDao.selectOne(taskWrapper);

        // 取消标注
        if (RoleEnum.annotator.getId() == roleId) {
            if (!NoteStepEnum.noting.getCode().equals(task.getStep())) {
                throw new RRException("当前任务状态不是标注中");
            }
            if (!task.getAnnotator().equals(userId)) {
                throw new RRException("您没有权限取消该文章");
            }
            // 删除操作的数据
            entityRepository.deleteByTaskId(taskId);
            attributesRepository.deleteByTaskId(taskId);
            relationshipRepository.deleteByTaskId(taskId);
            // 删除讨论区记录
            commentService.deleteCommentsByTaskId(taskId);
            // 重置文章状态
            noteTaskDao.deleteById(taskId);
            // 重置Note
            LambdaQueryWrapper<Note> noteWrapper = Wrappers.<Note>lambdaQuery()
                    .eq(Note::getNoteId, task.getNoteId())
                    .eq(Note::getStep, NoteStepEnum.noting.getCode());
            Note note = noteDao.selectOne(noteWrapper);
            if (note == null) {
                throw new RRException("Note和NoteTask状态不一致，请联系管理员");
            }
            note.setPullCount(note.getPullCount() - 1);
            note.setUpdateTime(currentDate);
            if (note.getPullCount() == 0) {
                note.setStep(NoteStepEnum.unmarked.getCode());
            }
            noteDao.updateById(note);
        }
        // 取消审核
        if (RoleEnum.auditor.getId() == roleId) {
            if (!NoteStepEnum.reviewing.getCode().equals(task.getStep())) {
                throw new RRException("当前任务状态不是审核中");
            }
            if (!task.getAuditor().equals(userId)) {
                throw new RRException("您没有权限取消该文章");
            }

            LambdaQueryWrapper<NoteTask> allTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, task.getNoteId());

            List<NoteTask> allTask = noteTaskDao.selectList(allTaskWrapper);

            for (NoteTask noteTask : allTask) {
                // 删除审核员操作过的数据
                final Set<String> removeEntityIds = entityRepository.deleteByTaskIdAndAuditorId(noteTask.getTaskId(), userId);
//                attributesRepository.deleteByTaskIdAndAuditorId(noteTask.getTaskId(), userId);
                attributesRepository.deleteByAttributeIdInFact(removeEntityIds);
                relationshipRepository.deleteByTaskIdAndAuditorId(noteTask.getTaskId(), userId);
                noteTaskDao.update(noteTask, Wrappers.<NoteTask>lambdaUpdate()
                        .eq(NoteTask::getTaskId, noteTask.getTaskId())
                        .set(NoteTask::getStep, NoteStepEnum.marked.getCode())
                        .set(NoteTask::getUpdateTime, currentDate)
                        .set(NoteTask::getAuditEndTime, null)
                        .set(NoteTask::getAuditor, null)
                        .set(NoteTask::getCorrectRate, null)
                        .set(NoteTask::getRepulseMsg, null)
                        .set(NoteTask::getAuditStartTime, null));
            }

            // 重置Note
            LambdaQueryWrapper<Note> noteWrapper = Wrappers.<Note>lambdaQuery()
                    .eq(Note::getNoteId, task.getNoteId())
                    .eq(Note::getStep, NoteStepEnum.reviewing.getCode());
            Note note = noteDao.selectOne(noteWrapper);
            if (note == null) {
                throw new RRException("Note和NoteTask状态不一致，请联系管理员");
            }
            note.setUpdateTime(currentDate);
            note.setStep(NoteStepEnum.marked.getCode());
            noteDao.updateById(note);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAuditorTask(Long taskId) {
        if (taskId == null) {
            throw new RRException("任务ID不能为空");
        }
        NoteTask noteTask = noteTaskDao.selectById(taskId);

        // 把其他人的文章也一同审核通过
        LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, noteTask.getNoteId())
                .in(NoteTask::getStep, CollUtil.newArrayList(NoteStepEnum.reviewing.getCode(),
                        NoteStepEnum.corrected.getCode()))
                .orderByDesc(NoteTask::getMaster);

        List<NoteTask> tasks = noteTaskDao.selectList(otherTaskWrapper);

        if (CollUtil.isEmpty(tasks)) {
            throw new RRException("未查询到相关任务");
        }

        if (tasks.size() >= 2 && tasks.get(0).getMaster() != MasterEnum.master.getValue()) {
            throw new RRException("请选择某一个标注员的标注结果为蓝本");
        }

        NoteTask masterTask = tasks.get(0);

        // 蓝本标注员标注正确的 实体数、属性数和关系数
        long entityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long attrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long relationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
        long annoNum = entityNum + attrNum + relationNum;

        // 审核员新标注的
        long auditorEntityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
        long auditorAttrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
        long auditorRelationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
        // 审核员修改和删除标注员的
        long auditorUpdateAnnoEntityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);
        long auditorUpdateAttrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);
        long auditorUpdateRelationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);

        // 标注员标注正确的
        masterTask.setCorrectTotal(annoNum);
        // 标注员标注错误的
        masterTask.setErrorTotal(auditorUpdateAnnoEntityNum + auditorUpdateAttrNum + auditorUpdateRelationNum);
        // 标注员漏标的
        masterTask.setMissTotal(auditorEntityNum + auditorAttrNum + auditorRelationNum);
        // 总数 (标注员标对的 + 审核员新标的 + 审核员删除和修改标注员的)
        long totalCount = masterTask.getCorrectTotal() + masterTask.getErrorTotal() + masterTask.getMissTotal();

        masterTask.setCorrectEntity(entityNum);
        masterTask.setCorrectAttr(attrNum);
        masterTask.setCorrectRelation(relationNum);

        if (totalCount != 0) {
            masterTask.setCorrectRate((float) NumberUtil.div(masterTask.getCorrectTotal().intValue(), (int) totalCount, 4) * 100);
        } else {
            // 当蓝本标注员和审核员的标注结果都为空时，认为正确率为100%
            masterTask.setCorrectRate(100.0f);
        }

        Date currentDate = new Date();

        // 查询蓝本的总标注结果
        List<String> masterEntityMd5 = emptyColl(entityRepository.findDistinctMd5(masterTask.getTaskId()));
        List<String> masterAttrMd5 = emptyColl(attributesRepository.findTwoMd5(masterTask.getTaskId(), null));
        List<String> masterRelationMd5 = emptyColl(relationshipRepository.findDistinctMd5(masterTask.getTaskId()));

        // 蓝本中最终正确总的数量
        long masterTotal = masterEntityMd5.size() + masterAttrMd5.size() + masterRelationMd5.size();

        masterTask.setNeedTotal(masterTotal);

        final List<Long> taskIds = new ArrayList<>();
        for (NoteTask task : tasks) {
            if (!task.getTaskId().equals(masterTask.getTaskId())) {
                task.setNeedTotal(masterTotal);

                // 查询当前标注员的标注结果
                List<String> entityMd5 = emptyColl(entityRepository.findMd5ByUser(task.getTaskId(), task.getAnnotator()));
                List<String> attrContentMd5 = emptyColl(attributesRepository.findTwoMd5(task.getTaskId(), task.getAnnotator()));
                List<String> relationMd5 = emptyColl(relationshipRepository.findMd5ByUser(task.getTaskId(), task.getAnnotator()));

                // 当前标注员的标注结果总数
                long currentTotal = entityMd5.size() + attrContentMd5.size() + relationMd5.size();

                // 取俩个集合的交集
                if (CollUtil.isNotEmpty(masterEntityMd5)) {
                    task.setCorrectEntity((long) CollUtil.intersectionDistinct(entityMd5, masterEntityMd5).size());
                } else {
                    task.setCorrectEntity(0L);
                }
                if (CollUtil.isNotEmpty(masterAttrMd5)) {
                    task.setCorrectAttr((long) CollUtil.intersectionDistinct(attrContentMd5, masterAttrMd5).size());
                } else {
                    task.setCorrectAttr(0L);
                }
                if (CollUtil.isNotEmpty(masterRelationMd5)) {
                    task.setCorrectRelation((long) CollUtil.intersectionDistinct(relationMd5, masterRelationMd5).size());
                } else {
                    task.setCorrectRelation(0L);
                }

                // 当前标注员标注正确数量
                long correctNum = task.getCorrectEntity() + task.getCorrectAttr() + task.getCorrectRelation();
                task.setCorrectTotal(correctNum);
                // 当前标注员标错的数量
                long errNum = currentTotal - correctNum;
                task.setErrorTotal(errNum);
                // 当前标注员未标的数量
                long noMarkNum = masterTotal - correctNum;
                task.setMissTotal(noMarkNum);

                long total = correctNum + errNum + noMarkNum;

                if (total != 0 && masterTotal != 0) {
                    task.setCorrectRate((float) NumberUtil.div(correctNum, total, 4) * 100);
                } else {
                    // 当蓝本标注员和当前标注员的标注结果都为空时，认为正确率为100%
                    // 这种情况下两者都没有标注，应该视为一致
                    task.setCorrectRate(100.0f);
                }
            }
            task.setAuditor(masterTask.getAuditor());
            task.setAuditEndTime(currentDate);
            taskIds.add(task.getTaskId());
            updateTask(task, NoteStepEnum.reviewed);
        }

        // 重新审核提交时，清空task标注的质疑人
        resetDoubters(taskIds);

        Note note = noteDao.selectById(noteTask.getNoteId());
        note.setInvalid(masterTask.getInvalid());
        updateNote(note, NoteStepEnum.reviewed);
    }

    private void resetDoubters(List<Long> taskIds) {
        entityRepository.resetDoubters(taskIds);
    }

    public void fixSubmitData() {
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode())
                .eq(NoteTask::getMaster, MasterEnum.master.getValue());

        List<NoteTask> tasks = noteTaskDao.selectList(taskWrapper);
        for (NoteTask masterTask : tasks) {
            // 蓝本标注员标注正确的 实体数、属性数和关系数
            long entityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
            long attrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
            long relationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), null, false);
            long annoNum = entityNum + attrNum + relationNum;

            // 审核员新标注的
            long auditorEntityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
            long auditorAttrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
            long auditorRelationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), null, masterTask.getAuditor(), false);
            // 审核员修改和删除标注员的
            long auditorUpdateAnnoEntityNum = entityRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);
            long auditorUpdateAttrNum = attributesRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);
            long auditorUpdateRelationNum = relationshipRepository.countAnnoNum(masterTask.getTaskId(), masterTask.getAnnotator(), masterTask.getAuditor(), null);

            // 标注员标注正确的
            masterTask.setCorrectTotal(annoNum);
            // 标注员标注错误的
            masterTask.setErrorTotal(auditorUpdateAnnoEntityNum + auditorUpdateAttrNum + auditorUpdateRelationNum);
            // 标注员漏标的
            masterTask.setMissTotal(auditorEntityNum + auditorAttrNum + auditorRelationNum);
            // 总数 (标注员标对的 + 审核员新标的 + 审核员删除和修改标注员的)
            long totalCount = masterTask.getCorrectTotal() + masterTask.getErrorTotal() + masterTask.getMissTotal();

            masterTask.setCorrectEntity(entityNum);
            masterTask.setCorrectAttr(attrNum);
            masterTask.setCorrectRelation(relationNum);

            if (totalCount != 0) {
                masterTask.setCorrectRate((float) NumberUtil.div(masterTask.getCorrectTotal().intValue(), (int) totalCount, 4) * 100);
            }

            // 查询蓝本的总标注结果
            List<String> masterEntityMd5 = emptyColl(entityRepository.findDistinctMd5(masterTask.getTaskId()));
            List<String> masterAttrMd5 = emptyColl(attributesRepository.findTwoMd5(masterTask.getTaskId(), null));
            List<String> masterRelationMd5 = emptyColl(relationshipRepository.findDistinctMd5(masterTask.getTaskId()));

            // 蓝本中最终正确总的数量
            long masterTotal = masterEntityMd5.size() + masterAttrMd5.size() + masterRelationMd5.size();

            masterTask.setNeedTotal(masterTotal);

            noteTaskDao.updateById(masterTask);

            LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, masterTask.getNoteId())
                    .ne(NoteTask::getTaskId, masterTask.getTaskId())
                    .eq(NoteTask::getMaster, MasterEnum.not_master.getValue());

            List<NoteTask> otherTaskList = noteTaskDao.selectList(otherTaskWrapper);

            for (NoteTask task : otherTaskList) {

                task.setNeedTotal(masterTotal);

                // 查询当前标注员的标注结果
                List<String> entityMd5 = emptyColl(entityRepository.findMd5ByUser(task.getTaskId(), task.getAnnotator()));
                List<String> attrContentMd5 = emptyColl(attributesRepository.findTwoMd5(task.getTaskId(), task.getAnnotator()));
                List<String> relationMd5 = emptyColl(relationshipRepository.findMd5ByUser(task.getTaskId(), task.getAnnotator()));

                // 取俩个集合的交集
                if (CollUtil.isNotEmpty(masterEntityMd5)) {
                    task.setCorrectEntity((long) CollUtil.intersectionDistinct(entityMd5, masterEntityMd5).size());
                } else {
                    task.setCorrectEntity(0L);
                }
                if (CollUtil.isNotEmpty(masterAttrMd5)) {
                    task.setCorrectAttr((long) CollUtil.intersectionDistinct(attrContentMd5, masterAttrMd5).size());
                } else {
                    task.setCorrectAttr(0L);
                }
                if (CollUtil.isNotEmpty(masterRelationMd5)) {
                    task.setCorrectRelation((long) CollUtil.intersectionDistinct(relationMd5, masterRelationMd5).size());
                } else {
                    task.setCorrectRelation(0L);
                }

                // 当前标注员标注正确数量
                long correctNum = task.getCorrectEntity() + task.getCorrectAttr() + task.getCorrectRelation();
                task.setCorrectTotal(correctNum);
                // 当前标注员标错的数量
                long errNum = entityMd5.size() + attrContentMd5.size() + relationMd5.size() - correctNum;
                task.setErrorTotal(errNum);
                // 当前标注员未标的数量
                long noMarkNum = masterTotal - correctNum;
                task.setMissTotal(noMarkNum);

                // 标注正确数  /  标注正确数 + 漏标数 + 标错数
                long total = correctNum + errNum + noMarkNum;

                if (total != 0) {
                    task.setCorrectRate((float) NumberUtil.div(correctNum, total, 4) * 100);
                } else {
                    // 当蓝本标注员和当前标注员的标注结果都为空时，认为正确率为100%
                    // 这种情况下两者都没有标注，应该视为一致
                    task.setCorrectRate(100.0f);
                }
                noteTaskDao.updateById(task);
            }
        }
    }

    private List<String> emptyColl(List<String> input) {
        if (input == null) {
            return new ArrayList<>();
        }
        return input;
    }

    private List<String> getTaskMd5List(Long taskId) {
        List<String> entityMd5 = entityRepository.findDistinctMd5(taskId);
        if (CollUtil.isEmpty(entityMd5)) {
            return null;
        }
        List<String> relationMd5 = relationshipRepository.findDistinctMd5(taskId);
        if (CollUtil.isNotEmpty(relationMd5)) {
            entityMd5.addAll(relationMd5);
        }
        List<String> attrContentMd5 = attributesRepository.findTwoMd5(taskId, null);
        if (CollUtil.isNotEmpty(attrContentMd5)) {
            entityMd5.addAll(attrContentMd5);
        }
        return entityMd5;
    }

    /**
     * 分配新的文章
     *
     * @return note id
     */
    private Note getNewNote(Long batchId, Long userId, int markRounds) {
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .select(NoteTask::getNoteId)
                .eq(NoteTask::getBatchId, batchId)
                .eq(NoteTask::getAnnotator, userId);
        List<NoteTask> tasks = noteTaskDao.selectList(taskWrapper);

        LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .lt(Note::getPullCount, markRounds)
                .in(Note::getStep, CollUtil.newArrayList(NoteStepEnum.unmarked.getCode(), NoteStepEnum.noting.getCode()));

        // 排除历史标注过的NoteID
        if (CollUtil.isNotEmpty(tasks)) {
            Set<Long> noteIdSet = tasks.stream().map(NoteTask::getNoteId).collect(Collectors.toSet());
            wrapper.notIn(Note::getNoteId, noteIdSet);
        }
        wrapper.orderByDesc(Note::getStep)
                .orderByAsc(Note::getWordsCount)
                .orderByAsc(Note::getNoteId)
                .last(LIMIT_ONE);
        // 得到分配的文章
        Note note = noteDao.selectOne(wrapper);
        return note;
    }

    /**
     * 分配新的文章，尝试避免标注员组合高频重合
     * <p>
     * 用于找到重合度最低的标注文章组合：
     * 计算每篇可分配文章的"重合度分数"（当前标注员与已标注该文章的标注员共同标注过的文章数量）
     * 按重合度排序，选择重合度最低的几篇文章作为候选
     * 从候选文章中随机选择一篇，避免标注员组合高频重合
     * <p>
     * 首先尝试使用随机双盲方式分配文章
     * 如果找不到合适的文章，才回退到原来的分配方式
     * <p>
     * 随机双盲分配方式的优点：
     * 避免标注员总是被分配到同一组人一起标注文章
     * 能够随机化标注组合，确保标注质量和多样性
     * 当数据量足够大时，能够均匀分布标注员组合，提高标注结果的可靠性
     * 系统会自动计算并选择重合度最低的文章分配给标注员，达到随机双盲标注的效果。
     *
     * @param batchId    批次ID
     * @param userId     当前标注员ID
     * @param markRounds 标注轮数
     * @return 分配的文章
     */
    private Note getNewNoteWithRandomCombination(Long batchId, Long userId, int markRounds) {
        // 1. 获取当前标注员已标注的文章ID列表
        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .select(NoteTask::getNoteId)
                .eq(NoteTask::getBatchId, batchId)
                .eq(NoteTask::getAnnotator, userId);

        List<NoteTask> userTasks = noteTaskDao.selectList(taskWrapper);

        Set<Long> userNoteIds = CollUtil.isEmpty(userTasks) ?
                new HashSet<>() :
                userTasks.stream().map(NoteTask::getNoteId).collect(Collectors.toSet());

        // 2. 获取该批次下所有未标注完成的文章
        LambdaQueryWrapper<Note> noteWrapper = Wrappers.<Note>lambdaQuery()
                .eq(Note::getBatchId, batchId)
                .lt(Note::getPullCount, markRounds)
                .in(Note::getStep, CollUtil.newArrayList(NoteStepEnum.unmarked.getCode(), NoteStepEnum.noting.getCode()));

        // 排除当前标注员已标注过的文章
        if (!userNoteIds.isEmpty()) {
            noteWrapper.notIn(Note::getNoteId, userNoteIds);
        }

        List<Note> availableNotes = noteDao.selectList(noteWrapper);
        if (CollUtil.isEmpty(availableNotes)) {
            return null; // 没有可分配的文章
        }

        // 3. 计算每篇文章的重合度
        // 重合度：当前标注员与已标注该文章的标注员共同标注过的文章数量
        Map<Long, Integer> noteOverlapScores = new HashMap<>(availableNotes.size());

        for (Note note : availableNotes) {
            // 获取已经标注过这篇文章的标注员
            LambdaQueryWrapper<NoteTask> noteTasksWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .select(NoteTask::getAnnotator)
                    .eq(NoteTask::getNoteId, note.getNoteId());
            List<NoteTask> noteTasks = noteTaskDao.selectList(noteTasksWrapper);

            if (CollUtil.isEmpty(noteTasks)) {
                // 没有标注员标注过，重合度为0
                noteOverlapScores.put(note.getNoteId(), 0);
                continue;
            }

            // 获取已标注过这篇文章的标注员ID列表
            Set<Long> annotatorIds = noteTasks.stream()
                    .map(NoteTask::getAnnotator)
                    .collect(Collectors.toSet());

            // 计算当前标注员与这些标注员的重合度
            int overlapScore = 0;
            for (Long annotatorId : annotatorIds) {
                // 获取该标注员和当前标注员共同标注过的文章数量
                LambdaQueryWrapper<NoteTask> overlapWrapper = Wrappers.<NoteTask>lambdaQuery()
                        .select(NoteTask::getNoteId)
                        .eq(NoteTask::getBatchId, batchId)
                        .eq(NoteTask::getAnnotator, annotatorId);
                List<NoteTask> annotatorTasks = noteTaskDao.selectList(overlapWrapper);

                if (CollUtil.isNotEmpty(annotatorTasks)) {
                    Set<Long> annotatorNoteIds = annotatorTasks.stream()
                            .map(NoteTask::getNoteId)
                            .collect(Collectors.toSet());
                    // 计算交集大小，即共同标注过的文章数量
                    Set<Long> intersection = new HashSet<>(userNoteIds);
                    intersection.retainAll(annotatorNoteIds);
                    overlapScore += intersection.size();
                }
            }

            noteOverlapScores.put(note.getNoteId(), overlapScore);
        }

        // 4. 选择重合度最低的几篇文章，然后随机选择一篇
        // 按重合度排序
        List<Map.Entry<Long, Integer>> sortedNotes = noteOverlapScores.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toList());

        // 从所有候选标注员中先选出重合度最低的前3名作为"候选池",避免贪心算法的局限性：如果每次只选择一个最优解（重合度最低的那一个），可能导致整体分配不够优化。通过建立候选池，可以有更多选择空间。
        int candidateCount = Math.min(3, sortedNotes.size());
        List<Long> candidateNoteIds = sortedNotes.subList(0, candidateCount).stream()
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 从候选文章中随机选择一篇
        Long selectedNoteId = candidateNoteIds.get(new Random().nextInt(candidateNoteIds.size()));

        // 5. 返回选中的文章
        return availableNotes.stream()
                .filter(note -> note.getNoteId().equals(selectedNoteId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 分配标注员任务
     *
     * @return 标注任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public NoteTask assignAnnotatorTask(AssignTaskDTO dto) {
        Long batchId = dto.getBatchId();
        Long userId = dto.getUserId();

        NoteTask task;
        Date currentDate = new Date();

        // 查询当前项目的标注轮数
        Batch batch = batchDao.selectById(batchId);

        Project project = getProjectByBatchId(batchId);
        int markRounds = project.getMarkRounds();

        int mode = batch.getMode();

        Note note = null;
        // 均衡分配
        if (mode == 1) {
            // 直接标注以前分配好的数据
            Long roleId = dto.getRoleId();
            task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.noting, null);

            // 如果没有标注中的数据，则找被打回的数据
            if (task == null) {
                task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.repulse, null);
            }
            if (task != null && task.getAnnoStartTime() == null) {
                task.setAnnoStartTime(currentDate);
                noteTaskDao.updateById(task);
            }
            return task;
        } else {
            List<ProjectUser> annotators = null;
            if (markRounds == 2) {
                // 获取项目的标注员列表
                annotators = projectUserService.list(Wrappers.<ProjectUser>lambdaQuery()
                        .eq(ProjectUser::getProjectId, project.getProjectId())
                        .eq(ProjectUser::getRoleId, RoleEnum.annotator.getId()));
            }

            // 标注员人数等于2，标注轮数等于2，不执行随机双盲
            if (!(CollUtil.isNotEmpty(annotators) && annotators.size() == markRounds)) {
                // 使用随机双盲分配方式，避免标注员重合
                note = getNewNoteWithRandomCombination(batchId, userId, markRounds);
            }

            // 如果随机分配没找到合适的文章，则使用原来的方式
            if (note == null) {
                note = getNewNote(batchId, userId, markRounds);
            }
        }

        // 无待标注文章可分配了，则再看下是否有标注中或者被打回的任务
        if (note == null) {
            // 查询是否有遗留的未标注完的记录，分配没标注完的历史数据(标注中数据)
            Long roleId = dto.getRoleId();
            task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.noting, null);

            // 如果没有标注中的数据，则找被打回的数据
            if (task == null) {
                task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.repulse, null);
            }
            return task;
        }

        LambdaQueryWrapper<NoteTask> oldTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getBatchId, batchId)
                .eq(NoteTask::getNoteId, note.getNoteId())
                .eq(NoteTask::getAnnotator, userId)
                .last(LIMIT_ONE);
        NoteTask oldTask = noteTaskDao.selectOne(oldTaskWrapper);
        if (oldTask != null) {
            return oldTask;
        }

        // 新增当前分配的任务
        task = new NoteTask();
        task.setProjectId(batch.getProjectId());
        task.setBatchId(batchId);
        task.setNoteId(note.getNoteId());
        task.setStep(NoteStepEnum.noting.getCode());
        task.setAnnotator(userId);
        task.setAnnoStartTime(currentDate);
        task.setCreateTime(currentDate);
        task.setUpdateTime(currentDate);
        noteTaskDao.insert(task);

        note.setPullCount(note.getPullCount() + 1);
        updateNote(note, NoteStepEnum.noting);
        return task;
    }

    /**
     * 分配审核员任务
     *
     * @return 审核任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public NoteTask assignAuditorTask(AssignTaskDTO dto) {
        Long batchId = dto.getBatchId();
        Long userId = dto.getUserId();
//        Long roleId = dto.getRoleId();
        Long annotatorId = dto.getAnnotatorId();

        if (annotatorId == null) {
            throw new RRException("分配审核任务出错，被审核标注员不能为空");
        }
        NoteTask task;
        Date currentDate = new Date();
        // 先查询是否有遗留的审核中的记录(暂时取消该功能)
//        task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.reviewing, annotatorId);
//
//        // 查询该标注员是否有被打回然后修正重新提交的数据
//        if (task == null) {
//            task = findTaskByStatus(batchId, userId, roleId, NoteStepEnum.corrected, annotatorId);
//            // 把其他人被打回的也全部设置回审核中
//            if (task != null) {
//                Note note = noteDao.selectById(task.getNoteId());
//                if (note.getStep().equals(NoteStepEnum.marked.getCode())) {
//                    // 把别人的相同文章一起置为审核中 或 已修正
//                    LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
//                            .eq(NoteTask::getNoteId, task.getNoteId());
//                    List<NoteTask> tasks = noteTaskDao.selectList(otherTaskWrapper);
//                    // 集中修改这个note的状态
//                    for (NoteTask noteTask : tasks) {
//                        noteTask.setAuditor(userId);
//                        noteTask.setAuditStartTime(currentDate);
//                        if (task.getTaskId().equals(noteTask.getTaskId())) {
//                            task = noteTask;
//                        }
//                        updateTask(noteTask, NoteStepEnum.reviewing);
//                    }
//                    updateNote(note, NoteStepEnum.reviewing);
//                }
//            }
//        }
        // 分配新审核任务
        task = noteTaskDao.assignAuditorTask(batchId, annotatorId, userId);

        // 无文章可配置了
        if (task == null) {
            return null;
        }
        // 把别人的相同文章一起置为审核中
        LambdaQueryWrapper<NoteTask> otherTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, task.getNoteId())
                .eq(NoteTask::getStep, NoteStepEnum.marked.getCode());
        List<NoteTask> tasks = noteTaskDao.selectList(otherTaskWrapper);
        tasks.add(task);
        // 集中修改这个note的状态
        for (NoteTask noteTask : tasks) {
            noteTask.setAuditor(userId);
            noteTask.setAuditStartTime(currentDate);
            updateTask(noteTask, NoteStepEnum.reviewing);
        }
        Note note = noteDao.selectById(task.getNoteId());
        updateNote(note, NoteStepEnum.reviewing);
        return task;
    }

    @Override
    public List<AnnoUserVO> findAnnoByNoteId(Long noteId, Long userId) {
        if (noteId == null) {
            throw new RRException("NoteId不能为空");
        }
        LambdaQueryWrapper<NoteTask> wrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, noteId)
                .eq(NoteTask::getAuditor, userId);
        List<NoteTask> tasks = noteTaskDao.selectList(wrapper);
        if (CollUtil.isEmpty(tasks)) {
            throw new RRException("查询不到相关标注员，请检查当前用户是否拥有权限！");
        }
        List<AnnoUserVO> userVo = new ArrayList<>();
        for (NoteTask task : tasks) {
            AnnoUserVO annoUserVO = new AnnoUserVO();
            Long annotator = task.getAnnotator();
            SysUserEntity user = userDao.selectById(annotator);
            annoUserVO.setUserId(annotator);
            annoUserVO.setUsername(user.getUsername());
            annoUserVO.setTaskId(task.getTaskId());
            annoUserVO.setRepulseMsg(task.getRepulseMsg());
            userVo.add(annoUserVO);
        }
        return userVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repulse(List<AnnoUserVO> user, Long userId) {
        Long noteId = null;
        Set<Long> taskSet = new HashSet<>();
        for (AnnoUserVO vo : user) {
            Long taskId = vo.getTaskId();
            taskSet.add(taskId);
            NoteTask noteTask = noteTaskDao.selectById(taskId);
            if (!noteTask.getAuditor().equals(userId)) {
                throw new RRException("非法打回，您没有权限");
            }
            if (!noteTask.getStep().equals(NoteStepEnum.reviewing.getCode())) {
                throw new RRException("当前非审核中阶段，禁止操作");
            }
            noteId = noteTask.getNoteId();
            if (vo.getRepulseMsg() == null) {
                vo.setRepulseMsg("");
            }
            noteTask.setRepulseMsg(vo.getRepulseMsg());
            updateTask(noteTask, NoteStepEnum.repulse);
        }
        LambdaQueryWrapper<NoteTask> noteTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getNoteId, noteId)
                .notIn(NoteTask::getTaskId, taskSet)
                .eq(NoteTask::getStep, NoteStepEnum.reviewing.getCode());
        List<NoteTask> noteTaskList = noteTaskDao.selectList(noteTaskWrapper);
        if (CollUtil.isNotEmpty(noteTaskList)) {
            for (NoteTask noteTask : noteTaskList) {
                updateTask(noteTask, NoteStepEnum.marked);
            }
        }
        if (noteId != null) {
            Note note = noteDao.selectById(noteId);
            updateNote(note, NoteStepEnum.noting);
        }
    }

    @Override
    public List<Document.InfoDTO> findMetadataByNoteId(String noteId) {
        Note note = noteDao.selectById(noteId);
        String documentId = note.getDocumentId();
        Document document = documentRepository.findById(documentId).orElseThrow(() -> new RRException("document未找到"));
        return document.getInfo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalid(Long taskId, Integer invalid, Long userId) {
        if (taskId == null) {
            throw new RRException("任务ID不能为空");
        }
        if (invalid == null) {
            throw new RRException("操作的状态不能为空");
        }
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("未查询到该任务");
        }
        if (!userId.equals(noteTask.getAnnotator())) {
            throw new RRException("您没有权限操作");
        }
        noteTask.setInvalid(invalid);
        noteTask.setUpdateTime(new Date());
        noteTaskDao.updateById(noteTask);
    }

    @Override
    public void rework(Long taskId, Long roleId, Long userId) {
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("未查询到该文章");
        }
        Long noteId = noteTask.getNoteId();
        if (RoleEnum.annotator.getId() == roleId) {
            if (!noteTask.getAnnotator().equals(userId)) {
                throw new RRException("非法操作，您没有权限");
            }
            if (!noteTask.getStep().equals(NoteStepEnum.marked.getCode())) {
                throw new RRException("当前非待审核阶段，禁止重新标注");
            }
            updateTask(noteTask, NoteStepEnum.noting);
            if (noteId != null) {
                Note note = noteDao.selectById(noteId);
                updateNote(note, NoteStepEnum.noting);
            }
        }
        if (RoleEnum.auditor.getId() == roleId) {
            // 如果不是当前标注员标注的文章，且不是自动审核的
            if (!noteTask.getAuditor().equals(userId) && noteTask.getAuditor() != 2) {
                throw new RRException("非法操作，您没有权限");
            }
            if (!noteTask.getStep().equals(NoteStepEnum.reviewed.getCode()) && noteTask.getAuditor() != 2) {
                throw new RRException("当前非已合格阶段，禁止重新审核");
            }
            LambdaQueryWrapper<NoteTask> noteTaskWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, noteId)
                    .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode());
            List<NoteTask> noteTaskList = noteTaskDao.selectList(noteTaskWrapper);
            if (CollUtil.isEmpty(noteTaskList)) {
                throw new RRException("未查到可恢复审核中的数据");
            }
            for (NoteTask task : noteTaskList) {
                task.setAuditor(userId);
                task.setAuditStartTime(new Date());
                task.setAuditEndTime(new Date());
                updateTask(task, NoteStepEnum.reviewing);
            }
            if (noteId != null) {
                Note note = noteDao.selectById(noteId);
                updateNote(note, NoteStepEnum.reviewing);
            }
        }
    }

    private void updateNote(Note note, NoteStepEnum stepEnum) {
        note.setStep(stepEnum.getCode());
        note.setUpdateTime(new Date());
        noteDao.updateById(note);
    }

    private void updateTask(NoteTask task, NoteStepEnum stepEnum) {
        task.setStep(stepEnum.getCode());
        task.setUpdateTime(new Date());
        noteTaskDao.updateById(task);
    }

    private NoteTask findTaskByStatus(Long batchId, Long userId, Long roleId, NoteStepEnum stepEnum, Long annotatorId) {
        LambdaQueryWrapper<NoteTask> wrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getBatchId, batchId)
                .eq(annotatorId != null, NoteTask::getAnnotator, annotatorId)
                .eq(roleId == RoleEnum.annotator.getId(), NoteTask::getAnnotator, userId)
                .eq(roleId == RoleEnum.auditor.getId(), NoteTask::getAuditor, userId)
                .eq(NoteTask::getStep, stepEnum.getCode())
                .orderByDesc(NoteTask::getUpdateTime)
                .last(LIMIT_ONE);
        return noteTaskDao.selectOne(wrapper);
    }

    private Project getProjectByBatchId(Long batchId) {
        // 查询当前项目的标注轮数
        Batch batch = batchDao.selectById(batchId);
        if (batch == null) {
            throw new RRException("当前批次已删除", 501);
        }
        Long projectId = batch.getProjectId();
        return projectDao.selectById(projectId);
    }

    public void fixAutoAuditData() {

        LambdaQueryWrapper<NoteTask> taskWrapper = Wrappers.<NoteTask>lambdaQuery()
                .eq(NoteTask::getMaster, MasterEnum.master.getValue())
                .eq(NoteTask::getAuditor, AUTO_AUDITOR)
                .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode());
        List<NoteTask> masterTasks = noteTaskDao.selectList(taskWrapper);

        for (NoteTask masterTask : masterTasks) {

            String md5 = masterTask.getMd5();

            LambdaQueryWrapper<NoteTask> taskIdWrapper = Wrappers.<NoteTask>lambdaQuery()
                    .eq(NoteTask::getNoteId, masterTask.getNoteId())
                    .eq(NoteTask::getAuditor, AUTO_AUDITOR)
                    .eq(NoteTask::getStep, NoteStepEnum.reviewed.getCode());
            List<NoteTask> tasks = noteTaskDao.selectList(taskIdWrapper);

            for (NoteTask task : tasks) {
                String taskMd5 = task.getMd5();
                if (taskMd5 != null && !taskMd5.equals(md5)) {
                    return;
                }
                task.setCorrectTotal(masterTask.getCorrectTotal());
                task.setCorrectRate(100.00F);
                task.setCorrectEntity(masterTask.getCorrectEntity());
                task.setCorrectAttr(masterTask.getCorrectAttr());
                task.setCorrectRelation(masterTask.getCorrectRelation());
                // 自动审核，应标数等于标对数
                task.setNeedTotal(masterTask.getNeedTotal());
                noteTaskDao.updateById(task);
            }
        }

    }
}
