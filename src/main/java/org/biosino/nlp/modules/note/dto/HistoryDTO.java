package org.biosino.nlp.modules.note.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.nlp.modules.project.dto.BaseDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/4 11:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HistoryDTO extends BaseDTO {
    private Long noteId;

    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 批次ID，一个批次对应多篇标注文章
     */
    private Long batchId;
    /**
     * 文章ID
     */
    private String articleId;
    /**
     * 文章标题
     */
    private String articleName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    private Date lastUpdateTime;
    /**
     * 文章当前状态：0未标注，1标注中，2已标注，3审核中，4已审核
     */
    private Integer status;
    /**
     * 是否废弃[1.废弃， 0：未废弃]
     */
    private Integer invalid;
    private Long lockUser;
    private Long annotator;
    private Date annoStartTime;
    private Date annoEndTime;
    private Long auditor;
    private Date auditStartTime;
    private Date auditEndTime;
}
