package org.biosino.nlp.modules.note.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OpertionVO {
    private Long noteId;
    private Integer type;
    private String operation;
    private String msg;
    private String account;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
