package org.biosino.nlp.modules.note.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.project.entity.Project;

@Data
public class TaskDetailVO {
    private NoteTask task;
    @JsonIgnore
    private transient Project project;
    /**
     * 是否存在规范文件
     */
    private boolean hasSpecFile = false;
    private String projectName;
    private String batchName;
    private String articleId;
    private Integer step;
    private String status;
    private Integer taskStep;
    private String taskStatus;
    private Integer invalid;
    private String repulseMsg;
    private String documentSource;
}
