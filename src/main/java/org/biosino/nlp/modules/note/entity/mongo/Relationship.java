package org.biosino.nlp.modules.note.entity.mongo;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 关系标注
 *
 * <AUTHOR>
 * @date 2021-01-26 15:40
 */
@Data
@Document("m_relationship")
public class Relationship extends BaseMongo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Indexed
    @Field("note_id")
    private Long noteId;

    @Indexed
    @Field("project_id")
    private Long projectId;

    @Indexed
    @Field("batch_id")
    private Long batchId;

    @Field("task_id")
    private Long taskId;

    @Indexed
    @Field(name = "article_id")
    private String articleId;

    private String md5;

    @Field(name = "pattern_id")
    private Long patternId;

    private List<RelationItem> items;

    @Data
    public static class RelationItem implements Serializable {
        private String id;
        private Integer order;

        private List<String> subject;

        private Boolean negation;

        private List<String> annotations;

        private Long relation;

        private List<String> objects;

        public RelationItem(Integer order, List<String> subject, Boolean negation, List<String> annotations, Long relation, List<String> objects) {
            this.order = order;
            this.subject = subject;
            this.negation = negation;
            this.annotations = annotations;
            this.relation = relation;
            this.objects = objects;
        }

        public RelationItem() {
        }
    }

    @Field(name = "create_time")
    private Date createTime;

    /**
     * 标注员
     */
    @Field(name = "annotator_id")
    private Long annotatorId;

    /**
     * 审核员
     */
    @Field(name = "auditor_id")
    private Long auditorId;

    /**
     * 实体来源：self(自标注) 1、upload（预标注） 2、其他系统名称
     */
    private int source;

    /**
     * 用户导入的数据：不为空,为导入id的任务号，用户在系统标注的数据：为null
     */
    @Field(name = "import_log_id")
    private Long importLogId = null;

    /**
     * 审核状态，正确:true,错误:false
     */
    private Boolean correct = true;

}
