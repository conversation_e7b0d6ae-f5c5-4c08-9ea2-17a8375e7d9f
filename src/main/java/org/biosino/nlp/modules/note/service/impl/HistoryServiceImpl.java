package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.note.dao.HistoryDao;
import org.biosino.nlp.modules.note.dto.HistoryDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.HistoryService;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service("historyService")
public class HistoryServiceImpl extends ServiceImpl<HistoryDao, Note> implements HistoryService {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 标注员身份查询标注历史数据
     */
    @Override
    public PageUtils annotatorDataList(HistoryDTO dto) {
        if (dto == null || dto.getBatchId() == null) {
            throw new RRException("请选择批次");
        }
        IPage<Note> page = this.page(new Query<Note>().getPage(dto),
                Wrappers.<Note>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dto.getArticleId()), Note::getArticleId, dto.getArticleId())
                        .like(StrUtil.isNotBlank(dto.getArticleName()), Note::getArticleName, dto.getArticleName())
                        .eq(dto.getStatus() != null, Note::getStep, dto.getStatus())
                        .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId())
//                        .eq(dto.getAnnotator() != null, Note::getAnnotator, dto.getAnnotator())
                        .in(Note::getStep, Arrays.asList(NoteStepEnum.noting.getCode(), NoteStepEnum.marked.getCode(), NoteStepEnum.reviewing.getCode(), NoteStepEnum.reviewed.getCode()))
                        .orderByAsc(Note::getStep));
//                        .orderByDesc(Note::getAnnoEndTime));

        return new PageUtils(page);
    }

    /**
     * 审核员身份查询
     */
    @Override
    public PageUtils auditorDataList(HistoryDTO dto) {
        if (dto == null || dto.getBatchId() == null) {
            throw new RRException("请选择批次");
        }
        IPage<Note> page = this.page(new Query<Note>().getPage(dto),
                Wrappers.<Note>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dto.getArticleId()), Note::getArticleId, dto.getArticleId())
                        .like(StrUtil.isNotBlank(dto.getArticleName()), Note::getArticleName, dto.getArticleName())
//                        .eq(dto.getAnnotator() != null, Note::getAnnotator, dto.getAnnotator())
                        .eq(dto.getStatus() != null, Note::getStep, dto.getStatus())
                        .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId())
//                        .eq(dto.getAuditor() != null, Note::getAuditor, dto.getAuditor())
                        .in(Note::getStep, Arrays.asList(NoteStepEnum.reviewing.getCode(), NoteStepEnum.reviewed.getCode()))
                        .orderByAsc(Note::getStep));
//                        .orderByDesc(Note::getAuditEndTime)

        return new PageUtils(page);
    }

    /**
     * 项目管理员查询批次标注信息
     */
    @Override
    public PageUtils getProjectAdminDataList(HistoryDTO dto) {
        if (dto == null || dto.getBatchId() == null) {
            throw new RRException("请选择批次");
        }
        IPage<Note> page = this.page(new Query<Note>().getPage(dto),
                Wrappers.<Note>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dto.getArticleId()), Note::getArticleId, dto.getArticleId())
                        .like(StrUtil.isNotBlank(dto.getArticleName()), Note::getArticleName, dto.getArticleName())
//                        .eq(dto.getAnnotator() != null, Note::getAnnotator, dto.getAnnotator())
                        .eq(dto.getStatus() != null, Note::getStep, dto.getStatus())
                        .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId())
//                        .eq(dto.getAuditor() != null, Note::getAuditor, dto.getAuditor())
                        .orderByAsc(Note::getStep));
//                        .orderByDesc(Note::getAuditEndTime)

        return new PageUtils(page);
    }

    @Override
    public Map<Long, String> getAnnotator(HistoryDTO dto) {
        LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
//                .select(Note::getAnnotator)
                .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId());
        List<Note> list = this.list(wrapper);

        Map<Long, String> annotatorMap = new HashMap<>(16);
        list.stream().distinct().filter(Objects::nonNull).
                forEach(x -> {
//                    annotatorMap.put(x.getAnnotator(), sysUserService.findNameById(x.getAnnotator()));
                });
        return annotatorMap;
    }

    @Override
    public Map<Long, String> getAuditor(HistoryDTO dto) {
        LambdaQueryWrapper<Note> wrapper = Wrappers.<Note>lambdaQuery()
//                .select(Note::getAuditor)
                .eq(dto.getBatchId() != null, Note::getBatchId, dto.getBatchId());

        List<Note> list = this.list(wrapper);

        Map<Long, String> auditorMap = new HashMap<>(16);
        list.stream().distinct().filter(Objects::nonNull).forEach(x -> {
//            auditorMap.put(x.getAuditor(), sysUserService.findNameById(x.getAuditor()));
        });
        return auditorMap;
    }


}
