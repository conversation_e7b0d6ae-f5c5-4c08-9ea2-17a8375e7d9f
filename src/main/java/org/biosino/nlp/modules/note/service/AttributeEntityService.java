package org.biosino.nlp.modules.note.service;

import org.biosino.nlp.modules.note.dto.AttributeEntityDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.vo.AttrVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface AttributeEntityService {
    Map<String, Object> addAttributeEntity(AttributeEntityDTO dto, Long userId);

    List<AttrVO> findAllAttrByEntityLabelId(Long entityLabelId, String entityId);

    Map<String, Object> findAttrListByEntityId(String entityId);

    Map<String, Integer> countMapByEntityIds(Collection<String> entityIds);

    void addMd5(Attributes attribute);

    void deleteNotExistAttributesRelation();

}
