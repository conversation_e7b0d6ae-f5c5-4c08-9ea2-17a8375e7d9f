package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.springframework.data.domain.Page;

import java.util.*;

/**
 * <AUTHOR> @date 2020/12/10 18:51
 */
public interface EntityCustomRepository extends BaseLogicalDelete<Entity> {

    boolean findSubLabelByID(Long id);

    boolean findEntityByConceptIdAndProjectId(String conceptId, Long projectId);

    List<Entity> findByIds(final Collection<String> ids);

    Optional<Entity> findByUniqueid(final String uniqueid);

    /**
     * 查询位置重叠的所有标注信息
     */
    List<AnnoDTO.EntityInfo> findSameEntity(final Entity entity, final AnnoDTO.EntityInfo entityInfo);

    void deleteAllByNoteId(Long noteId);

    boolean existsByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(String md5, Long noteId, String articleId, int isAttr, int source);

    Optional<Entity> findFirstByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(String md5, Long noteId, String articleId, int isAttr, int source);

    long countByTaskIdAndMd5AndSource(Long noteTaskId, String md5, int source);

    List<Entity> findByTaskIdAndMd5AndSource(Long noteTaskId, String md5, int source);

    Page<Entity> queryPage(EntitySearchDTO dto);

    List<Long> getUserIdsByLabelId(Long labelId);

    List<Long> findAllPreSourceByArticleId(String articleId, Long projectId);

    /**
     * 查询自标注和预标注的数据
     */
    List<Entity> findDocumentArticle(Long taskId, Long noteId, PreSourceEnum sourceEnum, Long currImportLogId, String articleId, Long projectId);

    List<Entity> findDocumentArticle(Long taskId, Long noteId, PreSourceEnum sourceEnum, final boolean showAnnoOnly, final Long annotatorId, Long currImportLogId, String articleId, Long projectId);

    /**
     * 根据taskId和标注来源查询所有标注
     */
    List<Entity> findByTaskIdAndSource(Long taskId, PreSourceEnum sourceEnum);

    /**
     * 根据taskId获取noteId
     */
    Optional<Long> findNoteIdByTaskId(Long taskId);

    Map<String, Entity> findMd5ByTaskIdAndSource(Long taskId, PreSourceEnum sourceEnum);

    List<String> findDistinctMd5(Long taskId);

    List<String> findDistinctMd5ByLabelId(Long batchId, Long labelId, Collection<Long> masterTaskIds);

    long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted);

    List<String> findMd5ByUser(Long taskId, Long userId);

    long countByUserId(Long projectId, Long taskId, Integer isAttr, Long annotatorId, Long auditorId, Boolean deleted);

    List<Entity> findBatchEntities(String batchAnnotateId);

    Set<String> findAllDeleteIds(Set<String> ids);

    Optional<Entity> findDelById(String entityId, Long annotatorId);

    void deleteByImportLogId(Long id);

    List<Entity> findByImportLogId(Long importLogId);

    int countQueryByTaskId(Long taskId);

    void resetDoubters(List<Long> taskIds);

    Map<Long, Set<String>> findOtherSameEntityMd5(Long noteId, Set<String> md5Set);

    Set<String> deleteByTaskIdAndAuditorId(Long taskId, Long auditorId);

}
