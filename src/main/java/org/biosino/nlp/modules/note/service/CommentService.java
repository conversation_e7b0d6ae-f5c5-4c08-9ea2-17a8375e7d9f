package org.biosino.nlp.modules.note.service;

import org.biosino.nlp.modules.note.entity.mongo.Comment;

import java.util.List;

/**
 * Comment Service
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
public interface CommentService {

    /**
     * 创建提问
     *
     * @param comment 提问信息
     * @return 创建的提问
     */
    Comment createQuestion(Comment comment);

    /**
     * 回复提问
     *
     * @param commentId 提问ID
     * @param replyText 回复内容
     * @param replyId   回复人ID
     * @return 更新后的提问
     */
    Comment replyQuestion(String commentId, String replyText, Long replyId);

    /**
     * 根据任务ID查询提问列表
     *
     * @param taskId 任务ID
     * @return 提问列表
     */
    List<Comment> getQuestionsByTaskId(Long taskId);

    /**
     * 根据文书ID查询提问列表
     *
     * @param noteId 文书ID
     * @return 提问列表
     */
    List<Comment> getQuestionsByNoteId(Long noteId);

    /**
     * 更新提问状态
     *
     * @param commentId 提问ID
     * @param status    状态
     * @return 更新后的提问
     */
    Comment updateQuestionStatus(String commentId, Integer status);

    /**
     * 根据实体ID查找相关的m_attributes记录ID列表
     *
     * @param entityId 实体ID
     * @return m_attributes记录ID列表
     */
    List<String> findAttributeRecordIdsByEntityId(String entityId);

    /**
     * 根据m_attributes记录ID列表查找相关的实体ID列表
     *
     * @param attributeRecordIds m_attributes记录ID列表
     * @return 实体ID列表
     */
    List<String> findEntityIdsByAttributeRecords(String entityId, List<String> attributeRecordIds);

    /**
     * 处理提问列表，添加实体名称和用户名称等详细信息
     *
     * @param questions 原始提问列表
     * @return 处理后的提问列表
     */
    List<Comment> processQuestionsWithDetails(List<Comment> questions);

    /**
     * 根据任务ID查询未解决状态的评论数量
     *
     * @param taskId 任务ID
     * @return 未解决评论数量
     */
    long countUnresolvedQuestionsByTaskId(Long taskId);

    /**
     * 根据文书ID删除相关的讨论记录
     *
     * @param noteId 文书ID
     */
    void deleteCommentsByNoteId(Long noteId);

    /**
     * 根据文书ID列表批量删除相关的讨论记录
     *
     * @param noteIds 文书ID列表
     */
    void deleteCommentsByNoteIds(List<Long> noteIds);

    /**
     * 根据批次ID删除相关的讨论记录
     *
     * @param batchId 批次ID
     */
    void deleteCommentsByBatchId(Long batchId);

    /**
     * 根据任务ID删除相关的讨论记录
     *
     * @param taskId 任务ID
     */
    void deleteCommentsByTaskId(Long taskId);
}
