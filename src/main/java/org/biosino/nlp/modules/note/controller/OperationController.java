package org.biosino.nlp.modules.note.controller;

import io.swagger.annotations.Api;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.note.dto.OperationDTO;
import org.biosino.nlp.modules.note.enums.EntityEnum;
import org.biosino.nlp.modules.note.enums.OptionEnum;
import org.biosino.nlp.modules.note.service.OpertionService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 标注操作日志控制层
 *
 * <AUTHOR>
 */
@RestController
@Api("标注操作日志接口")
@RequestMapping("/operation")
public class OperationController extends AbstractController {

    @Autowired
    private OpertionService opertionService;

    /**
     * 当前文章操作日志
     */
    @GetMapping("/getDocument")
    public R currentArticleLog(OperationDTO dto) {
        if (dto.getNoteId() == null) {
            return R.error("Note ID不能为空");
        }
        PageUtils page = opertionService.queryPage(dto);
        Map<Long, String> account = opertionService.getAccount(dto);
        Map<Integer, String> optionEnum = OptionEnum.toMap();
        Map<Integer, String> entityEnum = EntityEnum.toMap();
        return R.ok().put("page", page).put("optionEnum", optionEnum).put("entityEnum", entityEnum).put("account", account);
    }
}
