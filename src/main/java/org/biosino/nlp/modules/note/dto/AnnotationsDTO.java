package org.biosino.nlp.modules.note.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AnnotationsDTO implements Serializable {
    private Integer start;
    private Integer end;
    private String label;
    private String textId;
    private String uniqueid;
    private String annoid;
    private String annotate;
    private Integer isAttr;

    /**
     * 实体标注已分配的所有属性标注的id拼接字符串
     */
    private String attrIds;
    /**
     * 属性标注已分配的所有实体标注的id拼接字符串
     */
    private String entityIds;
    /**
     * 已分配属性名称拼接字符串
     */
    private String attrLabelInfo;
    private String attrLabelUsedInfo;

    /**
     * 属性标注对应的实体标注的标签id, 用于改变属性标注颜色
     */
    private String labelIdOfAttrAnno;
    private Integer source;
    //是否为复合标注
    private Integer isCombo;
    private String batchAnnotateId;

    private Integer questionLogo;

    private Integer showMultiSameEntity;

//    public AnnotationsDTO(Integer start, Integer end, String label, String textId) {
//        this(start, end, label, textId, null);
//    }

    public AnnotationsDTO(Integer start, Integer end, Long label, String textId, String uniqueid, String annoid, String annotate, Integer isAttr, String attrIds, String entityIds,
                          String attrLabelInfo, Long labelIdOfAttrAnno, Integer source, int size, String batchAnnotateId, String attrLabelUsedInfo, Integer questionLogo, boolean showMultiSameEntity) {
        this.start = start;
        this.end = end;
        // 反射代码没有处理Long型
        this.label = label == null ? null : label.toString();
        this.textId = textId;
        this.uniqueid = uniqueid;
        this.annoid = annoid;
        this.annotate = annotate;
        this.isAttr = isAttr;
        this.attrIds = attrIds;
        this.entityIds = entityIds;
        this.attrLabelInfo = attrLabelInfo;
        this.attrLabelUsedInfo = attrLabelUsedInfo;
        this.labelIdOfAttrAnno = labelIdOfAttrAnno == null ? null : labelIdOfAttrAnno.toString();
        this.source = source;
        this.isCombo = size > 1 ? 1 : 0;
        this.batchAnnotateId = batchAnnotateId;
        this.questionLogo = questionLogo;
        this.showMultiSameEntity = showMultiSameEntity ? 1 : 0;
    }

}
