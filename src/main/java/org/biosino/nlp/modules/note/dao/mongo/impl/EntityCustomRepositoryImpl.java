package org.biosino.nlp.modules.note.dao.mongo.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.common.enums.AttrAnnoEnum;
import org.biosino.nlp.common.enums.DeleteEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.note.dao.mongo.EntityCustomRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-09 19:30
 */
@Repository
public class EntityCustomRepositoryImpl implements EntityCustomRepository {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public boolean findSubLabelByID(Long id) {
        Query query = Query.query(baseCriteria().and("subLabels").in(id));
        query.fields().include("subLabels");
        return mongoTemplate.exists(query, Entity.class);
    }

    @Override
    public boolean findEntityByConceptIdAndProjectId(String conceptId, Long projectId) {
        Query query = Query.query(baseCriteria().and("project_id").is(projectId).and("conceptId").is(conceptId));
        return mongoTemplate.exists(query, Entity.class);
    }

    @Override
    public List<Entity> findByIds(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Query query = Query.query(baseCriteria().and("_id").in(ids));
        // query.fields().include("_id");
        return mongoTemplate.find(query, getCls());
    }

    /*@Override
    public List<Entity> findAllByNoteIdAndUserIdAndRoleId(Long noteId, Long userId, Long roleId) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("noteId").is(noteId));
        final Optional<RoleEnum> roleEnumOpt = RoleEnum.findById(roleId);
        if (roleEnumOpt.isPresent()) {
            RoleEnum role = roleEnumOpt.get();
            switch (role) {
                case annotator:
                    condition.add(Criteria.where("annotatorId").is(userId));
                    break;
                case auditor:
                    condition.add(Criteria.where("auditorId").is(userId));
                    break;
                default:
                    throw new ServiceException("该用户角色没有标注权限");
            }
        } else {
            throw new ServiceException("角色信息错误");
        }
        final Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        return mongoOperations.find(Query.query(criteria), Entity.class);
    }*/

    @Override
    public Optional<Entity> findByUniqueid(final String uniqueid) {
        if (StrUtil.isBlank(uniqueid)) {
            return Optional.empty();
        }
        final Entity e = mongoTemplate.findOne(Query.query(baseCriteria().and("entityInfos.uniqueid").is(uniqueid)), getCls());
        if (e != null) {
            return Optional.of(e);
        } else {
            return Optional.empty();
        }
    }


   /* public List<String> findAllId(List<String> ids) {
        Query query = Query.query(Criteria.where("_id").in(ids));
        query.fields().include("_id");
        List<Article> articles = mongoOperations.find(query, Article.class);
        return articles.stream().map(Article::getId).collect(Collectors.toList());
    }*/

    @Override
    public List<AnnoDTO.EntityInfo> findSameEntity(final Entity entity, final AnnoDTO.EntityInfo entityInfo) {
        final Long noteTaskId = entity.getTaskId();
        final List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());

        // 预标注task_id为空，所以必须查以下字段
        condition.add(Criteria.where("note_id").is(entity.getNoteId()));
        condition.add(Criteria.where("batch_id").is(entity.getBatchId()));
        condition.add(Criteria.where("article_id").is(entity.getArticleId()));

        condition.add(Criteria.where("task_id").is(noteTaskId));
//        final List<AnnoDTO.EntityInfo> entityInfos = entity.getEntityInfos();
        condition.add(Criteria.where("entityInfos.start").is(entityInfo.getStart()));
        condition.add(Criteria.where("entityInfos.end").is(entityInfo.getEnd()));
        condition.add(Criteria.where("entityInfos.text_id").is(entityInfo.getTextId()));
        final Criteria criteria = new Criteria();
        criteria.andOperator(condition.toArray(new Criteria[]{}));
        List<Entity> entities = mongoTemplate.find(Query.query(criteria), Entity.class);
        final List<AnnoDTO.EntityInfo> infos = new ArrayList<>();
        for (Entity e : entities) {
            List<AnnoDTO.EntityInfo> list = e.getEntityInfos();
            for (AnnoDTO.EntityInfo info : list) {
                final AnnoDTO.EntityInfo item = new AnnoDTO.EntityInfo();
                item.setAnnoid(e.getId());
                item.setLabelId(e.getLabelId());
                item.setAnnotate(e.getAnnotate());
                item.setIsAttr(e.getIsAttr());
                item.setUniqueid(info.getUniqueid());
                item.setStart(info.getStart());
                item.setEnd(info.getEnd());
                item.setTextId(info.getTextId());
                item.setContent(info.getContent());
                infos.add(item);
            }
        }
        return infos;
    }

    /*private void findAttrInfo(Entity entity) {
        if (entity.getIs_attr() == AttrAnnoEnum.not_attr.getCode()) {
            // 实体标注查询其包含的属性
            List<Long> attrLabelIds = null;
            List<Attributes> attributesList = attributesRepository.findByEntityId(entity.getId());
            if (CollUtil.isNotEmpty(attributesList)) {
                attrLabelIds = attributesList.stream().map(Attributes::getAttr_label_id).collect(Collectors.toList());
            }
            List<AttributeLabel> attributeLabels = attributeLabelService.findByIds(attrLabelIds);
            if (CollUtil.isNotEmpty(attributeLabels)) {
                final List<String> attrNames = attributeLabels.stream().map(AttributeLabel::getName).distinct().collect(Collectors.toList());
                entity.setAttrNames(attrNames);
            }
        }
    }*/

    @Override
    public void deleteAllByNoteId(Long noteId) {
        final Query query = Query.query(Criteria.where("noteId").is(noteId));
        mongoTemplate.updateMulti(query, deleteUpdate(), Entity.class);
    }

    @Override
    public boolean existsByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(String md5, Long noteId, String articleId, int isAttr, int source) {
        Query query = Query.query(baseCriteria()
                .and("note_id").is(noteId)
                .and("article_id").is(articleId)
                .and("is_attr").is(isAttr)
                .and("md5").is(md5)
                .and("source").is(source)
        );
        return mongoTemplate.exists(query, getCls());
    }

    @Override
    public Optional<Entity> findFirstByMd5AndNoteIdAndArticleIdAndIsAttrAndSource(String md5, Long noteId, String articleId, int isAttr, int source) {
        Query query = Query.query(baseCriteria()
                .and("md5").is(md5)
                .and("note_id").is(noteId)
                .and("article_id").is(articleId)
                .and("is_attr").is(isAttr)
                .and("source").is(source)
        );
        Entity one = mongoTemplate.findOne(query, getCls());
        return one == null ? Optional.empty() : Optional.of(one);
    }

    @Override
    public long countByTaskIdAndMd5AndSource(Long noteTaskId, String md5, int source) {
        Query query = Query.query(baseCriteria()
                .and("task_id").is(noteTaskId)
                .and("md5").is(md5)
                .and("source").is(source)
        );
        return mongoTemplate.count(query, getCls());
    }

    @Override
    public List<Entity> findByTaskIdAndMd5AndSource(Long noteTaskId, String md5, int source) {
        // 改为已删除的数据也需要查询出来
        Query query = Query.query(Criteria.where("task_id").is(noteTaskId)
                .and("md5").is(md5)
                .and("source").is(source)
        );
//        Entity one = mongoTemplate.find(query, getCls());
        return mongoTemplate.find(query, getCls());
    }

    @Override
    public List<Long> findAllPreSourceByArticleId(String articleId, Long projectId) {
        final Query query = Query.query(
                baseCriteria().and("article_id").is(articleId)
                        .and("project_id").is(projectId)
                        .and("source").is(PreSourceEnum.CUSTOMIZE.getId())
                        .and("import_log_id").exists(true)
        );
//        query.fields().include("import_log_id");

        return mongoTemplate.findDistinct(query, "import_log_id", Entity.class, Long.class);
    }

    /**
     * 查询自标注和预标注的数据
     *
     * @param taskId
     * @param sourceEnum
     * @return
     */
    @Override
    public List<Entity> findDocumentArticle(final Long taskId, final Long noteId, final PreSourceEnum sourceEnum, Long currImportLogId, String articleId, Long projectId) {
        return findDocumentArticle(taskId, noteId, sourceEnum, false, null, currImportLogId, articleId, projectId);
    }

    @Override
    public List<Entity> findDocumentArticle(final Long taskId, final Long noteId, final PreSourceEnum sourceEnum, final boolean showAnnoOnly,
                                            final Long annotatorId, final Long currImportLogId, String articleId, Long projectId) {
        final List<Criteria> condition = new ArrayList<>();
        if (showAnnoOnly && annotatorId != null) {
            initDelCondition(condition, baseCriteria(), annotatorId);
        } else {
            condition.add(baseCriteria());
        }
        condition.add(Criteria.where("source").is(sourceEnum.getId()));
        Criteria taskOrNoteCri;
        if (NoteServiceImpl.isInvalidLong(taskId) || !PreSourceEnum.SELF.equals(sourceEnum)) {
            // 预标注
            if (currImportLogId == null) {
                throw new RRException("import log id不能为空");
            }
            condition.add(Criteria.where("import_log_id").is(currImportLogId));

            condition.add(Criteria.where("project_id").is(projectId));
            condition.add(Criteria.where("article_id").is(articleId));
        } else {
            // 自标注
            taskOrNoteCri = Criteria.where("task_id").is(taskId);
            condition.add(taskOrNoteCri);
        }

        Query query = Query.query(andQuery(condition));
        return mongoTemplate.find(query, getCls());
    }

    public static void initDelCondition(final List<Criteria> condition, Criteria baseCriteria, final Long annotatorId) {
        // 仅查询当前标注员的标注信息(包含审核员删除的内容)
        condition.add(
                new Criteria().orOperator(
                        baseCriteria,
                        new Criteria().andOperator(
                                Criteria.where(LOGICAL_DELETE_FIELD_NAME).is(DeleteEnum.delete.getVal()),
                                Criteria.where("auditor_id").exists(true)
                        )
                )
        );
        condition.add(Criteria.where("annotator_id").is(annotatorId));
    }

    @Override
    public List<Entity> findByTaskIdAndSource(final Long taskId, final PreSourceEnum sourceEnum) {
        Criteria criteria = null;
        if (PreSourceEnum.SELF.equals(sourceEnum)) {
            //原文
            criteria = baseCriteria().and("task_id").is(taskId).and("source").is(sourceEnum.getId());
        } else {
            //预标注
            final Optional<Long> noteIdOptional = findNoteIdByTaskId(taskId);
            if (noteIdOptional.isPresent()) {
                criteria = baseCriteria().and("note_id").is(noteIdOptional.get()).and("source").is(sourceEnum.getId());
            }
        }
        if (criteria == null) {
            return new ArrayList<>();
        }
        //.with(Sort.by(Sort.Direction.ASC, "create_time"))
        return mongoTemplate.find(Query.query(criteria), getCls());
        // 使用aggregation按照段落和最小的start排序
        /*final Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.unwind("entityInfos"),
                Aggregation.group("_id")
                        .first("task_id").as("task_id")
                        .first("is_attr").as("is_attr")
                        .first("source").as("source")
                        .first("label_id").as("label_id")
                        .push(new BasicDBObject("text_id", "$entityInfos.text_id").append("start", "$entityInfos.start").append("end", "$entityInfos.end")
                                .append("content", "$entityInfos.content")).as("entityInfos")
                        .addToSet("entityInfos").as("entityInfos")
                        .min("entityInfos.start").as("minStart")
                        .min("entityInfos.text_id").as("minTextId"),
                Aggregation.sort(Sort.Direction.ASC, "minTextId", "minStart")
        );
        return aggregateAll(aggregation);*/
    }

    @Override
    public Optional<Long> findNoteIdByTaskId(Long taskId) {
        if (taskId == null) {
            return Optional.empty();
        }
        Criteria criteria = baseCriteria().and("task_id").is(taskId);
        Query query = Query.query(criteria);
        query.fields().include("note_id");
        Entity entity = mongoTemplate.findOne(query, getCls());
        if (entity == null) {
            return Optional.empty();
        }
        Long noteId = entity.getNoteId();
        return noteId == null ? Optional.empty() : Optional.of(noteId);
    }

    @Override
    public Map<String, Entity> findMd5ByTaskIdAndSource(final Long taskId, final PreSourceEnum sourceEnum) {
        final Map<String, Entity> map = new HashMap<>();
        if (NoteServiceImpl.isInvalidLong(taskId)) {
            return map;
        }
        // 改为已删除的数据也需要查询出来
        final Query query = Query.query(Criteria.where("task_id").is(taskId)
                .and("source").is(sourceEnum.getId())
                .and("md5").exists(true));
        final List<Entity> entities = mongoTemplate.find(query, getCls());
        for (Entity entity : entities) {
            map.put(entity.getMd5(), entity);
        }
        return map;
//        return mongoTemplate.findDistinct(query, "md5", getCls(), String.class);
    }

    @Override
    public List<String> findDistinctMd5ByLabelId(Long batchId, Long labelId, Collection<Long> masterTaskIds) {
        Query query = Query.query(baseCriteria().and("batch_id").is(batchId).and("label_id").is(labelId).and("task_id").in(masterTaskIds)
                .and("source").is(PreSourceEnum.SELF.getId()).and("is_attr").is(AttrAnnoEnum.not_attr.getCode()));
        query.with(Sort.by("md5"));
        return mongoTemplate.findDistinct(query, "md5", Entity.class, String.class);
    }

    @Override
    public List<String> findDistinctMd5(Long taskId) {
        Query query = Query.query(baseCriteria().and("task_id").is(taskId)
                .and("source").is(PreSourceEnum.SELF.getId()).and("is_attr").is(AttrAnnoEnum.not_attr.getCode()));
        query.with(Sort.by("md5"));
        return mongoTemplate.findDistinct(query, "md5", Entity.class, String.class);
    }

    @Override
    public long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted) {
        Query query = new Query();
        query.addCriteria(Criteria.where("task_id").is(taskId).and("source").is(PreSourceEnum.SELF.getId()));

        if (deleted != null) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        query.addCriteria(Criteria.where("is_attr").is(AttrAnnoEnum.not_attr.getCode()));
        query.addCriteria(Criteria.where("annotator_id").is(annotatorId));
        query.addCriteria(Criteria.where("auditor_id").is(auditorId));
        return mongoTemplate.count(query, Entity.class);
    }

    @Override
    public List<String> findMd5ByUser(Long taskId, Long userId) {
        Query query = Query.query(baseCriteria().and("task_id").is(taskId)
                .and("annotator_id").is(userId).and("source").is(PreSourceEnum.SELF.getId())
                .and("is_attr").is(AttrAnnoEnum.not_attr.getCode()));

        query.with(Sort.by("md5"));
        return mongoTemplate.findDistinct(query, "md5", Entity.class, String.class);
    }

    @Override
    public long countByUserId(Long projectId, Long taskId, Integer isAttr, Long annotatorId, Long auditorId, Boolean deleted) {
        Query query = Query.query(Criteria.where("project_id").is(projectId).and("is_attr").is(isAttr));

        if (taskId != null) {
            query.addCriteria(Criteria.where("task_id").is(taskId));
        }
        if (deleted != null) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        query.addCriteria(Criteria.where("annotator_id").is(annotatorId));
        query.addCriteria(Criteria.where("auditor_id").is(auditorId));
        return mongoTemplate.count(query, Entity.class);
    }

    @Override
    public List<Entity> findBatchEntities(String batchAnnotateId) {
        if (StrUtil.isBlank(batchAnnotateId)) {
            return new ArrayList<>();
        }
        Query query = Query.query(baseCriteria().and("batch_annotate_id").is(batchAnnotateId));
        return mongoTemplate.find(query, getCls());
    }

    @Override
    public Set<String> findAllDeleteIds(final Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashSet<>();
        }
//        final Query query = Query.query(Criteria.where(LOGICAL_DELETE_FIELD_NAME).is(DeleteEnum.delete.getVal()).and("_id").in(ids));
        final Query query = Query.query(Criteria.where("_id").in(ids));
        final List<Entity> entities = mongoTemplate.find(query, getCls());
        final Set<String> set = new LinkedHashSet<>();
        final Set<String> existIds = new HashSet<>();
        for (Entity entity : entities) {
            final String id = entity.getId();
            existIds.add(id);
            if (entity.isDeleted()) {
                set.add(id);
            }
        }

        final HashSet<String> notExistIds = new HashSet<>(ids);
        notExistIds.removeAll(existIds);
        if (!notExistIds.isEmpty()) {
            mongoTemplate.remove(Query.query(Criteria.where("attribute_id").in(notExistIds)), Attributes.class);
            set.addAll(notExistIds);
        }

        return set;
//        final List<String> list = mongoTemplate.findDistinct(query, "_id", Entity.class, String.class);
//        return CollUtil.isEmpty(list) ? new HashSet<>() : new HashSet<>(list);
    }

    @Override
    public Optional<Entity> findDelById(String entityId, Long annotatorId) {
        Optional<Entity> optional = Optional.empty();
        if (StrUtil.isBlank(entityId)) {
            return optional;
        }

        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("_id").is(entityId));

        initDelCondition(condition, baseCriteria(), annotatorId);

        Entity one = mongoTemplate.findOne(Query.query(baseCriteria().and("_id").is(entityId)), getCls());
        if (one != null) {
            optional = Optional.of(one);
        }
        return optional;
    }

    @Override
    public Page<Entity> queryPage(EntitySearchDTO dto) {
        Query query = new Query();
        Criteria criteria = baseCriteria();
        criteria.and("is_attr").is(AttrAnnoEnum.not_attr.getCode());
        criteria.and("labelId").is(dto.getLabelId());
        if (StrUtil.isNotBlank(dto.getArticleId())) {
            criteria.and("article_id").is(dto.getArticleId());
        }
        if (dto.getUserId() != null) {
            criteria.orOperator(Criteria.where("annotator_id").is(dto.getUserId()).and("auditor_id").is(null),
                    Criteria.where("annotator_id").is(null).and("auditor_id").is(dto.getUserId()));
        } else {
            criteria.orOperator(Criteria.where("annotator_id").is(null).and("auditor_id").ne(null),
                    Criteria.where("annotator_id").ne(null).and("auditor_id").is(null));
        }

        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            criteria.andOperator(
                    Criteria.where("create_time").gte(dto.getStartDate()),
                    Criteria.where("create_time").lte(DateUtil.endOfDay(dto.getEndDate()))
            );
        } else if (dto.getStartDate() != null) {
            criteria.and("create_time").gte(dto.getStartDate());
        } else if (dto.getEndDate() != null) {
            criteria.and("create_time").lte(DateUtil.endOfDay(dto.getEndDate()));
        }
        query.addCriteria(criteria);

        // 统计符合条件的总数
        final long count = mongoTemplate.count(query, getCls());

        // 凭借分页查询条件
        final Sort.Direction direction = Boolean.TRUE.equals(dto.getIsAsc()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        final Sort sort = Sort.by(direction, dto.getOrderBy());
        final Pageable pageable = PageRequest.of(dto.getPage() - 1, dto.getLimit(), sort);
        if (count == 0) {
            return new PageImpl<>(new ArrayList<>(), pageable, count);
        } else {
            if (Boolean.TRUE.equals(dto.getForPageIds())) {
                query.fields().include("batch_id").include("note_id");
            }

            query.with(pageable);
            List<Entity> list = mongoTemplate.find(query, getCls());

            return new PageImpl<>(list, pageable, count);
        }
    }

    @Override
    public List<Long> getUserIdsByLabelId(Long labelId) {
        List<Long> annotatorIds = mongoTemplate.findDistinct(Query.query(baseCriteria().and("is_attr")
                .is(AttrAnnoEnum.not_attr.getCode()).and("label_id").is(labelId)), "annotator_id", Entity.class, Long.class);
        List<Long> auditorIds = mongoTemplate.findDistinct(Query.query(baseCriteria().and("is_attr")
                .is(AttrAnnoEnum.not_attr.getCode()).and("label_id").is(labelId)), "auditor_id", Entity.class, Long.class);
        annotatorIds.addAll(auditorIds);
        return annotatorIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public void deleteByImportLogId(Long id) {
        // 根据importLogId删除数据
        if (id == null) {
            return;
        }
        mongoTemplate.remove(Query.query(Criteria.where("import_log_id").is(id)), getCls());
    }

    @Override
    public List<Entity> findByImportLogId(Long importLogId) {
        return mongoTemplate.find(Query.query(Criteria.where("import_log_id").is(importLogId)), getCls());
    }

    @Override
    public int countQueryByTaskId(final Long taskId) {
        if (taskId == null) {
            return 0;
        }
        return (int) mongoTemplate.count(Query.query(baseCriteria().and("task_id").is(taskId)
                .and("doubters").exists(true)), getCls());
    }

    @Override
    public void resetDoubters(List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return;
        }

        final Update update = new Update();
        update.unset("doubters");
        update.set("update_time", new Date());
        mongoTemplate.updateMulti(Query.query(baseCriteria().and("task_id").in(taskIds)), update, getCls());
    }

    @Override
    public Map<Long, Set<String>> findOtherSameEntityMd5(Long noteId, Set<String> md5Set) {
        final Criteria criteria = baseCriteria()
                .and("is_attr").is(AttrAnnoEnum.not_attr.getCode())
                .and("source").is(PreSourceEnum.SELF.getId())
                .and("md5").in(md5Set)
                .and("note_id").is(noteId)
                .and("task_id").exists(true);

        final List<Entity> entities = mongoTemplate.find(Query.query(criteria), getCls());
        // final List<String> md5s = mongoTemplate.findDistinct(Query.query(criteria), "md5", getCls(), String.class);
        return entities.stream().collect(Collectors.groupingBy(Entity::getTaskId, Collectors.mapping(Entity::getMd5, Collectors.toSet())));
    }

    @Override
    public Set<String> deleteByTaskIdAndAuditorId(Long taskId, Long auditorId) {
        final Criteria criteria = Criteria.where("task_id").is(taskId)
                .and("auditor_id").is(auditorId);

        // 删除并返回删除的id
        final List<Entity> removeEntities = mongoTemplate.findAllAndRemove(Query.query(criteria), getCls());
        final Set<String> removeEntityIds = new HashSet<>();
        if (CollUtil.isNotEmpty(removeEntities)) {
            for (Entity entity : removeEntities) {
                removeEntityIds.add(entity.getId());
            }
        }
        return removeEntityIds;
    }

    @Override
    public Class<Entity> getCls() {
        return Entity.class;
    }

    @Override
    public MongoTemplate getMongoTemplate() {
        return mongoTemplate;
    }

}
