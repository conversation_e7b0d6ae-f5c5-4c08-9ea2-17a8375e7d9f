package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.entity.mongo.Comment;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Comment Repository
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Repository
public interface CommentRepository extends MongoRepository<Comment, String> {

    /**
     * 根据任务ID查询评论
     */
    List<Comment> findByTaskIdOrderByCreateTimeDesc(Long taskId);

    /**
     * 根据文书ID查询评论
     */
    List<Comment> findByNoteIdOrderByCreateTimeDesc(Long noteId);

    /**
     * 根据任务ID和状态查询评论数量
     */
    long countByTaskIdAndStatus(Long taskId, Integer status);

    /**
     * 根据文书ID删除评论
     */
    void deleteByNoteId(Long noteId);

    /**
     * 根据文书ID列表批量删除评论
     */
    void deleteByNoteIdIn(List<Long> noteIds);

    /**
     * 根据批次ID删除评论
     */
    void deleteByBatchId(Long batchId);

    /**
     * 根据任务ID删除评论
     */
    void deleteByTaskId(Long taskId);
}
