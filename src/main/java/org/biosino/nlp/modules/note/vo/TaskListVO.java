package org.biosino.nlp.modules.note.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TaskListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long noteId;

    private Long taskId;

    private String articleId;

    private String articleName;

    private Integer correctRate;

    private Integer step;

    private Long auditorId;

    private Boolean editable;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date updateTime;

    private Integer queries;
}
