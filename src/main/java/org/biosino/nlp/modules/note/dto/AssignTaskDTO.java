package org.biosino.nlp.modules.note.dto;

import lombok.Data;
import org.biosino.nlp.modules.project.dto.BaseDTO;

import javax.validation.constraints.NotNull;

@Data
public class AssignTaskDTO extends BaseDTO {
    @NotNull
    private Long batchId;
    @NotNull
    private Long roleId;
    private Long noteId;
    /**
     * 当前用户的ID，可能是标注员可能是审核员
     */
    private Long userId;
    private Boolean editable;
    /**
     * 当前用户是审核员状态下，标注员的ID
     */
    private Long annotatorId;
}
