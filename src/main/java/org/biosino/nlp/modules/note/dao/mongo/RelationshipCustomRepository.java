package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-26 15:55
 */
@Repository
public interface RelationshipCustomRepository extends BaseLogicalDelete<Relationship> {

    Relationship saveRelationship(RelationshipDTO relationshipDTO);

    boolean isRelationExists(Long taskId, String entityId);

    void deleteAllByNoteId(Long noteId);

    boolean existsByRelationId(Long relationId);

    Page<Relationship> queryPage(EntitySearchDTO dto);

    List<Long> getUserIdsByLabelId(Long patternId);

    List<Relationship> findPreAnnoRelation(Long noteId);

    List<String> findDistinctMd5(Long taskId);

    List<String> findMd5ByUser(Long taskId, Long userId);

    long countByUserId(Long projectId, Long userId, Long roleId, Long taskId);

    long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted);

    long countItemsByUserId(Long projectId, Long userId, Long roleId, Long taskId);

    List<Relationship> getRelationshipsByPatternId(Long taskId, Long noteId, Integer patternId, Integer source, Long importLogId);

    void deleteByImportLogId(Long importLogId);
}
