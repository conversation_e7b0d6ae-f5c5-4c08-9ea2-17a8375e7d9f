package org.biosino.nlp.modules.note.entity.mongo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "m_entity")
public class Entity extends BaseMongo implements Serializable {

    @Transient
    private static final long serialVersionUID = -2186697763662420763L;

    @Indexed
    @Field(name = "note_id")
    private Long noteId;
    @Indexed
    @Field(name = "task_id")
    private Long taskId;
    @Indexed
    @Field(name = "project_id")
    private Long projectId;
    @Indexed
    @Field(name = "batch_id")
    private Long batchId;
    @Indexed
    @Field(name = "article_id")
    private String articleId;

    @Field(name = "label_id")
    private Long labelId;

    private List<AnnoDTO.EntityInfo> entityInfos;
    /**
     * 标注md5值
     */
    @Indexed
    private String md5;
    /**
     * 标注员
     */
    @Field(name = "annotator_id")
    private Long annotatorId;
    /**
     * 审核员
     */
    @Field(name = "auditor_id")
    private Long auditorId;

    /**
     * 提问人ID
     */
    private Long doubters;

    /**
     * 标注批注 或 提问信息
     */
    private String annotate;
    /**
     * umls消歧
     */
    private String conceptId;
    private String conceptText;
    private Integer conceptType;
    /**
     * 实体来源：self(自标注) 1、upload（预标注） 2、其他系统名称
     * @see org.biosino.nlp.common.enums.PreSourceEnum
     */
    @Indexed
    private int source;

    @Field(name = "import_log_id")
    private Long importLogId;

    @Field(name = "create_time")
    private Date createTime;

    /**
     * 批量标注批次id, 用于批量删除等操作
     */
    @Indexed
    @Field(name = "batch_annotate_id")
    private String batchAnnotateId;

    /**
     * 如果isAttr是1则表示是属性，是0则表示是实体（默认）
     */
    @Indexed
    @Field("is_attr")
    private Integer isAttr = 0;

    @Transient
    private EntityLabel label;

    @Transient
    private AttributeLabel attributeLabel;

    /**
     * 实体标注集合，包含完全重叠的标注
     */
    @Transient
    private List<AnnoDTO.EntityInfo> allEntityInfos;

    @Transient
    private List<String> allEntityNames;

}
