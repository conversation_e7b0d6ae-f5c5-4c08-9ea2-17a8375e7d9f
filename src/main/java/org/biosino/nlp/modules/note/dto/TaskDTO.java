package org.biosino.nlp.modules.note.dto;

import lombok.Data;
import org.biosino.nlp.modules.project.dto.BaseDTO;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TaskDTO extends BaseDTO {
    @NotNull
    private Long roleId;

    @NotNull
    private Long batchId;

    /**
     * 当前用户的ID，可能是标注员可能是审核员
     */
    @NotNull
    private Long userId;

    /**
     * 标注员的ID
     */
    private Long annotatorId;
    /**
     * 当前用户是审核员状态下，标注员的ID
     */
    private Long auditorId;

    private String articleId;
    private String articleName;
    private Integer correctRate;
    @NotNull
    private String activeStep;
    private List<Integer> step;

    private int markRounds;
}
