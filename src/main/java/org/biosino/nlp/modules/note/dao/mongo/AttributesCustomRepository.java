package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.dto.AttrAnnoLabelDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;

import java.util.*;

public interface AttributesCustomRepository extends BaseLogicalDelete<Attributes> {
    List<Attributes> findByEntityId(String entityId);

    List<Attributes> findDelByEntityId(String entityId, Long annotatorId);

    List<Attributes> findByEntityIdHasAttributeId(String entityId);

    List<Attributes> findByEntityIdAndAttrLabelId(String entityId, Long attrLabelId);

    List<Attributes> findByEntityIdOrAttrId(String entityId, String attId);

    List<Attributes> findByAttributeId(String attributeId);

    List<Attributes> findDelByAttributeId(String attributeId, Long annotatorId);

    boolean isAttrExists(String attributeId);

    long countByEntityIdAndAttrLabelIdAndContentAndAttributeId(String entityId, Long attrLabelId, String content, String attribute_id);

    List<Attributes> findAllEntityIdIn(Collection<String> ids);

//    void deleteByEntityId(String entityId);

    boolean existsByAttrLabelId(Long attrLabelId);

    long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted);

    Map<String, Integer> countMapByEntityIds(Collection<String> ids);

    void deleteAllByNoteId(Long noteId);

    List<String> findTwoMd5(Long taskId, Long userId);

    Map<String, LinkedHashSet<AttrAnnoLabelDTO>> findAttrAnnoLabelByAttributeIds(List<String> attributeIds);

    long countByUserId(Long projectId, Long userId, Long roleId, Long taskId);

    void deleteByAttributeId(Set<String> allDeleteIds);

    /**
     * 删除属性关系(真实删除)
     */
    void deleteByAttributeIdInFact(Set<String> allDeleteIds);

    void deleteByImportLogId(Long id);

    Map<String, Boolean> findEntityAndAttrMap(Set<Long> otherTaskIds, Set<String> md5Set);

}
