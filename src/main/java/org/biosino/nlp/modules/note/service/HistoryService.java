package org.biosino.nlp.modules.note.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.note.dto.HistoryDTO;
import org.biosino.nlp.modules.note.entity.Note;

import java.util.Map;

public interface HistoryService extends IService<Note> {
    PageUtils annotatorDataList(HistoryDTO dto);

    PageUtils auditorDataList(HistoryDTO dto);

    Map<Long, String> getAnnotator(HistoryDTO dto);

    Map<Long, String> getAuditor(HistoryDTO dto);

    PageUtils getProjectAdminDataList(HistoryDTO dto);
}
