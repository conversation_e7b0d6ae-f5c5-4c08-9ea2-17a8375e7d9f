package org.biosino.nlp.modules.note.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 标注的操作日志
 *
 * <AUTHOR>
 */
@Data
@TableName("l_operation")
public class Operation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long noteId;

    private Long taskId;

    private Long userId;

    private Integer operation;

    private Integer type;

    private String msg;

    private Date createTime;

    /**
     * 是否删除[1.已删除值  0.未删除]
     */
    @TableLogic
    private Integer deleted;

}
