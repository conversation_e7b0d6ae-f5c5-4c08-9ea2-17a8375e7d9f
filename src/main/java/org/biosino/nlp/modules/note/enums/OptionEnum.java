package org.biosino.nlp.modules.note.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 标注状态
 */
public enum OptionEnum {
    //
    add(1, "新增"),
    update(2, "编辑"),
    delete(3, "删除");

    private Integer code;
    private String desc;

    OptionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<>(5);
        for (OptionEnum statusEnum : OptionEnum.values()) {
            map.put(statusEnum.getCode(), statusEnum.getDesc());
        }
        return map;
    }

    public static String getDesc(Integer code) {
        for (OptionEnum statusEnum : OptionEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
