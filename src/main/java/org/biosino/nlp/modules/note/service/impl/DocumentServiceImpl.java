package org.biosino.nlp.modules.note.service.impl;

import com.aliyun.oss.ServiceException;
import org.biosino.nlp.modules.note.dao.mongo.DocumentRepository;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("documentService")
public class DocumentServiceImpl implements DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Override
    @Cacheable(cacheNames = "getDocumentById_cache")
    public Document getDocumentById(String documentId) {
        return documentRepository.findById(documentId).orElseThrow(() -> new ServiceException("未查询到该文档"));
    }

}
