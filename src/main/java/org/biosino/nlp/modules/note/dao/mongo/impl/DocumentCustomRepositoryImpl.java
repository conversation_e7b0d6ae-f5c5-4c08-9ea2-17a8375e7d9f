package org.biosino.nlp.modules.note.dao.mongo.impl;

import org.biosino.nlp.modules.note.dao.mongo.DocumentCustomRepository;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-09 19:30
 */
@Repository
public class DocumentCustomRepositoryImpl implements DocumentCustomRepository {

    @Autowired
    private MongoOperations mongoOperations;

    @Override
    public List<String> findAllId(List<String> ids) {
        Query query = Query.query(Criteria.where("_id").in(ids));
        query.fields().include("_id");

        List<Document> documents = mongoOperations.find(query, Document.class);
        return documents.stream().map(Document::getId).collect(Collectors.toList());
    }
}
