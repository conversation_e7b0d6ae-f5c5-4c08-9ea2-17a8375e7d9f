package org.biosino.nlp.modules.note.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.biosino.nlp.common.enums.MasterEnum;

import java.io.Serializable;
import java.util.Date;


/**
 * 每个标注员具体的标注任务
 *
 * <AUTHOR>
 * @date 2023/4/21
 */
@Data
@TableName("t_note_task")
public class NoteTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long taskId;

    private Long projectId;
    private Long batchId;
    private Long noteId;

    /**
     * 文章当前状态 [0未标注  1标注中  2已标注  3审核中  4已审核  5打回  6已修正]
     */
    private Integer step;

    private Long annotator;
    private Date annoStartTime;
    private Date annoEndTime;

    private Long auditor;
    private Date auditStartTime;
    private Date auditEndTime;

    /**
     * 是否废弃[1.废弃、 0.未废弃]
     */
    private Integer invalid = 0;

    /**
     * 打回的批注信息（有数据表示打回，没有数据表示未打回）
     */
    private String repulseMsg;

    private Date createTime;
    private Date updateTime;

    /**
     * 标注结果的准确率
     */
    private Float correctRate = 100F;
    private Long correctEntity = 0L;
    private Long correctAttr = 0L;
    private Long correctRelation = 0L;

    // 应标实体数
    private Long needTotal = 0L;
    // 标对数TP
    private Long correctTotal = 0L;
    // 标错数FP
    private Long errorTotal = 0L;
    // 漏标数FN
    private Long missTotal = 0L;

    /**
     * 是否为基础蓝本任务,0非蓝本，1蓝本，用于多人标注审核
     */
    private Integer master = MasterEnum.not_master.getValue();
    /**
     * 标注总信息的MD5
     */
    private String md5;
    /**
     * 关系预标注是否已经导入完毕，0未导入，1已导入
     */
    private Integer preRelationImported = 0;

    /**
     * 质疑实体标注的数量
     */
    private Integer queries = 0;

    /**
     * 是否删除[1.已删除值  0.未删除]
     */
    @TableLogic
    private Integer deleted;


}
