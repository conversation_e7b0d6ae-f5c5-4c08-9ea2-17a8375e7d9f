package org.biosino.nlp.modules.note.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class EntityAttrDTO implements Serializable {
    private static final long serialVersionUID = 5476897386888995335L;

    /**
     * 实体标注中，已分配的属性标注的id拼接字符串
     */
    private String attrIds;
    /**
     * 属性标注中，已分配的实体标签id拼接字符串
     */
    private String entityIds;
    /**
     * 属性标注中，对应已分配属性标签名称拼接字符串
     */
    private String attrLabelInfo = null;

    private String attrLabelUsedInfo = null;

    /**
     * 属性标注中，对应的实体标注的标签id
     */
    private Long labelIdOfAttrAnno = null;
}
