package org.biosino.nlp.modules.note.controller;

import io.swagger.annotations.Api;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.note.dto.HistoryDTO;
import org.biosino.nlp.modules.note.service.HistoryService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 标注操作历史记录控制层
 *
 * <AUTHOR>
 */
@RestController
@Api("标注操作历史记录接口")
@RequestMapping("/history")
public class HistoryController extends AbstractController {

    @Autowired
    private HistoryService historyService;

    /**
     * 标注员 标注历史列表数据
     */
    @RequestMapping("/annotatorDataList")
    public R annotatorDataList(HistoryDTO historyDTO) {
        PageUtils page = historyService.annotatorDataList(historyDTO);
        return R.ok().put("page", page);
    }

    /**
     * 审核员 标注历史列表数据
     */
    @RequestMapping("/auditorDataList")
    public R auditorDataList(HistoryDTO dto) {
        PageUtils page = historyService.auditorDataList(dto);
        Map<Long, String> annotatorList = historyService.getAnnotator(dto);
        return R.ok().put("page", page).put("annotatorList", annotatorList);
    }

    /**
     * 系统管理员员 标注历史列表数据
     */
    @RequestMapping("/getProjectDataList")
    public R getProjectDataList(HistoryDTO dto) {
        PageUtils page = historyService.getProjectAdminDataList(dto);
        Map<Long, String> annotatorList = historyService.getAnnotator(dto);
        Map<Long, String> auditorList = historyService.getAuditor(dto);
        return R.ok().put("page", page).put("annotatorList", annotatorList).put("auditorList", auditorList);
    }

}
