package org.biosino.nlp.modules.note.controller;

import io.swagger.annotations.Api;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.common.validator.ValidatorUtils;
import org.biosino.nlp.modules.api.vo.PatternDataVo;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.note.dto.PreImportDTO;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.service.RelationshipMapService;
import org.biosino.nlp.modules.note.service.RelationshipService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/8 19:46
 */

@RestController
@Api("关系标注接口")
@RequestMapping("/note/relationship")
public class RelationNoteController extends AbstractController {

    @Autowired
    private RelationshipService relationshipService;

    @Autowired
    private RelationshipMapService relationshipMapService;

    /**
     * 添加关系标签
     */
    @RequestMapping("/add")
    public R addRelationship(@RequestBody RelationshipDTO dto) {
        ValidatorUtils.validateEntity(dto);
        relationshipService.addRelationship(dto, getUserId());
        return R.ok();
    }

    /**
     * 获取所有的关系谓语
     */
    @RequestMapping("/list")
    public R getRelationshipAnno(Long taskId, Long noteId, Integer patternId, Integer source, Long importLogId) {
        List<PatternDataVo> list = relationshipService.getRelationshipsGroup(taskId, noteId, patternId, source, importLogId);
        return R.success(list);
    }

    @RequestMapping("/removeById")
    public R removeRelationship(String id, Long taskId, Long roleId) {
        relationshipService.removeById(id, taskId, roleId, getUserId());
        return R.ok();
    }


    /**
     * 审核关系标注，修改状态
     */
    @RequestMapping("/auditStatus")
    public R auditStatus(String id, Long taskId, Boolean status) {
        relationshipService.auditStatus(id, taskId, status);
        return R.ok();
    }

    /**
     * 根据noteId查询关系图
     */
    @RequestMapping("/getMapData")
    public R getMapData(Long taskId, Long noteId, Integer patternId, Integer source) {
        Graph graph = relationshipMapService.getData(taskId, noteId, patternId, source);
        return R.success(graph);
    }


    @RequestMapping("/getById")
    public R getById(String id) {
        Relationship relationship = relationshipService.getById(id);
        Map<String, String> map = relationshipService.findEntitiesById(id);
        return R.success(relationship).put("entityMap", map);
    }

    /**
     * 根据GroupId查询出所有的实体
     */
    @RequestMapping("/getEntitiesById")
    public R getEntityByGroupId(String id) {
        List<String> list = relationshipService.getEntitiesById(id);
        return R.success(list);
    }

    @RequestMapping("/importToOriginal")
    public R importToOriginal(@RequestBody PreImportDTO dto) {
        relationshipService.importToOriginal(dto, getUserId());
        return R.ok();
    }

    @RequestMapping("/importAllToOriginal")
    public R importAllToOriginal(@RequestBody PreImportDTO dto) {
        relationshipService.importAllToOriginal(dto, getUserId());
        return R.ok();
    }
}
