package org.biosino.nlp.modules.note.controller;

import com.aliyun.oss.ServiceException;
import io.swagger.annotations.Api;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.common.validator.ValidatorUtils;
import org.biosino.nlp.modules.note.dto.AttributeEntityDTO;
import org.biosino.nlp.modules.note.service.AttributeEntityService;
import org.biosino.nlp.modules.note.validata.AddEntity;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api("属性标注接口")
@RestController
@RequestMapping("/attr")
public class AttributeEntityController extends AbstractController {
    @Autowired
    private AttributeEntityService attributeEntityService;

    @PostMapping("/addAttributeEntity")
    public R addAttributeEntity(@RequestBody AttributeEntityDTO dto) {
        ValidatorUtils.validateEntity(dto, AddEntity.class);
        try {
            return R.success(attributeEntityService.addAttributeEntity(dto, getUserId()));
        } catch (ServiceException e) {
            return R.error(e.getErrorMessage());
        }
    }

    @RequestMapping("/findAllAttrByEntityLabelId")
    public R findAllAttrByEntityLabelId(Long entityLabelId, String entityId) {
        return R.success(attributeEntityService.findAllAttrByEntityLabelId(entityLabelId, entityId));
    }

    @RequestMapping("/findAttrListByEntityId")
    public R attrList(String entityId) {
        Map<String, Object> data = attributeEntityService.findAttrListByEntityId(entityId);
        return R.ok().put("data", data);
    }
}
