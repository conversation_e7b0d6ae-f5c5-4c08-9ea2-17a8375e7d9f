package org.biosino.nlp.modules.note.dto;

import lombok.Data;
import org.biosino.nlp.modules.note.validata.AddEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AttributeEntityDTO {

    @NotNull(groups = {AddEntity.class})
    private Long taskId;
    @NotNull(groups = {AddEntity.class})
    private Long projectId;
    @NotNull(groups = {AddEntity.class})
    private Long batchId;
    @NotBlank(groups = {AddEntity.class})
    private String articleId;
    @NotBlank(groups = {AddEntity.class})
    private String entityId;
    @NotNull(groups = {AddEntity.class})
    private Long attrLabelId;

    @NotNull(groups = {AddEntity.class})
    private Long roleId;

    @Size(min = 0, max = 100, groups = {AddEntity.class})
    @Valid
    private List<AttrData> data;

    @Data
    public static class AttrData {
        @NotBlank(groups = {AddEntity.class})
        private String label;
        private String value;
    }

}
