package org.biosino.nlp.modules.note.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.biosino.nlp.modules.labels.service.RelationPatternService;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.service.RelationshipService;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.note.vo.RelationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@RestController
@Api("关系标注接口")
@RequestMapping("/relation")
public class RelationController {
    @Autowired
    private RelationshipService relationshipService;
    @Autowired
    private RelationPatternService relationPatternService;


    @RequestMapping("/list")
    public R queryPage(EntitySearchDTO dto) {
        dto = BeanUtil.trimStrFields(dto);
        // 查询关系标注的分页数据
        PageIdsListVO page = relationshipService.queryPage(dto);
        // 根据模板id查询关系标注中的用户信息
        Map<Long, String> userMap = relationshipService.getUsersByPatternId(dto.getLabelId());
        // 根据模板id查询模板信息
        RelationPattern label = relationPatternService.getById(dto.getLabelId());
        return R.ok().put("page", page).put("userList", userMap).put("label", label);
    }
}
