package org.biosino.nlp.modules.note.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Data
public class RelationVO {

    private String articleId;
    private Long batchId;
    private Long noteId;
    private List<RelationItem> items;
    private Date createTime;
    private Long userId;

    @Data
    public static class RelationItem {
        private Integer order;

        private List<String> subject;
        private boolean subjectIsForeign;

        private Boolean negation;

        private List<String> annotations;

        private String relation;

        private List<String> objects;
        private boolean objectsIsForeign;
    }
}
