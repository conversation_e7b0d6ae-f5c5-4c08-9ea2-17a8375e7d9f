package org.biosino.nlp.modules.note.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.EntityAttrDTO;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 标注缓存service,用于缓存查询到的标注信息，加快标注操作反应速度
 */
@Service
public class AnnoCacheService {
    public static final String FIND_ATTR_IDS_CACHE = "find_attr_ids_cache";
    public static final String FIND_ATTR_IDS_CACHE_KEY = "'entity_id_'";

    public static final String FIND_BY_ATTRIBUTE_ID_CACHE = "find_by_attribute_id_cache";
    public static final String FIND_BY_ATTRIBUTE_ID_CACHE_KEY = "'attribute_id_'";

    public static final String FIND_ENTITY_BY_ID_CUS_CACHE = "find_entity_by_id_cus_cache";
    public static final String FIND_ENTITY_BY_ID_CUS_CACHE_KEY = "'entity_id_'";
    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private CacheManager cacheManager;

    @Cacheable(cacheNames = FIND_ATTR_IDS_CACHE, key = (FIND_ATTR_IDS_CACHE_KEY + "+#id"))
    public EntityAttrDTO findAttrIds(String id, final List<AttributeLabel> entityLabelInfos) {
        final List<Attributes> attributesList = attributesRepository.findByEntityId(id);
        return initEntityAttrDTO(attributesList, entityLabelInfos);
    }

    public EntityAttrDTO findDelAttrIds(String id, final List<AttributeLabel> entityLabelInfos, final Long annotatorId) {
        final List<Attributes> attributesList = attributesRepository.findDelByEntityId(id, annotatorId);
        return initEntityAttrDTO(attributesList, entityLabelInfos);
    }


    private EntityAttrDTO initEntityAttrDTO(final List<Attributes> attributesList, final List<AttributeLabel> entityLabelInfos) {
        final EntityAttrDTO dto = new EntityAttrDTO();

        final List<Long> attrLabelIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(attributesList)) {
            final List<String> attrIdsList = new ArrayList<>();
            for (Attributes attributes : attributesList) {
                final String attributeId = attributes.getAttributeId();
                if (StrUtil.isNotBlank(attributeId)) {
                    attrIdsList.add(attributeId);
                }

                Long attrLabelId = attributes.getAttrLabelId();
                if (attrLabelId != null) {
                    attrLabelIds.add(attrLabelId);
                }
            }
            dto.setAttrIds(attrIdsList.stream().distinct().collect(NoteServiceImpl.attrJoin()));
        }

        findAttrLabelInfo(attrLabelIds, dto, entityLabelInfos);
        return dto;
    }

    public void findAttrLabelInfo(List<Long> attrLabelIds, final EntityAttrDTO dto, final List<AttributeLabel> entityLabelInfos) {
        String attrLabelInfo = null;
        final List<Long> attrLabelInfoIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(attrLabelIds)) {
            final List<AttributeLabel> attrLabelData = attributeLabelService.findByIds(attrLabelIds);
            if (CollUtil.isNotEmpty(attrLabelData)) {
                final StringBuilder attrLabelBuilder = new StringBuilder();
                int size = attrLabelData.size();

                for (int i = 0; i < size; i++) {
                    final AttributeLabel label = attrLabelData.get(i);
                    String name = label.getName();
                    attrLabelBuilder.append("[");
                    attrLabelBuilder.append(name);
                    if (i < size - 1) {
                        attrLabelBuilder.append("] ");
                    } else {
                        attrLabelBuilder.append("]");
                    }

                    attrLabelInfoIds.add(label.getId());
                }
                attrLabelInfo = attrLabelBuilder.toString();
            }
        }

        dto.setAttrLabelInfo(attrLabelInfo);

        if (CollUtil.isNotEmpty(entityLabelInfos)) {
            final List<String> attrLabelUsedInfo = new ArrayList<>();
            for (AttributeLabel label : entityLabelInfos) {
                attrLabelUsedInfo.add(StrUtil.format("{}({})", label.getName(), attrLabelInfoIds.contains(label.getId()) ? "有" : "无"));
            }
            dto.setAttrLabelUsedInfo(CollUtil.join(attrLabelUsedInfo, ", "));
        }
    }

    private void removeAttrIdsCache(String id, Cache cache) {
        if (cache == null) {
            cache = cacheManager.getCache(FIND_ATTR_IDS_CACHE);
        }
        if (cache != null) {
            cache.evict(FIND_ATTR_IDS_CACHE_KEY.replace("'", StrUtil.EMPTY) + id);
        }
    }

    @Cacheable(cacheNames = FIND_BY_ATTRIBUTE_ID_CACHE, key = (FIND_BY_ATTRIBUTE_ID_CACHE_KEY + "+#attributeId"))
    public List<Attributes> findByAttributeId(String attributeId) {
        return attributesRepository.findByAttributeId(attributeId);
    }

    public List<Attributes> findDelByAttributeId(String attributeId, final Long annotatorId) {
        if (annotatorId != null) {
            return attributesRepository.findDelByAttributeId(attributeId, annotatorId);
        } else {
            return new ArrayList<>();
        }
    }

    private void removeAttributesCacheById(String id, Cache cache) {
        if (cache == null) {
            cache = cacheManager.getCache(FIND_BY_ATTRIBUTE_ID_CACHE);
        }
        if (cache != null) {
            cache.evict(FIND_BY_ATTRIBUTE_ID_CACHE_KEY.replace("'", StrUtil.EMPTY) + id);
        }
    }

    @Cacheable(cacheNames = FIND_ENTITY_BY_ID_CUS_CACHE, key = (FIND_ENTITY_BY_ID_CUS_CACHE_KEY + "+#entityId"))
    public Optional<Entity> findEntityByIdCus(String entityId) {
        return entityRepository.findByIdCus(entityId);
    }

    public void removeEntityCache(String id) {
        removeEntityCache(id, false);
    }

    public void removeEntityCacheByIds(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        for (String id : ids) {
            removeEntityCache(id, false);
        }
    }

    public void removeEntityCache(String id, final boolean deleteAttr) {
        Cache cache = cacheManager.getCache(FIND_ENTITY_BY_ID_CUS_CACHE);
        if (cache != null) {
            cache.evict(FIND_ENTITY_BY_ID_CUS_CACHE_KEY.replace("'", StrUtil.EMPTY) + id);
        }
        if (deleteAttr && StrUtil.isNotBlank(id)) {
            List<Attributes> attributes = attributesRepository.findByEntityId(id);
            removeAttributesCache(attributes);
        }
    }

    /**
     * 删除属性标注关系时，清理缓存
     *
     * @param deleteList
     */
    public void removeAttributesCache(List<Attributes> deleteList) {
        if (CollUtil.isNotEmpty(deleteList)) {
            final Cache attrIdsCache = cacheManager.getCache(FIND_ATTR_IDS_CACHE);
            final Cache attrCache = cacheManager.getCache(FIND_BY_ATTRIBUTE_ID_CACHE);
            for (Attributes attributes : deleteList) {
                removeAttrIdsCache(attributes.getEntityId(), attrIdsCache);
                String attributeId = attributes.getAttributeId();
                if (StrUtil.isNotBlank(attributeId)) {
                    removeAttributesCacheById(attributeId, attrCache);
                }
            }
        }
    }
}
