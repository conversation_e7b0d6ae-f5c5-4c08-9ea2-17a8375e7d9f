package org.biosino.nlp.modules.note.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AttrAnnoLabelDTO implements Serializable {
    private static final long serialVersionUID = -5231299821978860217L;
    /**
     * 实体标注id
     */
    private String entityId;
    /**
     * m_attributes中的attr_label_id字段， 对应t_attribute_label表的id
     */
    private Long attrLabelId;
    /**
     * t_attribute_label表的name
     */
    private String attrLabelName;
    /**
     * 属性标注id
     */
    private String attributeId;
}
