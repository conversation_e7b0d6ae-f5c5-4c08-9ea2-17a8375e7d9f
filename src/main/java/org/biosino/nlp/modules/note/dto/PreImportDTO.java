package org.biosino.nlp.modules.note.dto;

import lombok.Data;
import org.biosino.nlp.modules.note.validata.AddEntity;

import javax.validation.constraints.NotNull;

@Data
public class PreImportDTO {
    private String relationId;
    private String entityId;
    @NotNull(groups = {AddEntity.class})
    private Long noteId;
    @NotNull(groups = {AddEntity.class})
    private Long taskId;
    @NotNull(groups = {AddEntity.class})
    private Integer source;
    @NotNull(groups = {AddEntity.class})
    private Long roleId;

    private String currAnnoUniId;
    /**
     * 多人标注时，其他标注员导入到蓝本时传入该参数
     */
    private Long masterTaskId;

    /**
     * 多人标注设置蓝本时，传入当前url中的annotatorId
     */
    private Long annotatorId;

    private Long importLogId;

}
