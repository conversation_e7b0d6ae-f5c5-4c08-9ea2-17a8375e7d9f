package org.biosino.nlp.modules.note.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.*;

/**
 * 标注状态
 *
 * <AUTHOR>
 */
@Getter
public enum NoteStepEnum {
    //
    unmarked(0, "待标注"),
    noting(1, "标注中"),
    marked(2, "待审核"),
    reviewing(3, "审核中"),
    reviewed(4, "已合格"),
    repulse(5, "被打回"),
    corrected(6, "已修改");

    private final Integer code;
    private final String desc;

    NoteStepEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<>(8);
        for (NoteStepEnum statusEnum : NoteStepEnum.values()) {
            map.put(statusEnum.getCode(), statusEnum.getDesc());
        }
        return map;
    }


    public static List<Integer> findStep(NoteStepEnum... steps) {
        if (steps == null) {
            return null;
        }
        HashSet<NoteStepEnum> stepEnums = CollUtil.newHashSet(steps);
        if (CollUtil.isEmpty(stepEnums)) {
            return null;
        }
        List<Integer> result = new ArrayList<>();
        for (NoteStepEnum statusEnum : NoteStepEnum.values()) {
            if (stepEnums.contains(statusEnum)) {
                result.add(statusEnum.getCode());
            }
        }
        return result;
    }

    public static Integer findStep(String stepStr) {
        if (StrUtil.isBlank(stepStr)) {
            return null;
        }
        for (NoteStepEnum statusEnum : NoteStepEnum.values()) {
            if (stepStr.equals(statusEnum.name())) {
                return statusEnum.getCode();
            }
        }
        return null;
    }

    public static String findStep(Integer code) {
        if (code == null) {
            return null;
        }
        for (NoteStepEnum statusEnum : NoteStepEnum.values()) {
            if (code.equals(statusEnum.getCode())) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

}
