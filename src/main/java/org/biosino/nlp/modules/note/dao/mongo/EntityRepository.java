package org.biosino.nlp.modules.note.dao.mongo;

import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Collection;
import java.util.List;

public interface EntityRepository extends MongoRepository<Entity, String>, EntityCustomRepository {

//    Entity findTopByNoteIdAndTextIdAndStart(Long noteId, String textId, Integer start);

    // TODO sw 已废弃需要使用noteTaskId
    List<Entity> findAllByNoteIdAndDeleted(Long noteId, boolean deleted);

    boolean existsByLabelIdAndDeleted(Long id, boolean deleted);

    boolean existsByLabelIdAndSourceNotAndDeleted(Long id, int source, boolean deleted);

    Long countByIdInAndDeleted(Collection<String> ids, Boolean deleted);

    List<Entity> findAllByIdInAndDeleted(Collection<String> ids, Boolean deleted);

    Boolean existsByIdAndDeleted(String id, boolean deleted);

    void deleteByTaskId(Long taskId);

    List<Entity> findAllByTaskIdAndIsAttrAndDeleted(Long taskId, int isAttr, boolean deleted);

    void deleteByNoteIdAndSource(Long noteId, Integer source);
}
