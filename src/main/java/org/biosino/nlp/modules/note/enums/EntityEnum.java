package org.biosino.nlp.modules.note.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 标注状态
 */
public enum EntityEnum {
    //
    entity(1, "实体"),
    attributes(2, "属性"),
    relationship(3, "关系");

    private Integer code;
    private String desc;

    EntityEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<>(5);
        for (EntityEnum statusEnum : EntityEnum.values()) {
            map.put(statusEnum.getCode(), statusEnum.getDesc());
        }
        return map;
    }

    public static String getDesc(Integer code) {
        for (EntityEnum statusEnum : EntityEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
