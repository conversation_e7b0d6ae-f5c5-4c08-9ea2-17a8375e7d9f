package org.biosino.nlp.modules.note.dto;

import lombok.Data;

import java.util.List;

@Data
public class Graph {
    private List<Nodes> nodes;
    private List<Edges> edges;

    @Data
    public static class Nodes {
        private String id;
        private String conceptId;
        private String entityId;

        private String name;
        private Integer cluster;
        private Integer size;
        private String value;
        private String color;
        private List<String> attr;

    }

    @Data
    public static class Edges {
        private String id;
        private String source;
        private String target;
        private String name;
        private Boolean special;
    }
}
