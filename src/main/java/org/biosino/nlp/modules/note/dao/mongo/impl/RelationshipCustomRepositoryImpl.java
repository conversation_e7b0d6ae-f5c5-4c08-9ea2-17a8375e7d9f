package org.biosino.nlp.modules.note.dao.mongo.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.mapper.RelationMapper;
import org.biosino.nlp.modules.note.dao.NoteDao;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipCustomRepository;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.bson.Document;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-01-26 15:54
 */
@Repository
@RequiredArgsConstructor
public class RelationshipCustomRepositoryImpl implements RelationshipCustomRepository {

    private final MongoTemplate mongoTemplate;
    private final NoteDao noteDao;

    @Override
    public Relationship saveRelationship(RelationshipDTO dto) {
        Relationship relationship = RelationMapper.INSTANCE.copy(dto);
        Note note = Optional.ofNullable(noteDao.selectById(dto.getNoteId())).orElseThrow(() -> new RRException("文献不存在"));
        relationship.setId(dto.getId());
        relationship.setNoteId(note.getNoteId());
        relationship.setTaskId(dto.getTaskId());
        relationship.setProjectId(note.getProjectId());
        relationship.setBatchId(note.getBatchId());
        relationship.setArticleId(note.getArticleId());
        relationship.setCreateTime(new Date());
        relationship.setSource(PreSourceEnum.SELF.getId());

        return mongoTemplate.save(relationship);
    }

    @Override
    public boolean isRelationExists(Long taskId, String entityId) {
        Query query = new Query();
        Criteria criteria = baseCriteria().and("task_id").is(taskId)
                .orOperator(Criteria.where("items.subject").is(entityId),
                        Criteria.where("items.objects").is(entityId));
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Relationship.class);
    }

    @Override
    public void deleteAllByNoteId(Long noteId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("note_id").is(noteId));
        Update update = new Update();
        update.set("deleted", true);
        mongoTemplate.updateMulti(query, update, Relationship.class);
    }

    @Override
    public boolean existsByRelationId(Long relationId) {
        return mongoTemplate.exists(new Query(baseCriteria().and("items.relation").is(relationId)), Relationship.class);
    }

    @Override
    public List<String> findDistinctMd5(Long taskId) {
        Query query = Query.query(baseCriteria().and("task_id").is(taskId)
                .and("source").is(PreSourceEnum.SELF.getId()));
        query.with(Sort.by("md5"));
        return mongoTemplate.findDistinct(query, "md5", Relationship.class, String.class);
    }

    @Override
    public List<String> findMd5ByUser(Long taskId, Long userId) {
        Query query = Query.query(baseCriteria().and("task_id").is(taskId)
                .and("source").is(PreSourceEnum.SELF.getId()).and("annotator_id").is(userId));
        query.with(Sort.by("md5"));
        return mongoTemplate.findDistinct(query, "md5", Relationship.class, String.class);
    }


    @Override
    public long countAnnoNum(Long taskId, Long annotatorId, Long auditorId, Boolean deleted) {
        Query query = new Query();
        query.addCriteria(Criteria.where("task_id").is(taskId).and("source").is(PreSourceEnum.SELF.getId()));

        if (deleted != null) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        query.addCriteria(Criteria.where("annotator_id").is(annotatorId));
        query.addCriteria(Criteria.where("auditor_id").is(auditorId));
        return mongoTemplate.count(query, Relationship.class);
    }

    @Override
    public long countByUserId(Long projectId, Long userId, Long roleId, Long taskId) {
        Query query = Query.query(baseCriteria().and("project_id").is(projectId));
        if (taskId != null) {
            query.addCriteria(Criteria.where("task_id").is(taskId));
        }
        if (roleId == RoleEnum.annotator.getId()) {
            query.addCriteria(Criteria.where("annotator_id").is(userId));
        }
        if (roleId == RoleEnum.auditor.getId()) {
            query.addCriteria(Criteria.where("auditor_id").is(userId));
        }
        return mongoTemplate.count(query, Relationship.class);
    }

    @Override
    public long countItemsByUserId(Long projectId, Long userId, Long roleId, Long taskId) {
        Criteria criteria = baseCriteria().and("project_id").is(projectId);
        if (taskId != null) {
            criteria.and("task_id").is(taskId);
        }
        if (roleId == RoleEnum.annotator.getId()) {
            criteria.and("annotator_id").is(userId);
        }
        if (roleId == RoleEnum.auditor.getId()) {
            criteria.and("auditor_id").is(userId);
        }
        MatchOperation matchOperation = Aggregation.match(criteria);
        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation,
                Aggregation.project("items"),
                Aggregation.unwind("items"),
                Aggregation.group().count().as("count")
        );
        AggregationResults<Document> documents = mongoTemplate.aggregate(aggregation, Relationship.class, Document.class);
        List<Document> mappedResults = documents.getMappedResults();
        if (CollUtil.isEmpty(mappedResults)) {
            return 0;
        }
        return (long) mappedResults.get(0).getInteger("count");
    }

    @Override
    public List<Relationship> getRelationshipsByPatternId(Long taskId, Long noteId, Integer patternId, Integer source, Long importLogId) {
        Criteria criteria = baseCriteria();
        if (source.equals(PreSourceEnum.SELF.getId())) {
            criteria.and("task_id").is(taskId);
        } else {
            criteria.and("note_id").is(noteId);
        }
        if (patternId != null && patternId != -1) {
            criteria.and("pattern_id").is(patternId);
        }
        if (importLogId != null) {
            criteria.and("import_log_id").is(importLogId);
        }
        criteria.and("source").is(source);
        return mongoTemplate.find(Query.query(criteria), getCls());
    }

    @Override
    public Page<Relationship> queryPage(EntitySearchDTO dto) {
        // 拼接查询条件
        Query query = new Query();
        Criteria criteria = baseCriteria();
        criteria.and("pattern_id").is(dto.getLabelId());
        if (StrUtil.isNotBlank(dto.getArticleId())) {
            criteria.and("article_id").is(dto.getArticleId());
        }
        if (dto.getUserId() != null) {
            criteria.orOperator(Criteria.where("annotator_id").is(dto.getUserId()).and("auditor_id").is(null),
                    Criteria.where("annotator_id").is(null).and("auditor_id").is(dto.getUserId()));
        } else {
            criteria.orOperator(Criteria.where("annotator_id").is(null).and("auditor_id").ne(null),
                    Criteria.where("annotator_id").ne(null).and("auditor_id").is(null));
        }

        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            criteria.andOperator(
                    Criteria.where("create_time").gte(dto.getStartDate()),
                    Criteria.where("create_time").lte(DateUtil.endOfDay(dto.getEndDate()))
            );
        } else if (dto.getStartDate() != null) {
            criteria.and("create_time").gte(dto.getStartDate());
        } else if (dto.getEndDate() != null) {
            criteria.and("create_time").lte(DateUtil.endOfDay(dto.getEndDate()));
        }
        query.addCriteria(criteria);

        // 统计符合条件的总数
        long count = mongoTemplate.count(query, getCls());
        // 拼接分页和排序条件
        Sort.Direction direction = Boolean.TRUE.equals(dto.getIsAsc()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = Sort.by(direction, dto.getOrderBy());

        // 分页数据，dto.getPage()默认值从1开始，在springdata mongodb中page重0开始，所有dto.getPage() - 1
        Pageable pageable = PageRequest.of(dto.getPage() - 1, dto.getLimit(), sort);
        query.with(pageable);
        List<Relationship> list = mongoTemplate.find(query, getCls());
        // 返回分页数据
        return new PageImpl<>(list, pageable, count);
    }

    @Override
    public List<Long> getUserIdsByLabelId(Long patternId) {
        List<Long> annotatorIds = mongoTemplate.findDistinct(
                Query.query(baseCriteria().and("pattern_id").is(patternId)), "annotator_id", getCls(), Long.class);
        List<Long> auditorIds = mongoTemplate.findDistinct(
                Query.query(baseCriteria().and("pattern_id").is(patternId)), "auditor_id", getCls(), Long.class);
        annotatorIds.addAll(auditorIds);
        return annotatorIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Relationship> findPreAnnoRelation(Long noteId) {
        return mongoTemplate.find(Query.query(baseCriteria().and("note_id").is(noteId).and("source")
                .is(PreSourceEnum.CUSTOMIZE.getId())), Relationship.class);
    }

    @Override
    public void deleteByImportLogId(Long importLogId) {
        // 根据importLogId删除数据
        if (importLogId == null) {
            return;
        }
        mongoTemplate.remove(Query.query(baseCriteria().and("import_log_id").is(importLogId)), getCls());
    }

    @Override
    public Class<Relationship> getCls() {
        return Relationship.class;
    }

    @Override
    public MongoTemplate getMongoTemplate() {
        return mongoTemplate;
    }
}
