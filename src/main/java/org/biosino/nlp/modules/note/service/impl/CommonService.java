package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.modules.note.service.OpertionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 存放标注的公共代码
 *
 * <AUTHOR>
 */
@Service
public class CommonService {

    @Autowired
    private OpertionService opertionService;

    public void addLog(Long noteId, Long taskId, Integer type, String msg, Long userId) {
        if (StrUtil.isBlank(msg)) {
            return;
        }
        ThreadUtil.execAsync(() -> {
            opertionService.addEntityLog(noteId, taskId, type, msg, userId);
        });
    }

}
