package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.DateUtils;
import org.biosino.nlp.common.utils.IdUtils;
import org.biosino.nlp.modules.api.mapper.AttributeEntityMapper;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.AttributeEntityDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.AnnoCacheService;
import org.biosino.nlp.modules.note.service.AttributeEntityService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.vo.AttrVO;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.nlp.modules.note.dao.mongo.BaseLogicalDelete.LOGICAL_DELETE_FIELD_NAME;
import static org.biosino.nlp.modules.note.dao.mongo.BaseLogicalDelete.NOT_DELETE;

@Slf4j
@Service("attributeEntityService")
public class AttributeEntityServiceImpl implements AttributeEntityService {
    public static final String ATTR_LIST_KEY = "list";
    public static final String ATTR_LABEL_MAP_KEY = "attrLabelMap";
    public static final String ATTR_USER_MAP_KEY = "userMap";

    @Autowired
    private AttributeLabelService attributeLabelService;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private NoteService noteService;
    @Autowired
    private NoteTaskDao noteTaskDao;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private AnnoCacheService annoCacheService;
    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public Map<String, Object> addAttributeEntity(AttributeEntityDTO dto, Long userId) {
        final Long taskId = dto.getTaskId();
        final NoteTask noteTask = noteTaskDao.selectById(taskId);
        if (noteTask == null) {
            throw new RRException("该标注任务不存在");
        }
        final String entityId = dto.getEntityId();
        final Long attrLabelId = dto.getAttrLabelId();
        final List<Attributes> attributesList = attributesRepository.findByEntityIdAndAttrLabelId(entityId, attrLabelId);
        List<AttributeEntityDTO.AttrData> data = dto.getData();
        final List<Attributes> addList = new ArrayList<>();
        List<Attributes> deleteList = new ArrayList<>();

        if (CollUtil.isNotEmpty(data)) {

            data = data.stream().distinct().collect(Collectors.toList());

            if (CollUtil.isEmpty(attributesList)) {

                for (AttributeEntityDTO.AttrData attrData : data) {
                    addList.add(initAttr(dto, userId, attrData));
                }

            } else {

                Map<String, Attributes> attributesMap = new HashMap<>();
                for (Attributes attributes : attributesList) {
                    String key = StrUtil.EMPTY + attributes.getContent() + attributes.getAttributeId();
                    if (!attributesMap.containsKey(key)) {
                        attributesMap.put(key, attributes);
                    }
                }

                if (CollUtil.isNotEmpty(attributesMap)) {

                    final List<String> newKeys = new ArrayList<>();

                    for (AttributeEntityDTO.AttrData attrData : data) {
                        final String value = StrUtil.trimToNull(attrData.getValue());
                        final String label = StrUtil.trimToNull(attrData.getLabel());
                        final String key = label + value;
                        newKeys.add(key);
                        Attributes attribute = attributesMap.get(key);
                        if (attribute == null) {
                            attribute = initAttr(dto, userId, attrData);
                            addList.add(attribute);
                        }
                    }

                    // 处理待删除
                    Set<String> keySet = attributesMap.keySet();
                    newKeys.forEach(keySet::remove);
                    if (CollUtil.isNotEmpty(keySet)) {
                        for (String s : keySet) {
                            deleteList.add(attributesMap.get(s));
                        }
                    }
                }

            }
        } else {
            deleteList = attributesList;
        }
        if (CollUtil.isNotEmpty(deleteList)) {
            final Long roleId = dto.getRoleId();
            noteService.deleteAttributes(deleteList, roleId, userId);
        }
        if (CollUtil.isNotEmpty(addList)) {
            // TODO BUG：属性数据偶尔会重复, 打丁暂时处理 (@尚尉后续处理)
            attributesRepository.saveAll(CollUtil.distinct(addList));
            annoCacheService.removeAttributesCache(addList);
        }
        final Map<String, Object> map = new HashMap<>();
        //属性标注拖拽保存后查询
        map.put("docAllInfo", noteService.initNewDoc(noteTask.getNoteId(), taskId));
        return map;
    }

    @Override
    public List<AttrVO> findAllAttrByEntityLabelId(Long entityLabelId, String entityId) {
        final String attrEntityPrefix = "_##_";
        List<AttributeLabel> attributeLabels = attributeLabelService.findAllByEntityLabelId(entityLabelId);
        final List<AttrVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(attributeLabels)) {
            //筛选已启用的
            attributeLabels = attributeLabels.stream().filter(x -> x.getStatus() == StatusEnum.enable.getValue()).collect(Collectors.toList());
            List<Attributes> attributesList = attributesRepository.findByEntityId(entityId);
            Map<Long, LinkedHashSet<Attributes>> attributesMap = new HashMap<>();
            if (CollUtil.isNotEmpty(attributesList)) {
                final Set<String> attrIds = attributesList.stream().map(Attributes::getAttributeId).filter(Objects::nonNull).collect(Collectors.toSet());
                final Set<String> allDeleteIds = entityRepository.findAllDeleteIds(attrIds);
                deleteAttributesRelation(allDeleteIds);

                for (Attributes attributes : attributesList) {
                    if (CollUtil.isNotEmpty(allDeleteIds) && allDeleteIds.contains(attributes.getAttributeId())) {
                        continue;
                    }

                    Long attrLabelId = attributes.getAttrLabelId();
                    LinkedHashSet<Attributes> data = attributesMap.get(attrLabelId);
                    if (data == null) {
                        data = new LinkedHashSet<>();
                    }
                    data.add(attributes);
                    attributesMap.put(attrLabelId, data);
                }
            }

            for (AttributeLabel attributeLabel : attributeLabels) {
                final AttrVO vo = new AttrVO();
                final Long attrLabelId = attributeLabel.getId();
                vo.setId(attrLabelId);
                vo.setName(attributeLabel.getName());
                LinkedHashSet<Attributes> attributes = attributesMap.get(attrLabelId);
                if (CollUtil.isNotEmpty(attributes)) {
                    final LinkedHashSet<String> values = new LinkedHashSet<>();
                    final LinkedHashSet<AttrVO.Options> options = new LinkedHashSet<>();
                    for (Attributes attribute : attributes) {
                        final String attribute_id = attribute.getAttributeId();
                        final String content = attribute.getContent();
                        final String val = attribute_id == null ? content : attrEntityPrefix + attribute_id;
                        values.add(val);
                        final AttrVO.Options option = new AttrVO.Options();
                        option.setValue(val);
                        option.setLabel(content);
                        options.add(option);
                    }
                    vo.setValues(new ArrayList<>(values));
                    vo.setOptions(new ArrayList<>(options));
                }
                list.add(vo);
            }
        }
        return list;
    }

    private void deleteAttributesRelation(Set<String> allDeleteIds) {
        attributesRepository.deleteByAttributeId(allDeleteIds);
    }

    @Override
    public Map<String, Object> findAttrListByEntityId(String entityId) {
        final Map<String, Object> data = new HashMap<>();
        final List<Attributes> attributes = attributesRepository.findByEntityId(entityId);

        final List<Long> labelIds = new ArrayList<>();
        final Set<Long> userIds = new HashSet<>();
        for (Attributes attribute : attributes) {
            if (attribute.getAttrLabelId() != null) {
                labelIds.add(attribute.getAttrLabelId());
            }

            attribute.setContent(StrUtil.trimToEmpty(attribute.getContent()));

            if (attribute.getAnnotatorId() != null) {
                userIds.add(attribute.getAnnotatorId());
            }
            if (attribute.getAuditorId() != null) {
                userIds.add(attribute.getAuditorId());
            }
        }

        /*List<Long> labelIds = attributes.stream().map(Attributes::getAttrLabelId).collect(Collectors.toList());*/
        final Map<Long, String> attrLabelMap = attributeLabelService.findByIds(labelIds).stream().collect(Collectors.toMap(AttributeLabel::getId, x -> StrUtil.trimToEmpty(x.getName())));
        /*Set<Long> userIds = attributes.stream()
                .flatMap(attribute -> Stream.of(attribute.getAnnotatorId(), attribute.getAuditorId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());*/

        Map<Long, String> userMap = new HashMap<>(16);
        List<SysUserEntity> users = sysUserService.findAllByIdIn(userIds);
        for (SysUserEntity user : users) {
            userMap.put(user.getUserId(), user.getUsername());
        }
        data.put(ATTR_LIST_KEY, attributes);
        data.put(ATTR_LABEL_MAP_KEY, attrLabelMap);
        data.put(ATTR_USER_MAP_KEY, userMap);
        return data;
    }

    @Override
    public Map<String, Integer> countMapByEntityIds(Collection<String> entityIds) {
        return attributesRepository.countMapByEntityIds(entityIds);
    }

    private Attributes initAttr(AttributeEntityDTO dto, Long userId, AttributeEntityDTO.AttrData attrData) {
        final String attributeId = StrUtil.trimToNull(attrData.getValue());
        final String label = StrUtil.trimToNull(attrData.getLabel());
        /*long c = attributesRepository.countByEntityIdAndAttrLabelIdAndContentAndAttributeId(dto.getEntityId(), dto.getAttrLabelId(), label, attributeId);
        if (c > 0) {
            throw new RRException("该属性标注已被使用");
        }*/
        final Attributes attribute = AttributeEntityMapper.INSTANCE.dtoToAttributes(dto);
        attribute.setId(IdUtils.id());
        setAttrUserId(attribute, userId, dto.getRoleId());
        final Date now = DateUtils.getMongoDate(new Date());
        attribute.setCreateTime(now);
        attribute.setUpdateTime(now);
        attribute.setAttributeId(attributeId);
        attribute.setContent(label);
//        if (StrUtil.isNotBlank(attributeId)) {
//            Entity entity = entityRepository.findById(attributeId).orElseThrow(() -> new RRException("对应属性标注不存在"));
//            attribute.setAttributeAnnoMd5(entity.getMd5());
//        }
        addMd5(attribute);
        return attribute;
    }

    @Override
    public void addMd5(Attributes attribute) {
        final String entityId = attribute.getEntityId();
        final Entity entity = entityRepository.findByIdCus(entityId).orElseThrow(() -> new RRException("实体标注数据不存在"));
        attribute.setEntityMd5(entity.getMd5());
        final String attributeId = attribute.getAttributeId();
        String attrMd5;
        if (StrUtil.isNotEmpty(attributeId)) {
            attrMd5 = entityRepository.findByIdCus(attributeId).orElseThrow(() -> new RRException("属性标注数据不存在")).getMd5();
        } else {
            attrMd5 = SecureUtil.md5(StrUtil.trimToEmpty(attribute.getContent()));
        }
        attribute.setAttrMd5(attrMd5);
    }

    @Override
    public void deleteNotExistAttributesRelation() {
        final Query query = new Query(Criteria.where(LOGICAL_DELETE_FIELD_NAME).is(NOT_DELETE).and("attribute_id").exists(true));

        final List<String> attributeIds = mongoTemplate.findDistinct(query, "attribute_id", Attributes.class, String.class);
        final int total = CollUtil.size(attributeIds);
        int deleteCount = 0;
        if (total > 0) {
            final int pageSize = 5000;
            final int pages = PageUtil.totalPage(total, pageSize);
            int count = 0;
            for (int i = 0; i < pages; i++) {
                final Set<String> attrIds = new HashSet<>(attributeIds.subList(i * pageSize, Math.min((i + 1) * pageSize, total)));
                final Set<String> allDeleteIds = entityRepository.findAllDeleteIds(attrIds);

                final int deleteSize = CollUtil.size(allDeleteIds);
                count += attrIds.size();
                deleteCount += deleteSize;
                attributesRepository.deleteByAttributeId(allDeleteIds);
                log.warn("预处理数量：{}/{},此次删除数量：{}", count, total, deleteSize);
            }
        }
        log.warn("删除标注不存在的属性关系完成，总删除数量：{}", deleteCount);
    }

    /*public static String attrRelationMd5(Long attrLabelId, String attributeId, String content) {
        return SecureUtil.md5("attrLabelId" + attrLabelId + "attributeId" + attributeId + "content" + content);
    }*/

    public static void setAttrUserId(Attributes attribute, final Long userId, final Long roleId) {
        final Optional<RoleEnum> roleEnumOpt = RoleEnum.findById(roleId);
        if (roleEnumOpt.isPresent()) {
            RoleEnum role = roleEnumOpt.get();
            switch (role) {
                case annotator:
                    attribute.setAnnotatorId(userId);
                    break;
                case auditor:
                    attribute.setAuditorId(userId);
                    break;
                default:
                    throw new RRException("该用户角色没有标注权限");
            }
        } else {
            throw new RRException("角色信息错误");
        }
    }
}
