package org.biosino.nlp.modules.note.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 评论处理状态
 *
 * <AUTHOR>
 */
@Getter
public enum CommentEnum {
    wait(1, "待解决"),
    resolved(2, "已解决"),
    closed(3, "已关闭");

    private final Integer code;
    private final String desc;

    CommentEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<>(8);
        for (CommentEnum statusEnum : CommentEnum.values()) {
            map.put(statusEnum.getCode(), statusEnum.getDesc());
        }
        return map;
    }

}
