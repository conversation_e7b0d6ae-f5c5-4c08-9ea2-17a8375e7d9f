package org.biosino.nlp.modules.note.enums;

/**
 * 文章废弃状态
 */
public enum NoteInvalidEnum {

    //
    invalid(1, "废弃"),
    normal(0, "正常");

    private Integer code;
    private String desc;

    NoteInvalidEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
