package org.biosino.nlp.modules.note.dto;

import lombok.Data;

import java.util.List;

/**
 * 提问DTO
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
public class QuestionDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 文书ID
     */
    private Long noteId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 提问内容
     */
    private String questionText;

    /**
     * 违背规则内容
     */
    private String violateRule;

    /**
     * 被提问的实体ID（主要的实体）
     */
    private String entityId;

    /**
     * 提交时，相关的所有实体ID（来自m_entity表）
     */
    private List<String> entityIds;

    /**
     * 提交时，相关的所有属性关联记录ID（来自m_attributes表）
     */
    private List<String> attributeIds;

    /**
     * 是否为属性提问
     */
    private Boolean isAttributeQuestion;

    /**
     * 当前选中的实体/属性的唯一ID
     */
    private String currentUniqueId;

    /**
     * 段落上下文信息
     */
    private String contextInfo;
}
