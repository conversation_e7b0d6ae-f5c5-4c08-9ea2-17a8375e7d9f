package org.biosino.nlp.modules.note.service;

import org.biosino.nlp.modules.api.vo.PatternDataVo;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.dto.PreImportDTO;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-01-26 16:23
 */
public interface RelationshipService {

    void addRelationship(RelationshipDTO dto, Long userId);

    List<PatternDataVo> getRelationshipsGroup(Long taskId, Long noteId, Integer patternId, Integer source, Long importLogId);

    List<Relationship> getRelationships(Long noteId);

    List<Relationship> getRelationshipByNoteIds(List<Long> noteIds);

    List<Relationship> getRelationshipByProject(Long projectId);

    void removeById(String id, Long noteId, Long roleId, Long userId);

    void removeById(String id);

    Relationship getById(String groupId);

    Map<String, String> findEntitiesById(String groupId);

    List<String> getEntitiesById(String id);

    void auditStatus(String id, Long noteId, Boolean status);

    void deleteAllByProjectIdAndImportLogId(Long projectId, Long userImport);

    PageIdsListVO queryPage(EntitySearchDTO dto);

    Map<Long, String> getUsersByPatternId(Long patternId);

    void importToOriginal(PreImportDTO preImportDTO, Long userId);

    void importAllToOriginal(PreImportDTO dto, Long userId);
}
