package org.biosino.nlp.modules.note.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.project.dto.ArticleListDTO;
import org.biosino.nlp.modules.project.vo.ArticleListVO;

import java.util.List;

/**
 * 标注
 */
@Mapper
public interface NoteDao extends BaseMapper<Note> {

    Note findByBatchAndUser(Long batchId, Long userId);

    IPage<ArticleListVO> findArticleList(IPage<?> page, ArticleListDTO dto);

    /**
     * 标注员平均标注时间，单位秒
     */
    Integer avgAnnotatorTime(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 审核员平均标注时间，单位秒
     */
    Integer avgAuditorTime(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 根据project_id  查出 article_id和document_id
     *
     * @param projectId
     * @return
     */
    @Select("select article_id,document_id,batch_id,note_id from t_note where project_id = #{projectId}")
    List<Note> findAIdAndDIdByProjectId(@Param("projectId") Long projectId);

}
