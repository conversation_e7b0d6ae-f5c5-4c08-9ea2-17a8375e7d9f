package org.biosino.nlp.modules.note.dao.mongo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.nlp.common.enums.DeleteEnum;
import org.biosino.nlp.modules.note.entity.mongo.BaseMongo;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;

/**
 * mongodb 逻辑删除接口
 *
 * @param <T>
 */
public interface BaseLogicalDelete<T extends BaseMongo> {
    boolean NOT_DELETE = DeleteEnum.not_delete.getVal();

    /**
     * 逻辑删除字段名称
     */
    String LOGICAL_DELETE_FIELD_NAME = "deleted";

    Class<T> getCls();

    MongoTemplate getMongoTemplate();

    default Criteria baseCriteria() {
        return Criteria.where(LOGICAL_DELETE_FIELD_NAME).is(NOT_DELETE);
    }

    public

    default Update deleteUpdate() {
        final Update update = new Update();
        update.set(LOGICAL_DELETE_FIELD_NAME, DeleteEnum.delete.getVal());
        update.set("update_time", new Date());
        return update;
    }

    default void deleteByIdCus(String id) {
        if (StrUtil.isBlank(id)) {
            return;
        }
        final Query query = Query.query(Criteria.where("_id").is(id));
        getMongoTemplate().updateFirst(query, deleteUpdate(), getCls());
    }

    default void deleteByIdsCus(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Query query = Query.query(Criteria.where("_id").in(ids));
        getMongoTemplate().updateMulti(query, deleteUpdate(), getCls());
    }

    default void deleteCus(T t) {
        deleteByIdCus(t.getId());
    }

    default void deleteAllCus(Iterable<? extends T> items) {
        if (CollUtil.isNotEmpty(items)) {
            final List<String> ids = new ArrayList<>();
            for (T item : items) {
                ids.add(item.getId());
            }
            final Query query = Query.query(Criteria.where("_id").in(ids));
            getMongoTemplate().updateMulti(query, deleteUpdate(), getCls());
        }
    }

    default void deleteAllCus() {
        getMongoTemplate().updateMulti(new Query(), deleteUpdate(), getCls());
    }

    default Optional<T> findByIdCus(String id) {
        Optional<T> optional = Optional.empty();
        if (StrUtil.isBlank(id)) {
            return optional;
        }
        T one = getMongoTemplate().findOne(Query.query(baseCriteria().and("_id").is(id)), getCls());
        if (one != null) {
            optional = Optional.of(one);
        }
        return optional;
    }


    default List<T> findAllCus() {
        return getMongoTemplate().find(Query.query(baseCriteria()), getCls());
    }

    default List<T> findAllByIdsCus(Collection<String> ids) {
        return getMongoTemplate().find(Query.query(baseCriteria().and("_id").in(ids)), getCls());
    }

    default long countByIdsCus(Collection<String> ids) {
        return getMongoTemplate().count(Query.query(baseCriteria().and("_id").in(ids)), getCls());
    }

    default List<T> findAllCus(Sort sort) {
        return getMongoTemplate().find(Query.query(baseCriteria()).with(sort), getCls());
    }

    default boolean existsByIdCus(String id) {
        return getMongoTemplate().count(Query.query(baseCriteria().and("_id").is(id)), getCls()) > 0;
    }

    default long countCus() {
        return getMongoTemplate().count(Query.query(baseCriteria()), getCls());
    }

    default List<T> aggregateAll(Aggregation aggregation) {
        final Class<T> cLz = getCls();
        final AggregationResults<T> results = getMongoTemplate().aggregate(aggregation, getMongoTemplate().getCollectionName(cLz), cLz);
        return results.getMappedResults();
    }

    default Criteria andQuery(List<Criteria> condition) {
        if (CollUtil.isEmpty(condition)) {
            return new Criteria();
        }
        return new Criteria().andOperator(condition.toArray(new Criteria[0]));
    }

    default Criteria andQuery(Criteria... condition) {
        if (ArrayUtil.isEmpty(condition)) {
            return new Criteria();
        }
        return new Criteria().andOperator(condition);
    }

}
