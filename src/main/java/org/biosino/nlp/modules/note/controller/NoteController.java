package org.biosino.nlp.modules.note.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.common.validator.ValidatorUtils;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.MetaMapDTO;
import org.biosino.nlp.modules.note.dto.PreImportDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.modules.note.validata.AddEntity;
import org.biosino.nlp.modules.note.validata.DeleteEntity;
import org.biosino.nlp.modules.note.validata.UpdateEntity;
import org.biosino.nlp.modules.note.vo.ArticleNoteVO;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标注功能控制层
 *
 * <AUTHOR>
 */
@Api("实体标注接口")
@RestController
@RequestMapping("/note")
public class NoteController extends AbstractController {


    @Autowired
    private NoteService noteService;

    @Autowired
    private EntityLabelService entityLabelService;

    @Autowired
    private NoteTaskService noteTaskService;

    /**
     * 根据NoteId查询具体Note
     */
    @GetMapping("/getNoteById/{noteId}/{roleId}")
    public R getNoteById(@PathVariable("noteId") Long noteId, @PathVariable("roleId") Long roleId,
                         @RequestParam(required = false) Long annotatorId, @RequestParam(required = false) Long taskId) {
        Note note = noteService.getById(noteId);
        if (note == null) {
            throw new RRException("未查询到该Note记录，请检查");
        }
        List<SourceVO> sources = noteService.existSource(note, roleId, annotatorId, taskId);

        ArticleNoteVO vo = new ArticleNoteVO();
        vo.setNote(note);
        vo.setSources(sources);
        return R.success(vo);
    }

    /**
     * 根据文章ID，拿到文章全文信息，且回显标记的数据
     */
    @GetMapping("/getDocument/{documentId}/{noteTaskId}/{source}")
    public R getDocument(@PathVariable String documentId, @PathVariable Long noteTaskId,
                         @PathVariable Integer source, Long noteId,
                         @RequestParam(required = false) Boolean showAnnoOnly,
                         @RequestParam(required = false) Long annotatorId,
                         @RequestParam(required = false) Long currImportLogId,
                         @RequestParam(required = false) String discussionId
    ) {
        final Document document = noteService.getDocumentArticle(StrUtil.trim(documentId), noteTaskId, source, noteId,
                Boolean.TRUE.equals(showAnnoOnly), annotatorId, currImportLogId, discussionId);
        return R.success(document);
    }

    /**
     * 获取当前项目的一级标签
     */
    @GetMapping("/getFirstLabels/{projectId}")
    @Cacheable(cacheNames = "getFirstLabels_cache")
    public R getFirstLabels(@PathVariable Long projectId) {
        List<EntityLabel> entityLabelList = entityLabelService.findAllEnableByProjectId(projectId);
        return R.success(entityLabelList);
    }


    /**
     * 新增实体标注
     */
    @PostMapping("/addEntity")
    public R addEntity(@RequestBody AnnoDTO annoDTO) {
        ValidatorUtils.validateEntity(annoDTO, AddEntity.class);
        return R.success(noteService.addEntity(annoDTO, getUserId()));
    }

    /**
     * 删除实体标注
     */
    @PostMapping("/deleteEntity")
    public R deleteEntity(@RequestBody AnnoDTO annoDTO) {
        ValidatorUtils.validateEntity(annoDTO, DeleteEntity.class);
        if (annoDTO.getMultiple()) {
            return noteService.deleteMultipleEntity(annoDTO, getUserId());
        } else {
            return noteService.deleteEntity(annoDTO.getEntityId(), annoDTO.getRoleId(), getUserId(), Boolean.TRUE.equals(annoDTO.getDeleteAttrAsWell()), Boolean.TRUE.equals(annoDTO.getForceDelAttr()));
        }
    }

    /**
     * 查询标注的实体
     */
    @GetMapping("/getEntity")
    public R getEntity(String id, @RequestParam(required = false) String source) {
        return R.success(noteService.getEntity(id, source));
    }

    @GetMapping("/findAnnotateByUniId")
    public R findAnnotateByUniId(String uniId) {
        return R.success(noteService.findAnnotateByUniId(uniId));
    }

    /**
     * 更新实体标注的批注
     */
    @PostMapping("/updateEntityAnnotate")
    public R updateEntityAnnotate(@RequestBody AnnoDTO annoDTO) {
        ValidatorUtils.validateEntity(annoDTO, UpdateEntity.class);
        Document document;
        if (Boolean.TRUE.equals(annoDTO.getMultiple())) {
            document = noteService.updateMultipleEntityAnnotate(annoDTO, getUserId());
        } else {
            document = noteService.updateEntityAnnotate(annoDTO, getUserId());
        }
        return R.success(document);
    }

    /**
     * 获取UMLSConcept
     */
    @RequestMapping("/getUMLS")
    @CacheEvict(value = "getUMLS_cache", allEntries = true)
    public R getUmls(String conceptName, String conceptId, Long projectId) {
        List<MetaMapDTO> list = noteService.getUmls(conceptName, conceptId, projectId);
        return R.success(list);
    }

    /**
     * 更新实体标注uml标签
     */
    @PostMapping("/updateEntityUmls")
    public R updateEntityUmls(@RequestBody AnnoDTO annoDTO) {
        ValidatorUtils.validateEntity(annoDTO, UpdateEntity.class);
        if (annoDTO.getMultiple()) {
            noteService.updateMultipleEntityUmls(annoDTO, getUserId());
        } else {
            noteService.updateEntityUmls(annoDTO, annoDTO.getEntityId(), getUserId());
        }
        return R.ok();
    }

    /**
     * 根据ConceptId获取UMLSConcept
     */
    @RequestMapping("/getUMLSById")
    @CacheEvict(value = "getUMLSById_cache", allEntries = true)
    public R getUmlsById(String conceptId, Long projectId) {
        List<MetaMapDTO> list = noteService.getUmlsById(conceptId, projectId);
        return R.success(list);
    }

    /**
     * 查询实体的UMLS
     */
    @PostMapping("/getEntityAndUMlS")
    public R getEntityAndUmls(@RequestBody List<String> entities) {
        List<EntityDataVo> entityDataVos = noteService.getEntityAndUmls(entities);
        return R.success(entityDataVos);
    }

    /**
     * 预标注选中标注导入到实体标注
     */
    @PostMapping("/selectedImportToOriginal")
    public R selectedImportToOriginal(@RequestBody PreImportDTO dto) {
        ValidatorUtils.validateEntity(dto, AddEntity.class);
        noteService.selectedImportToOriginal(dto, getUserId());
        return R.ok();
    }

    /**
     * 预标注全部标注导入到实体标注
     */
    @PostMapping("/preImportToEntity")
    public R preImportToEntity(@RequestBody PreImportDTO dto) {
        ValidatorUtils.validateEntity(dto, AddEntity.class);
        return noteService.preImportToEntity(dto, getUserId());
    }


    /**
     * 审核员设置蓝本
     */
    @SysLog("切换蓝本")
    @PostMapping("/setMaster")
    public R setMaster(@RequestBody PreImportDTO dto) {
        return R.success(noteService.setMaster(dto));
    }

    /**
     * 重新审核文书（从已合格状态重新进入审核流程）
     */
    @SysLog("重新审核文书")
    @PostMapping("/reReview")
    public R reReview(@RequestParam Long noteId, @RequestParam Long taskId) {
        if (noteId == null) {
            throw new RRException("文书ID不能为空");
        }
        if (taskId == null) {
            throw new RRException("任务ID不能为空");
        }

        // 调用现有的rework方法，角色为审核员
        noteTaskService.rework(taskId, RoleEnum.auditor.getId(), getUserId());
        return R.ok("重新审核成功");
    }

    /**
     * 获取当前task实体清单
     *
     * @param taskId 标记任务id
     * @param source 来源（自标注、预标注等）
     */
    @GetMapping("/getEntityListTree")
    public R getEntityListTree(Long taskId, Integer source) {
        return R.success(noteService.getEntityListTree(taskId, source));
    }

    /**
     * 分配文章（2.0接口，3.0已废弃）
     */
    @GetMapping("/assignArticle")
    public R assignArticle(Long batchId, Long roleId) {
        // 自动分配文章
        Note note = noteService.assignArticle(batchId, getUserId(), roleId);
        if (note == null) {
            return R.success(0L);
        }
        return R.success(note.getNoteId());
    }
}
