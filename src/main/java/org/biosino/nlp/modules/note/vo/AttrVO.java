package org.biosino.nlp.modules.note.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 属性标注下拉框初始化vo
 */
@Data
public class AttrVO implements Serializable {
    private static final long serialVersionUID = 9129279609225102084L;
    private Long id;
    private String name;
    private List<String> values = new ArrayList<>();
    private List<Options> options = new ArrayList<>();

    @Data
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Options implements Serializable {
        private static final long serialVersionUID = -9196347784625821525L;
        private String value;
        private String label;
    }
}
