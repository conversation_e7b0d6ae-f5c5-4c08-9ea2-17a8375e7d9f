package org.biosino.nlp.modules.note.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.nlp.common.enums.PreSourceEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class SourceVO implements Serializable {
    private static final long serialVersionUID = 6960570564061580097L;

    private String title;
    /**
     * 预标注类型id
     *
     * @see PreSourceEnum
     */
    private Integer value;
    private Long taskId;
    private Long userId;
    private Integer master;
    private Integer invalid;

    private Long importLogId;

    public SourceVO() {
    }

    public SourceVO(String title, Integer value) {
        this(title, value, null, null, null, null);
    }

    public SourceVO(String title, Integer value, Long taskId, Long userId, Integer master, Integer invalid) {
        this(title, value, taskId, userId, master, invalid, null);
    }

    public SourceVO(String title, Integer value, Long taskId, Long userId, Integer master, Integer invalid, Long importLogId) {
        this.title = title;
        this.value = value;
        this.taskId = taskId;
        this.userId = userId;
        this.master = master;
        this.invalid = invalid;
        this.importLogId = importLogId;
    }
}
