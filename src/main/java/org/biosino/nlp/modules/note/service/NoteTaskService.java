package org.biosino.nlp.modules.note.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.note.dto.AssignTaskDTO;
import org.biosino.nlp.modules.note.dto.TaskDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.vo.*;

import java.util.List;

/**
 * 标注任务列表
 *
 * <AUTHOR>
 */
public interface NoteTaskService extends IService<NoteTask> {

    List<ProjectBatchVO> queryProjectList(Long userId, Long roleId);

    IPage<TaskListVO> queryTaskList(TaskDTO taskDTO);

    PageIdsListVO queryTaskList4VO(TaskDTO taskDTO, Long userId);

    List<UserVO> queryUserList(Long batchId, Long roleId);

    TaskDetailVO getTaskByNote(AssignTaskDTO dto);

    void submitAnnotatorTask(Long taskId, boolean force);

    void submitAuditorTask(Long taskId);

    NoteTask assignAnnotatorTask(AssignTaskDTO assignTaskDTO);

    NoteTask assignAuditorTask(AssignTaskDTO assignTaskDTO);

    List<AnnoUserVO> findAnnoByNoteId(Long noteId, Long userId);

    void repulse(List<AnnoUserVO> user, Long userId);

    List<Document.InfoDTO> findMetadataByNoteId(String noteId);

    void invalid(Long noteId, Integer invalid, Long userId);

    void rework(Long taskId, Long roleId, Long userId);

    void batchReview(Long[] ids, Long annotatorId, Long userId);

    void cancelTask(Long roleId, Long taskId, Long userId);
}
