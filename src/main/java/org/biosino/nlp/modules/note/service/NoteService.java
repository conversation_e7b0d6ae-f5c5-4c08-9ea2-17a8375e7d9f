package org.biosino.nlp.modules.note.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.MetaMapDTO;
import org.biosino.nlp.modules.note.dto.PreImportDTO;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.vo.AddEntityVO;
import org.biosino.nlp.modules.note.vo.EntityListTreeVO;
import org.biosino.nlp.modules.note.vo.SourceVO;
import org.biosino.nlp.modules.project.dto.NoteDTO;

import java.util.List;
import java.util.Map;

/**
 * 标注
 *
 * <AUTHOR>
 */
public interface NoteService extends IService<Note> {
    Note assignArticle(Long batchId, Long userId, Long roleId);

    Document getDocumentArticle(String documentId, Long noteTaskId, Integer source, Long noteId, boolean showAnnoOnly, Long annotatorId, Long currImportLogId, String discussionId);

    Map<String, String> findIdAndContentMap(final String documentId);

    AddEntityVO addEntity(AnnoDTO annoDTO, Long userId);

    String getAddEntityMsg(Entity entity);

    R deleteEntity(String uniqueid, Long roleId, Long userId, boolean deleteAttrAsWell, boolean forceDelAttr);

    void deleteAttributes(List<Attributes> deleteList, final Long roleId, final Long userId);

    Document initNewDoc(Long noteId, Long taskId);

    R deleteMultipleEntity(AnnoDTO annoDTO, Long userId);

    Document updateEntityAnnotate(AnnoDTO annoDTO, Long userId);

    Document updateMultipleEntityAnnotate(AnnoDTO annoDTO, Long userId);

    void updateEntityUmls(final AnnoDTO annoDTO, final String uniqueid, final Long userId);

    void updateMultipleEntityUmls(AnnoDTO annoDTO, Long userId);

    Entity getEntity(final String id, final String source);

    List<SourceVO> existSource(Note note, Long roleId, Long annotatorId, Long taskId);

    Map<String, NoteDTO> findAIdAndDIdByProjectId(Long projectId);

    List<MetaMapDTO> getUmls(String conceptName, String conceptId, Long projectId);

    List<MetaMapDTO> getUmlsById(String conceptId, Long projectId);

    List<EntityDataVo> getEntityAndUmls(List<String> ids);

    Note findFirstByArticleIdAndProjectId(String articleId, Long projectId);

    Note findReviewedByArticleIdAndBatchId(String articleId, Long batchId);

    List<Note> findByArticleIdAndProjectId(String articleId, Long projectId);

    R preImportToEntity(PreImportDTO dto, Long userId);

    String selectedImportToOriginal(PreImportDTO dto, Long userId);

    List<SourceVO> setMaster(PreImportDTO dto);

    List<EntityListTreeVO> getEntityListTree(Long taskId, Integer source);

    void restoreDeletedEntry();

    Entity findAnnotateByUniId(String uniId);

    List<Note> findAllByBatchIdAndStepAndNotInvalid(Long batchId, Integer step);

    List<Note> findAllByBatchIdAndArticleIdInAndNotInvalid(Long sourceBatchId, List<String> articleIds);

    Entity getEntityNameById(String entityId);
}
