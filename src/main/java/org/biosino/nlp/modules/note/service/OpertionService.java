package org.biosino.nlp.modules.note.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.note.dto.OperationDTO;
import org.biosino.nlp.modules.note.entity.Operation;

import java.util.Map;

public interface OpertionService extends IService<Operation> {

    PageUtils queryPage(OperationDTO operationDTO);

    /**
     * 获取该文章中所有文章的用户
     */
    Map<Long, String> getAccount(OperationDTO dto);

    void addEntityLog(Long noteId, Long taskId, Integer type, String msg, Long userId);

}
