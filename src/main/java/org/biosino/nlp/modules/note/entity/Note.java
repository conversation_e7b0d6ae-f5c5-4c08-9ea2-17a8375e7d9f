package org.biosino.nlp.modules.note.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * MySQL 标注表，文章和批次的关联
 *
 * <AUTHOR>
 */
@Data
@TableName("t_note")
public class Note implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long noteId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 批次ID，一个批次对应多篇标注文章
     */
    private Long batchId;

    /**
     * 文档集内容ID
     */
    private String documentId;

    /**
     * 文档字数
     */
    private Integer wordsCount = 0;

    /**
     * 文章ID
     */
    private String articleId;

    /**
     * 文章标题
     */
    private String articleName;

    /**
     * 文章当前标注进展步骤：[0未标注  1标注中  2已标注  3审核中  4已审核]
     */
    private Integer step;

    /**
     * 是否废弃[1.废弃  0：未废弃]
     */
    private Integer invalid = 0;

    /**
     * 已经被几个标注员拉取（初始是0）
     */
    private Integer pullCount = 0;

    private Date createTime;
    private Date updateTime;

    /**
     * 是否删除[1.已删除值  0.未删除]
     */
    @TableLogic
    private Integer deleted;

}
