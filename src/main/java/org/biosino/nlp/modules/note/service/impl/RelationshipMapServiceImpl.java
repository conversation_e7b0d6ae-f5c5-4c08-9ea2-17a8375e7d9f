package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.service.RelationshipMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021-03-5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RelationshipMapServiceImpl implements RelationshipMapService {

    private final static String ENTITY_PRE = "E_";
    private final static String STATEMENT_PRE = "S_";
    private final static String RELATION_PRE = "R_";
    private final static String VIRTUAL_NODE = "V_";
    private final static String S = "subject";
    private final static String O = "objects";
    private final static String DELIMITER = "#";
    private final static String COMBINATION = "Combination";
    private final static String PARTS = "is parts of";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RelationLabelService relationLabelService;

    @Autowired
    private EntityLabelService entityLabelService;

    @Autowired
    private AttributeLabelService attributeLabelService;

    @Autowired
    private AttributesRepository attributesRepository;

    @Override
    public Graph getData(Long taskId, Long noteId, Integer patternId, Integer source) {
        Criteria criteria = Criteria
                .where("deleted").is(false)
                .and("correct").is(true)
                .and("note_id").is(noteId)
                .and("source").is(source);
        if (PreSourceEnum.SELF.getId() == source) {
            criteria.and("task_id").is(taskId);
        }
        Query relationsQuery = Query.query(criteria);
        if (patternId != null && patternId != -1) {
            relationsQuery.addCriteria(Criteria.where("pattern_id").is(patternId));
        }
        List<Relationship> relationshipList = mongoTemplate.find(relationsQuery, Relationship.class);
        if (CollUtil.isEmpty(relationshipList)) {
            return null;
        }

        // 提取出所有涉及实体id
        Set<String> entitySet = relationshipList.stream()
                .flatMap(r -> r.getItems().stream())
                .flatMap(item -> Stream.of(item.getSubject(), item.getObjects()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // 加载所有实体的属性标注
        Map<String, List<Attributes>> entityAttrMap = attributesRepository.findAllEntityIdIn(entitySet).stream().collect(Collectors.groupingBy(Attributes::getEntityId));
        // 加载所有的关系标签id
        Set<Long> relationIds = relationshipList.stream()
                .flatMap(r -> r.getItems().stream())
                .map(Relationship.RelationItem::getRelation)
                .collect(Collectors.toSet());
        // 加载所有的关系标签
        Map<Long, RelationLabel> labelMap = relationLabelService.listByIds(relationIds).stream().collect(Collectors.toMap(RelationLabel::getId, x -> x));

        // 把所有的实体加载到内存
        Query query = Query.query(Criteria.where("id").in(entitySet));
        query.fields().include("entityInfos").include("conceptId").include("label_id");
        List<Entity> entities = mongoTemplate.find(query, Entity.class);
        List<Long> entityLabelIds = entities.stream().map(Entity::getLabelId).collect(Collectors.toList());
        Map<Long, EntityLabel> entityLabelMap = entityLabelService.listByIds(entityLabelIds).stream().collect(Collectors.toMap(EntityLabel::getId, x -> x));
        Map<String, Entity> entityMap = entities.stream().collect(Collectors.toMap(Entity::getId, x -> x));

        // 加载所有的属性标注
        Map<Long, String> attrLabelMap = attributeLabelService.list(Wrappers.<AttributeLabel>lambdaQuery().in(AttributeLabel::getEntityLabelId, entityLabelIds))
                .stream().collect(Collectors.toMap(AttributeLabel::getId, AttributeLabel::getName));


        // 生成图形
        Graph graph = new Graph();
        Set<Graph.Nodes> nodes = new HashSet<>(32);
        Set<Graph.Edges> edges = new HashSet<>(32);

        // String: 三元组ID, Set: 自动生成的线段ID
        Map<String, String> edgesMap = new HashMap<>(32);


        try {
            for (Relationship ship : relationshipList) {
                Map<String, String> idItemMap = new HashMap<>(8);
                List<Relationship.RelationItem> relationships = ship.getItems();
                relationships.sort(Comparator.comparingInt(Relationship.RelationItem::getOrder));
                for (Relationship.RelationItem relationship : relationships) {
                    relationship.setId(STATEMENT_PRE + IdUtil.simpleUUID());
                    idItemMap.put(relationship.getOrder().toString(), relationship.getId());
                }
                for (Relationship.RelationItem relationship : relationships) {
                    relationship.setId(STATEMENT_PRE + IdUtil.simpleUUID());
                    idItemMap.put(relationship.getOrder().toString(), relationship.getId());
                    if (relationship.getSubject().size() == 1 && !isEntityId(relationship.getSubject().get(0))) {
                        String subject = relationship.getSubject().get(0);
                        if (subject.contains(DELIMITER)) {
                            String temp = subject.substring(0, subject.indexOf("#"));
                            // 目标三元组的#主语||宾语
                            if (subject.endsWith(S)) {
                                relationship.setSubject(CollUtil.newArrayList(idItemMap.get(temp) + DELIMITER + S));
                            } else if (subject.endsWith(O)) {
                                relationship.setSubject(CollUtil.newArrayList(idItemMap.get(temp) + DELIMITER + O));
                            }
                        } else {
                            relationship.setSubject(CollUtil.newArrayList(idItemMap.get(subject)));
                        }
                    }
                    if (relationship.getObjects().size() == 1 && !isEntityId(relationship.getObjects().get(0))) {
                        String objects = relationship.getObjects().get(0);
                        if (objects.contains(DELIMITER)) {
                            String temp = objects.substring(0, objects.indexOf("#"));
                            // 目标三元组的#主语||宾语
                            if (objects.endsWith(S)) {
                                relationship.setObjects(CollUtil.newArrayList(idItemMap.get(temp) + DELIMITER + S));
                            } else if (objects.endsWith(O)) {
                                relationship.setObjects(CollUtil.newArrayList(idItemMap.get(temp) + DELIMITER + O));
                            }
                        } else {
                            relationship.setObjects(CollUtil.newArrayList(idItemMap.get(objects)));
                        }
                    }
                }
            }
            for (Relationship ship : relationshipList) {
                Map<String, String> subVirtualNodeMap = new HashMap<>(8);
                Map<String, String> objVirtualNodeMap = new HashMap<>(8);

                String group = ship.getId();
                List<Relationship.RelationItem> relationships = ship.getItems();
                relationships.sort(Comparator.comparingInt(Relationship.RelationItem::getOrder));

                Set<Integer> clusterSet = new TreeSet<>();
                clusterSet.add(3);
                for (Relationship.RelationItem relationship : relationships) {
                    String relationshipId = relationship.getId();

                    RelationLabel label = labelMap.get(relationship.getRelation());

                    // 如果主语有多个，说明是多个实体，需要构建虚拟中心节点
                    String subNodeId = getNodesId(relationship.getSubject(), true, clusterSet, entityMap, nodes, edges, edgesMap, subVirtualNodeMap, objVirtualNodeMap, group, relationshipId, entityLabelMap, entityAttrMap, attrLabelMap);
                    String objNodeId = getNodesId(relationship.getObjects(), false, clusterSet, entityMap, nodes, edges, edgesMap, subVirtualNodeMap, objVirtualNodeMap, group, relationshipId, entityLabelMap, entityAttrMap, attrLabelMap);

                    // 建立主谓宾关系
                    Graph.Edges edge = new Graph.Edges();
                    edge.setId(RELATION_PRE + IdUtil.simpleUUID());
                    edge.setSource(subNodeId);
                    edge.setTarget(objNodeId);
                    edge.setName((Boolean.TRUE.equals(relationship.getNegation()) ? "[不]" : "") + label.getName());
                    edge.setSpecial(label.getSpecial() == 1);
                    edges.add(edge);

                    // 保存关系和三元组的临时映射
                    if (edgesMap.containsKey(relationshipId)) {
                        String set = edgesMap.get(relationshipId);
                        edgesMap.put(relationshipId, set);
                    } else {
                        edgesMap.put(relationshipId, edge.getId());
                    }
                }
            }
            // 将节点进行去重修改
            Map<String, List<String>> nodeIds = new HashMap<>();
            nodes.stream().filter(it -> it.getConceptId() == null).collect(Collectors.groupingBy(Graph.Nodes::getEntityId))
                    .forEach((k, v) -> nodeIds.put(IdUtil.simpleUUID(), v.stream().map(Graph.Nodes::getId).collect(Collectors.toList())));

            nodes.stream().filter(it -> it.getConceptId() != null).collect(Collectors.groupingBy(Graph.Nodes::getConceptId))
                    .forEach((k, v) -> nodeIds.put(IdUtil.simpleUUID(), v.stream().map(Graph.Nodes::getId).collect(Collectors.toList())));

            Set<Graph.Nodes> nowNodes = new HashSet<>();
            for (Map.Entry<String, List<String>> entry : nodeIds.entrySet()) {
                String id = entry.getValue().get(0);
                Graph.Nodes node = nodes.stream().filter(x -> x.getId().equals(id)).findFirst().get();
                node.setId(entry.getKey());
                nowNodes.add(node);
            }
            nodes = nowNodes;

            // 对边进行去重修改
            edges.forEach(it -> nodeIds.forEach((k, v) -> {
                if (v.contains(it.getSource())) {
                    it.setSource(k);
                }
                if (v.contains(it.getTarget())) {
                    it.setTarget(k);
                }
            }));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建图集合出错，集合：{}", JSON.toJSONString(relationshipList));
            return null;
        }

        graph.setNodes(CollUtil.newArrayList(nodes));
        graph.setEdges(CollUtil.newArrayList(edges));
        return graph;
    }


    private String getNodesId(List<String> nodeList, boolean subNode, Set<Integer> clusterSet, Map<String, Entity> entityMap, Set<Graph.Nodes> nodes,
                              Set<Graph.Edges> edges, Map<String, String> edgesMap,
                              Map<String, String> subVirtualNodeMap, Map<String, String> objVirtualNodeMap,
                              String group, String relationshipId, Map<Long, EntityLabel> entityLabelMap, Map<String, List<Attributes>> entityAttrMap, Map<Long, String> attrLabelMap) throws Exception {
        if (CollUtil.isEmpty(nodeList)) {
            throw new Exception("获取节点数据异常，请检查数据库中关系表存储的数据格式是否异常。");
        }
        // 多个实体则创建虚拟节点
        if (nodeList.size() > 1) {

            Graph.Nodes vNode = new Graph.Nodes();
            vNode.setId(VIRTUAL_NODE + "_" + IdUtil.simpleUUID());
            vNode.setEntityId(IdUtil.simpleUUID());
            vNode.setName(COMBINATION);
            vNode.setCluster(2);
            nodes.add(vNode);

            for (String sub : nodeList) {
                // 生成子节点的主语节点
                Graph.Nodes tempNodes = createNode(sub, group, entityMap, entityLabelMap, entityAttrMap, attrLabelMap);
                if (tempNodes == null) {
                    continue;
                }
                tempNodes.setCluster(1);
                tempNodes.setSize(35);
                nodes.add(tempNodes);

                Graph.Edges edge = new Graph.Edges();
                edge.setId(RELATION_PRE + IdUtil.simpleUUID());
                edge.setSource(tempNodes.getId());
                edge.setTarget(vNode.getId());
                edge.setName(PARTS);
                edge.setSpecial(false);
                edges.add(edge);
                if (subNode) {
                    subVirtualNodeMap.put(relationshipId, vNode.getId());
                } else {
                    objVirtualNodeMap.put(relationshipId, vNode.getId());
                }
            }
            return vNode.getId();
            // 单实体，或三元组
        } else {
            String subject = nodeList.get(0);
            // 如果主语||宾语是三元组
            if (subject.startsWith(STATEMENT_PRE)) {

                // 处理 "S_001#O" 的情况（三元组中的主或宾）
                if (subject.contains(DELIMITER)) {

                    String temp = subject.substring(0, subject.indexOf("#"));

                    // 目标三元组的#主语||宾语
                    if (subject.endsWith(S)) {
                        return subVirtualNodeMap.get(temp);
                    } else if (subject.endsWith(O)) {
                        return objVirtualNodeMap.get(temp);
                    }
                    // 直接就是整个目标三元组
                } else {
                    return edgesMap.get(subject);
                }
            } else {
                // 生成节点
                Graph.Nodes node = createNode(subject, group, entityMap, entityLabelMap, entityAttrMap, attrLabelMap);
                if (node != null) {
                    // 查重
                    long count = nodes.stream().filter((x) -> x.getId().equals(node.getId())).count();
                    // 如果没有存在相同节点，才存入
                    if (count == 0) {
                        Integer max = CollUtil.max(clusterSet);
                        node.setCluster(max);
                        clusterSet.add(max + 1);
                        nodes.add(node);
                    }
                    if (subNode) {
                        subVirtualNodeMap.put(relationshipId, node.getId());
                    } else {
                        objVirtualNodeMap.put(relationshipId, node.getId());
                    }
                    return node.getId();
                }
            }
        }
        return null;
    }

    /**
     * 创建节点
     */
    private Graph.Nodes createNode(String id, String group, Map<String, Entity> entityMap, Map<Long, EntityLabel> entityLabelMap, Map<String, List<Attributes>> entityAttrMap, Map<Long, String> attrLabelMap) {
        // if (id.length() != 32) {
        if (!isEntityId(id)) {
            return null;
        }
        Graph.Nodes nodes = new Graph.Nodes();
        nodes.setId(id + DELIMITER + group);
        nodes.setEntityId(id);
        if (entityMap != null && entityMap.get(id) != null) {
            nodes.setConceptId(entityMap.get(id).getConceptId());
        }
        if (entityAttrMap.containsKey(id)) {
            Map<Long, List<Attributes>> map = entityAttrMap.get(id).stream().collect(Collectors.groupingBy(Attributes::getAttrLabelId));
            List<String> attr = new ArrayList<>();
            map.forEach((k, v) -> {
                attr.add(StrUtil.format("<strong>{}</strong>：{}", attrLabelMap.get(k), v.stream().map(Attributes::getContent).collect(Collectors.joining(";  "))));
            });
            nodes.setAttr(attr);
        }

        Entity entity = entityMap.get(id);
        if (entity == null) {
            log.error("createNode时，Entity为空，EntityId:{}", id);
            return null;
        }
        // 设置关系图的节点颜色
        EntityLabel entityLabel = entityLabelMap.get(entity.getLabelId());
        nodes.setColor(entityLabel.getColor());
        // TODO sw 关系
        List<String> collect = entity.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList());
        String text = CollUtil.join(collect, "/");
        if (StrUtil.isNotBlank(text)) {
            nodes.setName(text);
        }
        nodes.setValue(entity.getConceptId());
        return nodes;
    }

    /**
     * 获取剔除"E_"后的实体ID
     */
    private String getEntityId(String originId) {
        return originId.substring(2);
    }

    /**
     * 从内存中查询出实体
     */
    private List<String> getEntity(List<String> subjects) {
        List<String> result = new ArrayList<>();
        for (String subject : subjects) {
            if (subject.length() == 32) {
                result.add(subject);
            }
        }
        return result;
    }

    /**
     * 校验字符串是不是实体id
     *
     * @param id 传入的id字符串
     * @return true or false
     */
    private boolean isEntityId(String id) {
        if (id != null && id.length() == 32) {
            return true;
        }
        return false;
    }
}
