package org.biosino.nlp.modules.note.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseMongo implements Serializable {
    @Transient
    private static final long serialVersionUID = -5857541097906416384L;
    @Id
    private String id;

    @Field(name = "update_time")
    private Date updateTime;

    @Indexed
    private boolean deleted;
}
