package org.biosino.nlp.modules.note.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.Api;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.note.dto.AssignTaskDTO;
import org.biosino.nlp.modules.note.dto.TaskDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.vo.*;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 任务列表控制层
 *
 * <AUTHOR>
 */
@Api("任务列表接口")
@RestController
@RequestMapping("/task")
public class NoteTaskController extends AbstractController {

    @Autowired
    private NoteTaskService noteTaskService;

    /**
     * 当前用户参与的项目批次
     */
    @RequestMapping("/queryProjectList")
    public R queryProjectList(Long roleId) {
        List<ProjectBatchVO> projectList = noteTaskService.queryProjectList(getUserId(), roleId);
        return R.success(projectList);
    }

    /**
     * 查询任务列表
     */
    @RequestMapping("/queryTaskList")
    public R queryTaskList(TaskDTO taskDTO) {
        Long userId = getUserId();
        PageIdsListVO vo = noteTaskService.queryTaskList4VO(taskDTO, userId);
        return R.success(vo);
    }

    /**
     * 查询参与当前批次的标注员或管理员
     */
    @RequestMapping("/queryUserList")
    public R queryUserList(Long batchId, Long roleId) {
        List<UserVO> users = noteTaskService.queryUserList(batchId, roleId);
        return R.success(users);
    }

    /**
     * 查询参与当前批次的标注员或管理员
     */
    @RequestMapping("/findAnnoByNoteId")
    public R findAnnoByTaskId(Long noteId) {
        List<AnnoUserVO> users = noteTaskService.findAnnoByNoteId(noteId, getUserId());
        return R.success(users);
    }

    /**
     * 打回文章
     */
    @SysLog("打回文章")
    @RequestMapping("/repulse")
    public R findAnnoByTaskId(@RequestBody Map<String, Object> data) {
        if (data == null || !data.containsKey("data")) {
            throw new RRException("打回提交的信息不能为空");
        }
        Object userStr = data.get("data");
        List<AnnoUserVO> userVoList = JSONArray.parseArray(JSON.toJSONString(userStr), AnnoUserVO.class);
        noteTaskService.repulse(userVoList, getUserId());
        return R.ok();
    }

    /**
     * 废弃/恢复文章
     */
    @SysLog("废弃/恢复文章")
    @RequestMapping("/invalid")
    public R invalidTask(Long taskId, Integer invalid) {
        if (taskId == null) {
            throw new RRException("请求文章NoteId不能为空");
        }
        if (invalid == null) {
            throw new RRException("废弃状态不能为空");
        }
        noteTaskService.invalid(taskId, invalid, getUserId());
        return R.ok();
    }

    /**
     * 重新标注审核文章
     */
    @SysLog("重新标注/审核文章")
    @RequestMapping("/rework")
    public R reworkTask(Long taskId, Long roleId) {
        if (taskId == null) {
            throw new RRException("请求文章NoteId不能为空");
        }
        if (roleId == null) {
            throw new RRException("用户角色不能为空");
        }
        noteTaskService.rework(taskId, roleId, getUserId());
        return R.ok();
    }

    /**
     * 根据NoteID查询TaskId
     */
    @RequestMapping("/findTaskByNoteId")
    public R getTaskByNoteId(@RequestBody AssignTaskDTO dto) {
        dto.setUserId(getUserId());
        TaskDetailVO task = noteTaskService.getTaskByNote(dto);
        return R.success(task);
    }

    /**
     * 提交任务
     */
    @SysLog("提交标注/审核任务")
    @RequestMapping("/submitTask")
    public synchronized R submitTask(Long roleId, Long taskId) {
        if (RoleEnum.annotator.getId() == roleId) {
            noteTaskService.submitAnnotatorTask(taskId, false);
        }
        if (RoleEnum.auditor.getId() == roleId) {
            noteTaskService.submitAuditorTask(taskId);
        }
        return R.ok();
    }

    /**
     * 取消标注当前任务
     */
    @SysLog("取消标注/审核任务")
    @RequestMapping("/cancelTask")
    public synchronized R cancelTask(Long roleId, Long taskId) {
        noteTaskService.cancelTask(roleId, taskId, getUserId());
        return R.ok();
    }

    /**
     * 分配下一个任务
     */
    @RequestMapping("/assignTask")
    public synchronized R assignTask(@RequestBody AssignTaskDTO dto) {
        dto.setUserId(getUserId());
        NoteTask noteTask = null;
        if (RoleEnum.annotator.getId() == dto.getRoleId()) {
            noteTask = noteTaskService.assignAnnotatorTask(dto);
        }
        if (RoleEnum.auditor.getId() == dto.getRoleId()) {
            noteTask = noteTaskService.assignAuditorTask(dto);
        }
        return R.success(noteTask);
    }

    /**
     * 批量审核
     */
    @SysLog("批量审核")
    @RequestMapping("/batchReview/{annotatorId}")
    public R batchReview(@RequestBody Long[] ids, @PathVariable Long annotatorId) {
        if (CollUtil.isEmpty(CollUtil.newArrayList(ids))) {
            throw new RRException("操作失败，选择不能为空");
        }
        noteTaskService.batchReview(ids, annotatorId, getUserId());
        return R.ok();
    }

    @RequestMapping("/findMetadataByNoteId")
    public R findMetadataByNoteId(String noteId) {
        List<Document.InfoDTO> infos = noteTaskService.findMetadataByNoteId(noteId);
        return R.success(infos);
    }
}
