package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.api.service.EntityService;
import org.biosino.nlp.modules.api.vo.PatternDataVo;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.dto.PreImportDTO;
import org.biosino.nlp.modules.note.dto.RelationshipDTO;
import org.biosino.nlp.modules.note.entity.Operation;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.enums.EntityEnum;
import org.biosino.nlp.modules.note.enums.OptionEnum;
import org.biosino.nlp.modules.note.service.NoteService;
import org.biosino.nlp.modules.note.service.OpertionService;
import org.biosino.nlp.modules.note.service.RelationshipService;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.note.vo.PageIdsVO;
import org.biosino.nlp.modules.note.vo.RelationVO;
import org.biosino.nlp.modules.project.service.impl.BatchServiceImpl;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-01-26 16:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RelationshipServiceImpl implements RelationshipService {

    private final RelationshipRepository relationshipRepository;

    private final EntityService entityService;

    private final SysUserService sysUserService;

    private final RelationLabelService relationLabelService;

    private final OpertionService opertionService;

    private final NoteService noteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRelationship(RelationshipDTO dto, Long userId) {
        List<Relationship.RelationItem> items = dto.getItems();
        if (CollUtil.isEmpty(items)) {
            return;
        }
        // 校验上传的关系内容是否完整
        for (Relationship.RelationItem item : items) {
            List<String> subject = item.getSubject();
            List<String> objects = item.getObjects();
            if (CollUtil.isEmpty(subject) || CollUtil.isEmpty(item.getObjects())) {
                throw new RRException("主语或宾语不能为空");
            }
            for (String s : subject) {
                if (StrUtil.isBlank(s)) {
                    throw new RRException("主语不能为空");
                }
            }
            for (String o : objects) {
                if (StrUtil.isBlank(o)) {
                    throw new RRException("宾语不能为空");
                }
            }
            if (item.getRelation() == null) {
                throw new RRException("关系不能为空");
            }
        }
        // 检验三元组是否重复
        List<String> entityIds = mergeEntityId(items).stream().filter(RelationshipServiceImpl::isEntityId).distinct().collect(Collectors.toList());
        List<Entity> entityList = entityService.findAllByIdIn(entityIds);
        if (duplicateCheck(dto, dto.getAnnotatorId(), dto.getAuditorId(), entityList)) {
            throw new RRException("该三元组已经存在，请勿重复添加");
        }
        // long count = mongoTemplate.count(Query.query(Criteria.where("id").in(entityIds)), Entity.class);
        Integer count = entityList.size();
        // 校验标注实体是否还存在
        if (count <= 0 || entityIds.size() > count) {
            throw new RRException("数据异常，请刷新页面后再操作");
        }
        relationshipRepository.saveRelationship(dto);
        addLog(dto.getNoteId(), dto.getTaskId(), dto.getId(), OptionEnum.add.getCode(), userId);
    }


    /**
     * 通过md5检查提交的关系标注是否在数据库已存在
     */
    private boolean duplicateCheck(RelationshipDTO dto, Long annotatorId, Long auditorId, List<Entity> entityList) {
        // items需要根据order进行排序，item中的subject和object需要进行排序
        String md5 = genRelationStrMd5(dto.getItems(), dto.getPatternId(), entityList);
        dto.setMd5(md5);
        return relationshipRepository.existsByTaskIdAndNoteIdAndAnnotatorIdAndAuditorIdAndSourceAndMd5AndDeleted(dto.getTaskId(), dto.getNoteId(), annotatorId, auditorId, PreSourceEnum.SELF.getId(), md5, false);
    }

    public static String genRelationStrMd5(List<Relationship.RelationItem> items, Long patternId, List<Entity> entityList) {
        Map<String, Entity> entityIdMap = entityList.stream().collect(Collectors.toMap(Entity::getId, x -> x));
        // 对 List<RelationItem> 进行排序
        items = items
                .stream()
                .sorted(Comparator.comparingInt(Relationship.RelationItem::getOrder))
                .collect(Collectors.toList());

        // 对每个 RelationItem 对象中的 subject、objects 和 annotations 进行排序
        items.forEach(relationItem -> {
            if (relationItem.getSubject() != null) {
                relationItem.setSubject(
                        relationItem.getSubject()
                                .stream()
                                .sorted()
                                .collect(Collectors.toList())
                );
            }

            if (relationItem.getObjects() != null) {
                relationItem.setObjects(
                        relationItem.getObjects()
                                .stream()
                                .sorted()
                                .collect(Collectors.toList())
                );
            }

            if (relationItem.getAnnotations() != null) {
                relationItem.setAnnotations(
                        relationItem.getAnnotations()
                                .stream()
                                .sorted(Comparator.reverseOrder())
                                .collect(Collectors.toList())
                );
            }
        });

        // 查询所有的关系实体
        List<Relationship.RelationItem> processedList = items.stream()
                .map(relationItem -> {
                    List<String> processedSubject = relationItem.getSubject().stream()
                            .map(x -> isEntityId(x) ? entityIdMap.get(x).getMd5() : x)
                            .sorted().collect(Collectors.toList());

                    List<String> processedObjects = relationItem.getObjects().stream()
                            .map(x -> isEntityId(x) ? entityIdMap.get(x).getMd5() : x)
                            .sorted().collect(Collectors.toList());

                    return new Relationship.RelationItem(relationItem.getOrder(), processedSubject, relationItem.getNegation(), relationItem.getAnnotations(), relationItem.getRelation(), processedObjects);
                })
                .collect(Collectors.toList());
        return SecureUtil.md5(JSON.toJSONString(processedList) + patternId);
    }

    @Override
    public List<Relationship> getRelationships(Long noteId) {
        return relationshipRepository.findAllByNoteIdAndDeletedOrderByCreateTime(noteId, false);
    }

    /**
     * 查询关系标注分组数据列表
     *
     * @param taskId    noteTaskId
     * @param noteId
     * @param patternId 模板编号（1~4）
     * @param source
     */
    @Override
    public List<PatternDataVo> getRelationshipsGroup(Long taskId, Long noteId, Integer patternId, Integer source, Long importLogId) {
        // List<Relationship> relationshipList;
        // if (patternId != null && patternId != -1) {
        //     relationshipList = relationshipRepository.findAllByTaskIdAndPatternIdAndSourceAndDeleted(taskId, patternId, source, false);
        // } else {
        //     relationshipList = relationshipRepository.findAllByTaskIdAndSourceAndDeleted(taskId, source, false);
        // }

        List<Relationship> relationshipList = relationshipRepository.getRelationshipsByPatternId(taskId, noteId, patternId, source, importLogId);

        if (relationshipList.isEmpty()) {
            return null;
        }
        List<String> entityIds = mergeEntityId(
                relationshipList.stream().map(Relationship::getItems)
                        .flatMap(Collection::stream).distinct().collect(Collectors.toList()))
                .stream().filter(RelationshipServiceImpl::isEntityId).collect(Collectors.toList()
                );

        // 查出所有实体
        // List<Entity> entities = mongoTemplate.find(Query.query(Criteria.where("id").in(entityIds)), Entity.class);
        List<Entity> entities = entityService.findAllByIdIn(entityIds);

        // 查出的实体按照id 分组
        Map<String, Entity> entityMap = new HashMap<>(16);
        entities.stream().filter(Objects::nonNull).forEach(x -> {
            entityMap.put(x.getId(), x);
        });

        Map<String, PatternDataVo.Item> itemMap = new HashMap<>();

        // 生成的结果集
        List<PatternDataVo> vos = new ArrayList<>();
        // 遍历分组后的情况
        for (Relationship relationship : relationshipList) {
            List<Relationship.RelationItem> relationItems = relationship.getItems();
            relationItems.sort(Comparator.comparingInt(Relationship.RelationItem::getOrder));
            Map<String, Integer> indexMap = new HashMap<>(16);
            int index = 1;
            for (Relationship.RelationItem ship : relationItems) {
                indexMap.put(ship.getOrder().toString(), index++);
            }

            PatternDataVo pvo = new PatternDataVo();
            pvo.setId(relationship.getId());

            List<PatternDataVo.Item> items = new ArrayList<>();

            try {
                for (Relationship.RelationItem relationItem : relationItems) {

                    if (BeanUtil.isNotEmpty(relationItem)) {

                        PatternDataVo.Item item = new PatternDataVo.Item();
                        item.setId(relationItem.getOrder().toString());
                        item.setOrder(relationItem.getOrder());
                        item.setNegation(relationItem.getNegation());
                        item.setAnnotations(relationItem.getAnnotations());

                        List<String> shipSubject = relationItem.getSubject();
                        if (!shipSubject.isEmpty()) {
                            PatternDataVo.Item.Node node = new PatternDataVo.Item.Node();
                            String firstSubject = shipSubject.get(0);
                            if (isEntityId(firstSubject)) {
                                node.setIsEntity(true);
                                node.setEntityIds(shipSubject);
                                List<String> collect = entityMap.get(firstSubject).getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList());
                                node.setText(CollUtil.join(collect, "/"));
                                if (shipSubject.size() > 1) {
                                    node.setNumber(shipSubject.size() - 1);
                                }
                            } else if (firstSubject.contains("#")) {
                                node.setIsEntity(true);
                                String statementId = firstSubject.substring(0, firstSubject.indexOf("#"));
                                PatternDataVo.Item it = itemMap.get(statementId);
                                if (firstSubject.endsWith("#subject")) {
                                    node.setText(it.getSubList().getText());
                                    node.setEntityIds(it.getSubList().getEntityIds());
                                    node.setNumber(it.getSubList().getNumber());
                                } else if (firstSubject.endsWith("#objects")) {
                                    node.setText(it.getObjList().getText());
                                    node.setEntityIds(it.getObjList().getEntityIds());
                                    node.setNumber(it.getObjList().getNumber());
                                }
                            } else {
                                node.setIsEntity(false);
                                node.setIndex(indexMap.get(firstSubject));
                            }
                            item.setSubList(node);
                        }

                        item.setRelation(relationItem.getRelation());
                        itemMap.put(item.getId(), item);
                        List<String> shipObjects = relationItem.getObjects();
                        if (!shipObjects.isEmpty()) {

                            PatternDataVo.Item.Node node = new PatternDataVo.Item.Node();
                            String firstObjects = shipObjects.get(0);

                            if (isEntityId(firstObjects)) {
                                node.setIsEntity(true);
                                node.setEntityIds(shipObjects);
                                List<String> collect = entityMap.get(firstObjects).getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList());
                                node.setText(CollUtil.join(collect, "/"));
                                if (shipObjects.size() > 1) {
                                    node.setNumber(shipObjects.size() - 1);
                                }
                            } else if (firstObjects.contains("#")) {
                                node.setIsEntity(true);
                                String statementId = firstObjects.substring(0, firstObjects.indexOf("#"));
                                PatternDataVo.Item it = itemMap.get(statementId);
                                if (firstObjects.endsWith("#subject")) {
                                    node.setText(it.getSubList().getText());
                                    node.setEntityIds(it.getSubList().getEntityIds());
                                    node.setNumber(it.getSubList().getNumber());
                                } else if (firstObjects.endsWith("#objects")) {
                                    node.setText(it.getObjList().getText());
                                    node.setEntityIds(it.getObjList().getEntityIds());
                                    node.setNumber(it.getObjList().getNumber());
                                }
                            } else {
                                node.setIsEntity(false);
                                node.setIndex(indexMap.get(firstObjects));
                            }
                            item.setObjList(node);
                        }
                        items.add(item);
                        itemMap.put(item.getId(), item);
                    }
                }
            } catch (Exception e) {
                log.error("创建关系列表出错，集合：{}", JSON.toJSONString(relationshipList));
                log.error("entityMap集合：{}", JSON.toJSONString(entityMap));
                e.printStackTrace();
                return null;
            }
            pvo.setItem(items);
            vos.add(pvo);
        }
        return vos;
    }


    @Override
    public List<Relationship> getRelationshipByNoteIds(List<Long> noteIds) {
        return relationshipRepository.findAllByNoteIdInAndDeletedOrderByCreateTime(noteIds, false);
    }

    @Override
    public List<Relationship> getRelationshipByProject(Long projectId) {
        return relationshipRepository.findAllByProjectIdAndDeletedOrderByCreateTime(projectId, false);
    }

    @Override
    public void removeById(String id, Long noteId, Long roleId, Long userId) {
        // List<Relationship> relationships = mongoTemplate.find(Query.query(Criteria.where("id").is(id)
        //         .and("deleted").is(false)), Relationship.class);
        Relationship relationship = getById(id);
        if (roleId.equals(RoleEnum.annotator.getId())) {
            relationship.setAnnotatorId(userId);
        }
        if (roleId.equals(RoleEnum.auditor.getId())) {
            relationship.setAuditorId(userId);
        }
        relationship.setDeleted(true);
        relationshipRepository.save(relationship);
        addLog(noteId, relationship.getTaskId(), id, OptionEnum.delete.getCode(), userId);
    }


    @Override
    public void auditStatus(String id, Long taskId, Boolean status) {
        // List<Relationship> relationships = mongoTemplate.find(Query.query(Criteria.where("id").is(id)
        //         .and("note_id").is(noteId).and("deleted").is(false)), Relationship.class);
        // if (CollUtil.isEmpty(relationships)) {
        //     return;
        // }
        // for (Relationship relationship : relationships) {
        //     relationship.setCorrect(status);
        //     mongoTemplate.save(relationship);
        // }
        Relationship relationship = getById(id);
        relationship.setCorrect(status);
        relationshipRepository.save(relationship);
    }


    @Override
    public void deleteAllByProjectIdAndImportLogId(Long projectId, Long importLogId) {
        relationshipRepository.deleteAllByProjectIdAndImportLogId(projectId, importLogId);
    }


    @Override
    public void removeById(String id) {
        relationshipRepository.deleteById(id);
    }

    @Override
    public Relationship getById(String id) {
        return relationshipRepository.findById(id).orElseThrow(() -> new RRException("relationship 未找到"));
    }

    @Override
    public Map<String, String> findEntitiesById(String groupId) {

        List<String> entityIds = getEntitiesById(groupId);

        // 查出所有实体
        // List<Entity> entities = mongoTemplate.find(Query.query(Criteria.where("id").in(entityIds)), Entity.class);
        List<Entity> entities = entityService.findAllByIdIn(entityIds);
        Map<String, String> entityMap = new HashMap<>(16);
        entities.stream().filter(Objects::nonNull).forEach(x -> {
            List<String> list = x.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent).collect(Collectors.toList());
            entityMap.put(x.getId(), CollUtil.join(list, "/"));
        });
        return entityMap;
    }


    @Override
    public List<String> getEntitiesById(String id) {
        Relationship relationship = getById(id);

        List<String> tempIdList = mergeEntityId(relationship.getItems());
        if (tempIdList == null) {
            return null;
        }
        return tempIdList.stream().filter(RelationshipServiceImpl::isEntityId)
                .distinct()
                .collect(Collectors.toList());
    }


    @Override
    public PageIdsListVO queryPage(EntitySearchDTO dto) {
        final PageIdsListVO pageIdsListVO = new PageIdsListVO();
        // 查询上下页切换的pageIds
        pageIdsListVO.setPageIdsVOList(allEntityPageIds(dto));

        // 查询分页数据
        Page<Relationship> page = relationshipRepository.queryPage(dto);
        // 获取当前页内容
        List<Relationship> content = page.getContent();
        // 找出关系中存在的所有实体id
        List<String> entityIds = content.stream()
                .flatMap(relationship -> mergeEntityId(relationship.getItems()).stream()).distinct()
                .collect(Collectors.toList());
        // 找出关系中所有的关系表标签Id
        List<Long> relationLabelIds = content.stream()
                .flatMap(relationship -> relationship.getItems().stream())
                .map(Relationship.RelationItem::getRelation).distinct()
                .collect(Collectors.toList());
        // 查询关系标签信息
        Map<Long, String> labelMap = relationLabelService.findAllByIdIn(relationLabelIds).stream()
                .collect(Collectors.toMap(RelationLabel::getId, RelationLabel::getName));
        // 查询实体标签内容
        Map<String, String> entityMap = entityService.findAllByIdIn(entityIds)
                .stream().collect(Collectors.toMap(Entity::getId,
                        x -> x.getEntityInfos().stream().map(AnnoDTO.EntityInfo::getContent)
                                .collect(Collectors.joining(" / "))));
        // 封装为结果数据
        ArrayList<RelationVO> list = new ArrayList<>();
        for (Relationship relationship : content) {
            RelationVO vo = new RelationVO();
            vo.setArticleId(relationship.getArticleId());
            vo.setBatchId(relationship.getBatchId());
            vo.setNoteId(relationship.getNoteId());
            vo.setCreateTime(relationship.getCreateTime());
            vo.setUserId(relationship.getAnnotatorId() != null ? relationship.getAnnotatorId() : relationship.getAuditorId());
            ArrayList<RelationVO.RelationItem> items = new ArrayList<>();
            for (Relationship.RelationItem item : relationship.getItems()) {
                RelationVO.RelationItem relationItem = new RelationVO.RelationItem();
                relationItem.setRelation(labelMap.get(item.getRelation()));
                relationItem.setOrder(item.getOrder());
                relationItem.setNegation(item.getNegation());
                relationItem.setAnnotations(item.getAnnotations());
                boolean isAllSubjectsValid = item.getSubject().stream().allMatch(s -> s.length() == 32);
                relationItem.setSubject(isAllSubjectsValid ?
                        item.getSubject().stream().map(entityMap::get).collect(Collectors.toList()) : item.getSubject());
                relationItem.setSubjectIsForeign(!isAllSubjectsValid);
                boolean isAllObjectsValid = item.getObjects().stream().allMatch(s -> s.length() == 32);
                relationItem.setObjects(isAllObjectsValid ?
                        item.getObjects().stream().map(entityMap::get).collect(Collectors.toList()) : item.getObjects());
                relationItem.setObjectsIsForeign(!isAllObjectsValid);
                items.add(relationItem);
            }
            vo.setItems(items);
            list.add(vo);
        }

        pageIdsListVO.setListData(new PageImpl<>(list, page.getPageable(), page.getTotalElements()));
        return pageIdsListVO;

    }

    private List<PageIdsVO> allEntityPageIds(EntitySearchDTO articleListDTO) {
        final EntitySearchDTO dto = BatchServiceImpl.initBaseDTO4Ids(articleListDTO, EntitySearchDTO.class);
        // 查询分页数据
        Page<Relationship> page = relationshipRepository.queryPage(dto);
        // 获取当前页数据
        List<Relationship> content = page.getContent();
        final Set<PageIdsVO> set = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(content)) {
            for (Relationship item : content) {
                final PageIdsVO pageIdsVO = new PageIdsVO();
                pageIdsVO.setBatchId(item.getBatchId());
                pageIdsVO.setNoteId(item.getNoteId());
                set.add(pageIdsVO);
            }
        }

        return new ArrayList<>(set);
    }

    @Override
    public Map<Long, String> getUsersByPatternId(Long patternId) {
        List<Long> userIds = relationshipRepository.getUserIdsByLabelId(patternId);
        Map<Long, String> userMap = new HashMap<>(16);
        List<SysUserEntity> users = sysUserService.findAllByIdIn(userIds);
        for (SysUserEntity user : users) {
            userMap.put(user.getUserId(), user.getUsername());
        }
        return userMap;
    }

    @Override
    public void importToOriginal(PreImportDTO preImportDTO, Long userId) {
        Relationship relation = relationshipRepository.findById(preImportDTO.getRelationId()).orElseThrow(() -> new RRException("关系未找到"));

        if (!NoteServiceImpl.isInvalidLong(preImportDTO.getMasterTaskId())) {
            if (relationshipRepository.existsByTaskIdAndMd5AndDeleted(preImportDTO.getMasterTaskId(), relation.getMd5(), false)) {
                return;
            }
            relation.setTaskId(preImportDTO.getMasterTaskId());
        } else {
            if (relationshipRepository.existsByTaskIdAndMd5AndDeleted(preImportDTO.getTaskId(), relation.getMd5(), false)) {
                return;
            }
            relation.setTaskId(preImportDTO.getTaskId());
        }
        relation.setId(IdUtil.simpleUUID());
        relation.setSource(PreSourceEnum.SELF.getId());
        relation.setImportLogId(null);
        if (preImportDTO.getRoleId().equals(RoleEnum.annotator.getId())) {
            relation.setAnnotatorId(userId);
        }
        if (preImportDTO.getRoleId().equals(RoleEnum.auditor.getId())) {
            relation.setAuditorId(userId);
        }

        relation.getItems().forEach(x -> {
            boolean allSubjectIsEntity = x.getSubject().stream().allMatch(y -> y.length() == 32);
            if (allSubjectIsEntity) {
                List<String> list = x.getSubject().stream().map(y -> {
                    preImportDTO.setEntityId(y);
                    return noteService.selectedImportToOriginal(preImportDTO, userId);
                }).collect(Collectors.toList());
                x.setSubject(list);
            }
            boolean allObjectIsEntity = x.getObjects().stream().allMatch(y -> y.length() == 32);
            if (allObjectIsEntity) {
                List<String> list = x.getObjects().stream().map(y -> {
                    preImportDTO.setEntityId(y);
                    return noteService.selectedImportToOriginal(preImportDTO, userId);
                }).collect(Collectors.toList());
                x.setObjects(list);
            }
        });

        relationshipRepository.save(relation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importAllToOriginal(PreImportDTO dto, Long userId) {
        final Long taskId = dto.getTaskId();
        if (NoteServiceImpl.isInvalidLong(taskId)) {
            throw new RRException("taskId 不能为空");
        }
        List<Relationship> list;
        if (dto.getSource().equals(PreSourceEnum.SELF.getId())) {
            list = relationshipRepository.getRelationshipsByPatternId(taskId, dto.getNoteId(), -1, dto.getSource(), dto.getImportLogId());
        } else {
            list = relationshipRepository.getRelationshipsByPatternId(null, dto.getNoteId(), -1, dto.getSource(), dto.getImportLogId());
        }
        // 循环调用单次导入的接口
        for (Relationship relationship : list) {
            dto.setRelationId(relationship.getId());
            importToOriginal(dto, userId);
        }
    }

    private List<String> mergeEntityId(List<Relationship.RelationItem> relationItems) {
        if (CollUtil.isEmpty(relationItems)) {
            return CollUtil.newArrayList();
        }
        // 查出此关系标签中所有的实体id放在内存中来做
        List<String> subject = relationItems.stream()
                .flatMap(it -> it.getSubject().stream()).collect(Collectors.toList());
        List<String> objects = relationItems.stream()
                .flatMap(it -> it.getObjects().stream()).collect(Collectors.toList());

        List<String> tempIdList = new ArrayList<>();
        tempIdList.addAll(subject);
        tempIdList.addAll(objects);
        return tempIdList.stream().distinct().collect(Collectors.toList());
    }

    private void addLog(Long noteId, Long taskId, String id, Integer type, Long userId) {
        ThreadUtil.execAsync(() -> {
            Operation operation = new Operation();
            operation.setNoteId(noteId);
            operation.setTaskId(taskId);
            operation.setOperation(EntityEnum.relationship.getCode());
            operation.setType(type);
            operation.setCreateTime(new Date());
            operation.setMsg("关系Id: " + id);
            operation.setUserId(userId);
            opertionService.save(operation);
        });
    }

    /**
     * 校验字符串是不是实体id
     *
     * @param id 传入的id字符串
     * @return true or false
     */
    public static boolean isEntityId(String id) {
        if (id != null && id.length() == 32) {
            return true;
        }
        return false;
    }
}
