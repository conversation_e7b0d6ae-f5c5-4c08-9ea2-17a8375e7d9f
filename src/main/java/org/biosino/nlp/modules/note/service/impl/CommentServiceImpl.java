package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.CommentRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Comment;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.enums.CommentEnum;
import org.biosino.nlp.modules.note.service.CommentService;
import org.biosino.nlp.modules.sys.dao.SysUserDao;
import org.biosino.nlp.modules.sys.entity.SysUserEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评论服务
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Service
@RequiredArgsConstructor
public class CommentServiceImpl implements CommentService {

    private final CommentRepository commentRepository;
    private final AttributesRepository attributesRepository;
    private final EntityRepository entityRepository;
    private final SysUserDao sysUserDao;
    private final NoteTaskDao noteTaskDao;

    @Override
    public Comment createQuestion(Comment comment) {
        // 设置创建时间和初始状态
        comment.setCreateTime(new Date());
        comment.setStatus(CommentEnum.wait.getCode());

        NoteTask noteTask = noteTaskDao.selectById(comment.getTaskId());
        comment.setAnnotatorId(noteTask.getAnnotator());

        Comment result = commentRepository.save(comment);

        // 更新任务的未解决评论数量
        updateTaskQueriesCount(noteTask.getTaskId());

        return result;
    }

    @Override
    public Comment replyQuestion(String commentId, String replyText, Long replyId) {
        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RRException("提问不存在"));

        comment.setReplyText(replyText);
        comment.setReplyId(replyId);
        comment.setReplyTime(new Date());
        comment.setStatus(CommentEnum.resolved.getCode());

        Comment result = commentRepository.save(comment);

        // 更新任务的未解决评论数量
        updateTaskQueriesCount(comment.getTaskId());

        return result;
    }

    @Override
    public List<Comment> getQuestionsByTaskId(Long taskId) {
        return commentRepository.findByTaskIdOrderByCreateTimeDesc(taskId);
    }

    @Override
    public List<Comment> getQuestionsByNoteId(Long noteId) {
        return commentRepository.findByNoteIdOrderByCreateTimeDesc(noteId);
    }

    @Override
    public Comment updateQuestionStatus(String commentId, Integer status) {
        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RRException("提问不存在"));

        comment.setStatus(status);

        Comment result = commentRepository.save(comment);

        // 更新任务的未解决评论数量
        updateTaskQueriesCount(comment.getTaskId());

        return result;
    }

    @Override
    public List<String> findAttributeRecordIdsByEntityId(String entityId) {
        if (entityId == null || entityId.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 根据实体ID查找所有相关的m_attributes记录
        List<Attributes> attributes = attributesRepository.findByEntityIdOrAttrId(entityId, entityId);

        return attributes.stream()
                .map(Attributes::getId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findEntityIdsByAttributeRecords(String entityId, List<String> attributeRecordIds) {
        List<String> entityIds = CollUtil.newArrayList(entityId);

        if (attributeRecordIds == null || attributeRecordIds.isEmpty()) {
            return entityIds;
        }

        for (String recordId : attributeRecordIds) {
            if (recordId != null && !recordId.trim().isEmpty()) {
                attributesRepository.findById(recordId).ifPresent(attr -> {
                    if (attr.getAttributeId() != null && !entityIds.contains(attr.getAttributeId())) {
                        entityIds.add(attr.getAttributeId());
                    }
                    // 同时添加原始实体ID
                    if (attr.getEntityId() != null && !entityIds.contains(attr.getEntityId())) {
                        entityIds.add(attr.getEntityId());
                    }
                });
            }
        }

        // 2. 通过attribute_id到m_entity表中查找id为这些attribute_id的记录
        for (String attributeId : entityIds) {
            if (attributeId != null && !attributeId.trim().isEmpty()) {
                // 查找m_entity表中id为attributeId的记录
                entityRepository.findById(attributeId).ifPresent(entity -> {
                    if (!entityIds.contains(entity.getId())) {
                        entityIds.add(entity.getId());
                    }
                });
            }
        }

        return entityIds;
    }

    @Override
    public List<Comment> processQuestionsWithDetails(List<Comment> questions) {
        for (Comment question : questions) {
            // 获取实体名称
            String entityNames = getEntityNames(question.getEntityId());
            question.setEntityNames(entityNames);

            // 获取提问人姓名
            String questionerName = getUserName(question.getQuestionerId());
            question.setQuestionerName(questionerName);

            // 获取回复人姓名
            String replierName = getUserName(question.getReplyId());
            question.setReplierName(replierName);
        }

        return questions;
    }

    @Override
    public long countUnresolvedQuestionsByTaskId(Long taskId) {
        if (taskId == null) {
            return 0;
        }
        return commentRepository.countByTaskIdAndStatus(taskId, CommentEnum.wait.getCode());
    }

    @Override
    public void deleteCommentsByNoteId(Long noteId) {
        if (noteId == null) {
            return;
        }
        commentRepository.deleteByNoteId(noteId);
    }

    @Override
    public void deleteCommentsByNoteIds(List<Long> noteIds) {
        if (CollUtil.isEmpty(noteIds)) {
            return;
        }
        commentRepository.deleteByNoteIdIn(noteIds);
    }

    @Override
    public void deleteCommentsByBatchId(Long batchId) {
        if (batchId == null) {
            return;
        }
        commentRepository.deleteByBatchId(batchId);
    }

    @Override
    public void deleteCommentsByTaskId(Long taskId) {
        if (taskId == null) {
            return;
        }
        commentRepository.deleteByTaskId(taskId);
    }

    /**
     * 更新任务的未解决评论数量
     *
     * @param taskId 任务ID
     */
    private void updateTaskQueriesCount(Long taskId) {
        if (taskId == null) {
            return;
        }

        NoteTask noteTask = noteTaskDao.selectById(taskId);

        if (noteTask != null) {
            // 查询当前任务下所有状态为"待解决"的评论数量
            long unresolvedCount = countUnresolvedQuestionsByTaskId(taskId);
            noteTask.setQueries((int) unresolvedCount);
            noteTask.setUpdateTime(new Date());
            noteTaskDao.updateById(noteTask);
        }
    }

    /**
     * 根据实体ID获取实体名称
     */
    private String getEntityNames(String entityId) {
        if (entityId == null || entityId.trim().isEmpty()) {
            return "未知实体";
        }

        try {
            Optional<Entity> entityOpt = entityRepository.findById(entityId);
            if (entityOpt.isPresent()) {
                Entity entity = entityOpt.get();

                // 构建显示名称
                StringBuilder nameBuilder = new StringBuilder();

                // 添加实体/属性标识
                if (entity.getIsAttr() != null && entity.getIsAttr() == 1) {
                    nameBuilder.append("[属性] ");
                } else {
                    nameBuilder.append("[实体] ");
                }

                // 获取内容并用"/"拼接
                if (entity.getEntityInfos() != null && !entity.getEntityInfos().isEmpty()) {
                    List<String> contents = entity.getEntityInfos().stream()
                            .map(AnnoDTO.EntityInfo::getContent)
                            .filter(content -> content != null && !content.trim().isEmpty())
                            .collect(Collectors.toList());

                    if (!contents.isEmpty()) {
                        nameBuilder.append(String.join(" / ", contents));
                    } else {
                        nameBuilder.append("无内容");
                    }
                } else {
                    nameBuilder.append("无内容");
                }

                return nameBuilder.toString();
            }
        } catch (Exception e) {
            // 记录错误但不抛出异常
            System.err.println("获取实体名称失败: " + e.getMessage());
        }

        return "获取失败";
    }

    /**
     * 根据用户ID获取用户名
     */
    private String getUserName(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            SysUserEntity user = sysUserDao.selectById(userId);
            return user != null ? user.getUsername() : "未知用户";
        } catch (Exception e) {
            // 记录错误但不抛出异常
            System.err.println("获取用户名失败: " + e.getMessage());
            return "获取失败";
        }
    }
}
