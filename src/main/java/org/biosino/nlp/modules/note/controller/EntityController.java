package org.biosino.nlp.modules.note.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.api.service.EntityService;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dto.EntitySearchDTO;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @date 2023/5/10
 */
@Api("实体标注列表接口")
@Slf4j
@RestController
@RequestMapping("/entity")
public class EntityController {
    @Autowired
    private EntityService entityService;

    @Autowired
    private EntityLabelService entityLabelService;


    @RequestMapping("/list")
    public R list(EntitySearchDTO dto) {
        dto = BeanUtil.trimStrFields(dto);
        // 查询分页的数据
        final PageIdsListVO vo = entityService.queryPage(dto);

        return R.ok().put("page", vo);
    }

    @RequestMapping("/userAndLabelInfo")
    public R userAndLabelInfo(EntitySearchDTO dto) {
        final Long labelId = dto.getLabelId();
        // 查询label下的所有user信息
        Map<Long, String> userMap = entityService.getUsersByLabelId(labelId);
        // 查询标签的信息
        EntityLabel label = entityLabelService.getById(labelId);
        return R.ok().put("userList", userMap).put("label", label);
    }

}
