package org.biosino.nlp.modules.note.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 标注状态
 *
 * <AUTHOR>
 */
public enum EventEnum {
    //
    unmarked(0, "未标注"),
    pullAnnotation(1, "拉取标注"),
    completeAnnotation(2, "完成标注"),
    pullReview(3, "拉取审核"),
    completeReviewed(4, "完成审核"),
    repulse(5, "被打回"),
    corrected(6, "已修正");

    private Integer code;
    private String desc;

    EventEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<>(8);
        for (EventEnum statusEnum : EventEnum.values()) {
            map.put(statusEnum.getCode(), statusEnum.getDesc());
        }
        return map;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
