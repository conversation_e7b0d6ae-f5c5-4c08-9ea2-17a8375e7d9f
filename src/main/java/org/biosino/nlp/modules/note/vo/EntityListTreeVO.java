package org.biosino.nlp.modules.note.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 实体清单树vo
 */
@Data
public class EntityListTreeVO implements Serializable {

    private static final long serialVersionUID = 7072421089571091396L;

    /**
     * 标注id
     */
    private String id;
    /**
     * 节点名称冒号之前的部分
     */
    private String label;
    /**
     * 节点名称冒号之后的部分
     */
    private List<String> val;
    /**
     * 实体标注对应的标签id
     */
    private Long labelId;

    private List<EntityListTreeVO> children;

    public EntityListTreeVO(String id, String label, List<String> val) {
        this(id, label, val, null);
    }

    public EntityListTreeVO(String id, String label, List<String> val, Long labelId) {
        this(id, label, val, labelId, null);
    }

    public EntityListTreeVO(String id, String label, List<String> val, Long labelId, List<EntityListTreeVO> children) {
        this.id = id;
        this.label = label;
        this.val = val;
        this.labelId = labelId;
        this.children = children;
    }
}
