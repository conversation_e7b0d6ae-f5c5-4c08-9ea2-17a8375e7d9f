package org.biosino.nlp.modules.note.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.note.dao.OperationDao;
import org.biosino.nlp.modules.note.dto.OperationDTO;
import org.biosino.nlp.modules.note.entity.Operation;
import org.biosino.nlp.modules.note.enums.EntityEnum;
import org.biosino.nlp.modules.note.service.OpertionService;
import org.biosino.nlp.modules.note.vo.OpertionVO;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * <AUTHOR>
 */
@Service("operationService")
public class OperationServiceImpl extends ServiceImpl<OperationDao, Operation> implements OpertionService {

    @Autowired
    private SysUserService sysUserService;

    @Override
    public PageUtils queryPage(OperationDTO dto) {
        IPage<Operation> page = this.page(new Query<Operation>().getPage(dto),
                Wrappers.<Operation>lambdaQuery()
                        .eq(dto.getNoteId() != null, Operation::getTaskId, dto.getNoteId())
                        .eq(dto.getAccount() != null, Operation::getUserId, dto.getAccount())
                        .eq(dto.getOperation() != null, Operation::getOperation, dto.getOperation())
                        .eq(dto.getType() != null, Operation::getType, dto.getType())
                        .like(StrUtil.isNotBlank(dto.getMsg()), Operation::getMsg, dto.getMsg())
                        .orderByDesc(Operation::getCreateTime));

        // 将结果转换为前端VO
        List<Operation> records = page.getRecords();
        List<OpertionVO> voList = new ArrayList<>();
        for (Operation record : records) {
            OpertionVO vo = new OpertionVO();
            vo.setNoteId(record.getTaskId());
            vo.setMsg(record.getMsg());
            vo.setOperation(EntityEnum.getDesc(record.getOperation()));
            vo.setType(record.getType());
            vo.setCreateTime(record.getCreateTime());
            vo.setAccount(sysUserService.findNameById(record.getUserId()));
            voList.add(vo);
        }
        return new PageUtils(page, voList);
    }

    @Override
    public Map<Long, String> getAccount(OperationDTO dto) {
        LambdaQueryWrapper<Operation> wrapper = Wrappers.<Operation>lambdaQuery()
                .select(Operation::getUserId)
                .eq(dto.getNoteId() != null, Operation::getTaskId, dto.getNoteId());

        Map<Long, String> accountMap = new HashMap<>(16);
        List<Operation> list = this.list(wrapper);
        for (Operation operation : list) {
            accountMap.put(operation.getUserId(), sysUserService.findNameById(operation.getUserId()));
        }
        return accountMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEntityLog(Long noteId, Long taskId, Integer type, String msg, Long userId) {
        Operation operation = new Operation();
        operation.setNoteId(noteId);
        operation.setTaskId(taskId);
        operation.setOperation(EntityEnum.entity.getCode());
        operation.setType(type);
        operation.setCreateTime(new Date());
        operation.setMsg(msg);
        operation.setUserId(userId);
        this.save(operation);
    }
}
