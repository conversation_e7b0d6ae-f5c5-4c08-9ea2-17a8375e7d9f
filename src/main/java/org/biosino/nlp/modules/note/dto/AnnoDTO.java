package org.biosino.nlp.modules.note.dto;

import com.alibaba.fastjson.serializer.PropertyFilter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.nlp.modules.note.validata.AddEntity;
import org.biosino.nlp.modules.note.validata.DeleteEntity;
import org.biosino.nlp.modules.note.validata.UpdateEntity;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnnoDTO implements Serializable {
    private static final long serialVersionUID = 1232L;

    @NotBlank(groups = {UpdateEntity.class, DeleteEntity.class})
    private String entityId;

    /**
     * 删除实体标注时，是否同时删除对应属性标注
     */
    @NotNull(groups = {DeleteEntity.class})
    private Boolean deleteAttrAsWell;

    @NotNull(groups = {AddEntity.class})
    private Long noteId;
    @NotNull(groups = {AddEntity.class})
    private Long taskId;
    @NotNull(groups = {AddEntity.class})
    private Long projectId;

    @NotNull(groups = {AddEntity.class})
    private Long batchId;
    @NotBlank(groups = {AddEntity.class})
    private String articleId;

    //    @NotNull(groups = {AddEntity.class})
    private Long labelId;

//    private String source;

    @Size(max = 100, groups = {AddEntity.class, UpdateEntity.class}, message = "实体批注信息最大长度不能超过100")
    private String annotate;

    @NotNull(groups = {DeleteEntity.class})
    private Boolean multiple = false;

    private Boolean questionFlag = false;

//    private Long secondLabel;

    private String conceptId;
    private String conceptText;
    private Integer conceptType;

    @NotNull(groups = {AddEntity.class, UpdateEntity.class})
    private Long roleId;

    @NotEmpty(groups = {AddEntity.class})
    @Size(min = 1, max = 200, groups = {AddEntity.class})
    @Valid
    private List<EntityInfo> entityInfos;

    @NotNull(groups = {AddEntity.class})
    private Integer isAttr = 0;

    /**
     * 删除属性时，是否强制删除被使用的属性
     */
    private Boolean forceDelAttr = false;


    /**
     * fastjson过滤器，忽略指定字段，用于生成md5值
     */
    @Transient
    public static final PropertyFilter ENTITY_JSON_FILTER = (obj, propName, val) -> {
        // || propName.equals("content")
        if (propName.equals("textId") || propName.equals("start") || propName.equals("end")) {
            return true;
        } else {
            return false;
        }
    };

    @Data
    // 用于distinct去重
    @EqualsAndHashCode
    public static class EntityInfo implements Serializable {
        @Transient
        private static final long serialVersionUID = 7851595492675222914L;

        /**
         * 单个标注的唯一性id
         */
//        @JSONField(serialize = false)
        @Indexed(unique = true, name = "anno_entity_id")
        private String uniqueid;
        @Field("text_id")
        @NotBlank(groups = {AddEntity.class})
        private String textId;
        @NotNull(groups = {AddEntity.class})
        private Integer start;
        @NotNull(groups = {AddEntity.class})
        private Integer end;
        @NotBlank(groups = {AddEntity.class})
        private String content;

        /**
         * 实体标注id
         */
//        @JSONField(serialize = false)
        @Transient
        private String annoid;
        /**
         * 标签id
         */
//        @JSONField(serialize = false)
        @Transient
        private Long labelId;
        /**
         * 标注批注信息
         */
//        @JSONField(serialize = false)
        @Transient
        private String annotate;
        @Transient
        private Integer isAttr;

    }
}
