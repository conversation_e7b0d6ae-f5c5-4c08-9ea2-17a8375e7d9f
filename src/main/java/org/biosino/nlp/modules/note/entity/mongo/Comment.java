package org.biosino.nlp.modules.note.entity.mongo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 实体讨论区 各用户间的评论
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "m_comment")
public class Comment extends BaseMongo implements Serializable {

    @Transient
    private static final long serialVersionUID = -218669776366242043L;

    @Indexed
    @Field(name = "project_id")
    private Long projectId;

    @Indexed
    @Field(name = "batch_id")
    private Long batchId;

    @Indexed
    @Field(name = "note_id")
    private Long noteId;

    @Indexed
    @Field(name = "task_id")
    private Long taskId;

    @Field(name = "annotator_id")
    private Long annotatorId;

    /**
     * 状态 (1 待解决、 2 已解决、3 已关闭)
     */
    @Field(name = "status")
    private Integer status;

    /**
     * 提问人ID （可为任意角色ID）
     */
    @Field(name = "questioner_id")
    private Long questionerId;

    /**
     * 提问内容
     */
    @Field(name = "question_text")
    private String questionText;

    /**
     * 回复人ID（审核员id）
     */
    @Field(name = "reply_id")
    private Long replyId;

    /**
     * 回复内容
     */
    @Field(name = "reply_text")
    private String replyText;

    /**
     * 用户所选择的违背规则内容
     */
    @Field(name = "violate_rule")
    private String violateRule;

    /**
     * 被提问的实体ID
     */
    @Field(name = "entity_id")
    private String entityId;

    /**
     * 提交时，相关的所有实体ID
     */
    @Field(name = "entity_ids")
    private List<String> entityIds;

    /**
     * 提交时，相关的所有实体与属性关联关系的ID
     */
    @Field(name = "attribute_ids")
    private List<String> attributeIds;

    /**
     * 记录创建时间，也是提问时间
     */
    @Field(name = "create_time")
    private Date createTime;

    /**
     * 审核员解决时间
     */
    @Field(name = "reply_time")
    private Date replyTime;

    // ========== 以下字段不映射到数据库，仅用于前端展示 ==========

    /**
     * 实体名称（用于前端显示，不存储到数据库）
     */
    @Transient
    private String entityNames;

    /**
     * 提问人姓名（用于前端显示，不存储到数据库）
     */
    @Transient
    private String questionerName;

    /**
     * 回复人姓名（用于前端显示，不存储到数据库）
     */
    @Transient
    private String replierName;

}
