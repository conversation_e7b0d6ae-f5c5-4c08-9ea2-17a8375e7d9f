package org.biosino.nlp.modules.note.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.nlp.modules.note.dto.TaskDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.vo.ProjectBatchVO;
import org.biosino.nlp.modules.note.vo.TaskListVO;
import org.biosino.nlp.modules.note.vo.UserVO;
import org.biosino.nlp.modules.project.vo.AnnoDetailsVO;

import java.util.List;

/**
 * 标注任务日志
 */
@Mapper
public interface NoteTaskDao extends BaseMapper<NoteTask> {

    List<ProjectBatchVO> queryProjectList(Long userId, Long roleId);

    IPage<TaskListVO> queryAllTaskList(IPage<?> page, TaskDTO taskDTO);

    IPage<TaskListVO> queryAnnotatorTaskList(IPage<?> page, TaskDTO taskDTO);

    Integer countUnmarkedTask(Long projectId, Long userId);

    Integer countUnreviewTask(Long projectId, Long userId);

    IPage<TaskListVO> queryAuditorTaskList(IPage<?> page, TaskDTO taskDTO);

    IPage<TaskListVO> queryRepulseTaskList(IPage<?> page, TaskDTO taskDTO);

    List<UserVO> queryUserList(Long batchId, Long roleId);

    List<AnnoDetailsVO> queryAnnoDetail(Long projectId, Long roleId, Long userId);

    NoteTask assignAuditorTask(Long batchId, Long annotatorId, Long auditorId);

    /**
     * 真删除
     */
    void reallyDeleteByTaskId(Long taskId);
    void reallyDeleteByNoteId(Long noteId);

    @Select("SELECT AVG(t.correct_rate) FROM t_note_task t  WHERE t.batch_id = ${batchId} AND t.deleted = 0")
    Double selectBatchAvgCorrectRate(@Param("batchId") Long batchId);

    @Select("SELECT  ROUND(COUNT(CASE WHEN t.repulse_msg IS NOT NULL THEN 1 END) / " +
            "COUNT(CASE WHEN t.repulse_msg IS NULL THEN 1 END), 2) AS Ratio " +
            "FROM t_note_task t WHERE t.batch_id = ${batchId} AND t.deleted = 0")
    Double selectBatchAvgRepulseRate(@Param("batchId") Long batchId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.annotator = ${userId} " +
            " AND t.anno_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) <= 3600;")
    Integer selectBatchAnnoAvgTimeByUser(@Param("batchId") Long batchId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.auditor = ${userId} " +
            " AND t.audit_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) <= 3600;")
    Integer selectBatchAuditorAvgTimeByUser(@Param("batchId") Long batchId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.project_id = ${projectId}" +
            " AND t.annotator = ${userId} " +
            " AND t.anno_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) <= 3600;")
    Integer selectAnnoAvgTime(@Param("projectId") Long projectId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.annotator = ${userId} " +
            " AND t.anno_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) <= 3600;")
    Integer selectAnnoAvgTimeByBatch(@Param("batchId") Long batchId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.project_id = ${projectId}" +
            " AND t.auditor = ${userId} " +
            " AND t.audit_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) <= 3600;")
    Integer selectAuditorAvgTime(@Param("projectId") Long projectId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.auditor = ${userId} " +
            " AND t.audit_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) <= 3600;")
    Integer selectAuditorAvgTimeByBatch(@Param("batchId") Long batchId, @Param("userId") Long userId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.anno_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.anno_start_time, t.anno_end_time ) <= 3600;")
    Integer selectBatchAnnoAvgTime(@Param("batchId") Long batchId);

    @Select("SELECT AVG(" +
            " TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time )) AS avgDiffTime " +
            " FROM t_note_task t  WHERE" +
            " t.batch_id = ${batchId}" +
            " AND t.audit_end_time is not null " +
            " AND t.deleted = 0 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) >= 10 " +
            " AND TIMESTAMPDIFF( SECOND, t.audit_start_time, t.audit_end_time ) <= 3600;")
    Integer selectBatchAuditorAvgTime(@Param("batchId") Long batchId);

    @Select({
            "<script>",
            "SELECT note_id FROM t_note_task",
            "WHERE project_id = #{projectId}",
            "AND annotator IN",
            "<foreach item='item' index='index' collection='userId' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "AND step IN (2, 3, 4)",
            "GROUP BY note_id",
            "HAVING COUNT(*) = #{markRounds}",
            "</script>"
    })
    List<Long> selectBatchAnnotatorNoteByProject(@Param("projectId") Long projectId, @Param("userId") List<Long> userId, @Param("markRounds") Integer markRounds);


    @Select({
            "<script>",
            "SELECT note_id FROM t_note_task",
            "WHERE batch_id = #{batchId}",
            "AND annotator IN",
            "<foreach item='item' index='index' collection='userId' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "AND step IN (2, 3, 4)",
            "GROUP BY note_id",
            "HAVING COUNT(*) = #{markRounds}",
            "</script>"
    })
    List<Long> selectBatchAnnotatorNoteByBatch(@Param("batchId") Long batchId, @Param("userId") List<Long> userId, @Param("markRounds") Integer markRounds);


    @Select({
            "<script>",
            "SELECT note_id FROM t_note_task",
            "WHERE project_id = #{projectId}",
            "AND step IN (2, 3, 4)",
            "GROUP BY note_id",
            "</script>"
    })
    List<Long> selectBatchAnnotatorNote2(@Param("projectId") Long projectId);
}
