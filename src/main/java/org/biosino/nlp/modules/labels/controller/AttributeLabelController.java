package org.biosino.nlp.modules.labels.controller;

import cn.hutool.core.bean.BeanUtil;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.dto.AttributeDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/label/attribute")
public class AttributeLabelController {
    @Autowired
    private AttributeLabelService attributeLabelService;

    @SysLog("保存属性")
    @RequestMapping("/save")
    public R save(@RequestBody AttributeLabel attributeLabel) {
        attributeLabelService.add(BeanUtil.trimStrFields(attributeLabel));
        return R.ok();
    }

    @RequestMapping("/findAllByEntityLabelId")
    public R findAllByEntityLabelId(Long eneityLabelId) {
        List<AttributeLabel> attributeLabels = attributeLabelService.findAllByEntityLabelId(eneityLabelId);
        return R.ok().put("data", attributeLabels);
    }

    @SysLog("删除属性")
    @RequestMapping("/delete")
    public void deleteOne(@RequestBody Long id) {
        attributeLabelService.removeLabelById(id);
    }

    /**
     * name 字段校验（基于整个项目）
     */
    @RequestMapping("/existByNameInProject")
    public R existByNameInProject(AttributeDTO dto) {
        boolean result = attributeLabelService.existByNameInProject(dto);
        return R.success(result);
    }

    /**
     * field 字段校验（基于整个项目）
     */
    @RequestMapping("/existByFieldInProject")
    public R existByFieldInProject(AttributeDTO dto) {
        boolean result = attributeLabelService.existByFieldInProject(dto);
        return R.success(result);
    }

}
