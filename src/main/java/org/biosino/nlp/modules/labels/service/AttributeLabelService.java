package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.dto.AttributeDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;

import java.util.Collection;
import java.util.List;
import java.util.Map;


public interface AttributeLabelService extends IService<AttributeLabel> {
    void add(AttributeLabel attributeLabels);

    void removeLabelById(Long id);

    List<AttributeLabel> findAllByEntityLabelId(Long entityLabelId);

    List<AttributeLabel> findAllEnableByEntityLabelIdIn(Collection<Long> entityLabelIds);

    Map<Long, List<AttributeLabel>> findAllEnableMapByEntityLabelIdIn(Collection<Long> entityLabelIds);

    boolean existByNameInProject(AttributeDTO dto);

    boolean existByFieldInProject(AttributeDTO dto);

    List<AttributeLabel> findByIds(List<Long> ids);

    AttributeLabel findById(Long id);

    List<LabelUseVo> getLabelUseInfo(List<String> queryCode);
}
