package org.biosino.nlp.modules.labels.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-21 14:44:13
 */
@Mapper
public interface AttributeLabelDao extends BaseMapper<AttributeLabel> {

    List<LabelUseVo> getLabelUseInfo(List<String> queryCode);
}
