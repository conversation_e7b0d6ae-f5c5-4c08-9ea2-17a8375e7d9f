package org.biosino.nlp.modules.labels.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.FileUtils;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.labels.dao.UmlsConceptDao;
import org.biosino.nlp.modules.labels.dto.UmlsConceptDTO;
import org.biosino.nlp.modules.labels.dto.UmlsConceptExcelDTO;
import org.biosino.nlp.modules.labels.dto.UmlsExcelDTO;
import org.biosino.nlp.modules.labels.entity.UmlsConcept;
import org.biosino.nlp.modules.labels.service.UmlsConceptService;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


@Service("umlsConceptService")
public class UmlsConceptServiceImpl extends ServiceImpl<UmlsConceptDao, UmlsConcept> implements UmlsConceptService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private EntityRepository entityCustomRepository;

    @Override
    public PageUtils queryPage(UmlsConceptDTO dto) {
        LambdaQueryWrapper<UmlsConcept> queryWrapper = Wrappers.<UmlsConcept>lambdaQuery();
        queryWrapper.like(StrUtil.isNotBlank(dto.getConceptId()), UmlsConcept::getConceptId, dto.getConceptId())
                .like(StrUtil.isNotBlank(dto.getConceptName()), UmlsConcept::getConceptName, dto.getConceptName())
                .like(StrUtil.isNotBlank(dto.getPreferredName()), UmlsConcept::getPreferredName, dto.getPreferredName())
                .eq(dto.getStatus() != null, UmlsConcept::getStatus, dto.getStatus())
                .eq(dto.getCreater() != null, UmlsConcept::getCreater, dto.getCreater())
                .eq(dto.getProjectId() != null, UmlsConcept::getProjectId, dto.getProjectId());
        IPage<UmlsConcept> page = this.page(new Query<UmlsConcept>().getPage(dto), queryWrapper.orderByDesc(UmlsConcept::getCreateTime));
        return new PageUtils(page);
    }

    @Override
    public void saveUmlsConcept(UmlsConcept umlsConcept, Long userId) {
        umlsConcept.setCreateTime(new Date());
        umlsConcept.setCreater(userId);
        this.save(BeanUtil.trimStrFields(umlsConcept));
    }

    @Override
    public Map<Long, String> getUsers(UmlsConceptDTO dto) {
        LambdaQueryWrapper<UmlsConcept> queryWrapper = Wrappers.<UmlsConcept>lambdaQuery().eq(dto.getProjectId() != null, UmlsConcept::getProjectId, dto.getProjectId());
        List<UmlsConcept> list = this.list(queryWrapper);
        HashMap<Long, String> userMap = new HashMap<>(16);
        list.stream().distinct().filter(Objects::nonNull).forEach(x -> {
            userMap.put(x.getCreater(), sysUserService.findNameById(x.getCreater()));
        });
        return userMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UmlsConcept> changeStatus(Integer status, List<Long> ids) {
        List<UmlsConcept> result = new ArrayList<>();
        List<UmlsConcept> umlsConcepts = this.listByIds(ids);
        if (status.equals(StatusEnum.disable.getValue())) {
            if (umlsConcepts.size() > 0) {
                for (UmlsConcept umlsConcept : umlsConcepts) {
                    if (entityCustomRepository.findEntityByConceptIdAndProjectId(umlsConcept.getConceptId(), umlsConcept.getProjectId())) {
                        result.add(umlsConcept);
                    } else {
                        this.update(Wrappers.<UmlsConcept>lambdaUpdate().eq(UmlsConcept::getId, umlsConcept.getId()).set(UmlsConcept::getStatus, status));
                    }
                }
            }
        } else {
            ids.forEach(x -> {
                this.update(Wrappers.<UmlsConcept>lambdaUpdate()
                        .eq(UmlsConcept::getId, x).set(UmlsConcept::getStatus, status));
            });
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UmlsConcept> removeUmlsConceptsByIds(List<Long> ids) {
        List<UmlsConcept> result = new ArrayList<>();
        List<UmlsConcept> umlsConcepts = this.listByIds(ids);
        if (umlsConcepts.size() > 0) {
            for (UmlsConcept umlsConcept : umlsConcepts) {
                if (entityCustomRepository.findEntityByConceptIdAndProjectId(umlsConcept.getConceptId(), umlsConcept.getProjectId())) {
                    result.add(umlsConcept);
                } else {
                    this.removeById(umlsConcept.getId());
                }
            }
        }
        return result;
    }

    @Override
    public Boolean validateConceptId(UmlsConceptDTO dto) {
        int count = 0;
        if (ObjectUtil.isNotNull(dto)) {
            count = this.count(Wrappers.<UmlsConcept>lambdaQuery().eq(StrUtil.isNotBlank(dto.getConceptId()), UmlsConcept::getConceptId, dto.getConceptId())
                    .eq(dto.getProjectId() != null, UmlsConcept::getProjectId, dto.getProjectId()));
        }
        return count > 0;
    }

    @Override
    public UmlsConcept findByConceptId(String conceptId, Long projectId) {
        return this.getOne(Wrappers.<UmlsConcept>lambdaQuery().eq(UmlsConcept::getConceptId, conceptId)
                .eq(UmlsConcept::getProjectId, projectId).eq(UmlsConcept::getStatus, StatusEnum.enable.getValue()));
    }

    @Override
    public List<UmlsConcept> findByConceptName(final String conceptName, final Long projectId) {
        if (StrUtil.isBlank(conceptName) || projectId == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<UmlsConcept>lambdaQuery().like(UmlsConcept::getConceptName, conceptName)
                .eq(UmlsConcept::getProjectId, projectId).eq(UmlsConcept::getStatus, StatusEnum.enable.getValue()));
    }

    /**
     * 用户自己上传的关系标签
     */
    @Override
    public void uploadFile(Long projectId, MultipartFile multipartFile, Long userId) {
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        if (!"xlsx".equals(suffix) && !"xls".equals(suffix)) {
            throw new RRException("文件格式错误，请上传 Excel 文件");
        }
        File file = FileUtils.transferToFile(multipartFile);
        saveFileData(projectId, file, userId);
    }

    @Override
    public List<UmlsConceptExcelDTO> exportByProjectId(Long projectId) {
        if (projectId == null) {
            throw new RRException("参数错误，projectId不能为空");
        }
        List<UmlsConcept> list = this.list(Wrappers.<UmlsConcept>lambdaQuery().eq(UmlsConcept::getProjectId, projectId).orderByDesc(UmlsConcept::getCreateTime));
        return list.stream().map(x -> {
            UmlsConceptExcelDTO dto = new UmlsConceptExcelDTO();
            dto.setConceptId(x.getConceptId());
            dto.setConceptName(x.getConceptName());
            dto.setPreferredName(x.getPreferredName());
            dto.setSemanticTypes(x.getSemanticTypes());
            return dto;
        }).collect(Collectors.toList());
    }

    public void saveFileData(Long projectId, File file, Long userId) {
        if (!file.exists()) {
            throw new RRException("文件上传出错");
        }

        ExcelReader reader = ExcelUtil.getReader(file);
        reader.addHeaderAlias("Concept Id", "conceptId");
        reader.addHeaderAlias("Concept Name", "conceptName");
        reader.addHeaderAlias("Preferred Name", "preferredName");
        reader.addHeaderAlias("Semantic Types", "semanticTypes");
        List<UmlsExcelDTO> umlsDataList = reader.readAll(UmlsExcelDTO.class);

        if (CollUtil.isEmpty(umlsDataList)) {
            throw new RRException("数据不能为空");
        }

        // 查重
        LambdaQueryWrapper<UmlsConcept> wrapper = Wrappers.<UmlsConcept>lambdaQuery().eq(projectId != null, UmlsConcept::getProjectId, projectId).select(UmlsConcept::getConceptId);
        List<UmlsConcept> list = this.list(wrapper);
        Set<String> dict = new HashSet<>();
        if (CollUtil.isNotEmpty(list)) {
            dict = list.stream().map(UmlsConcept::getConceptId).collect(Collectors.toSet());
        }

        List<UmlsConcept> umlsConcepts = new ArrayList<>();
        for (UmlsExcelDTO excelDTO : umlsDataList) {
            if (StrUtil.isBlank(excelDTO.getConceptId())) {
                throw new RRException("Concept Id不能为空");
            }
            if (StrUtil.isBlank(excelDTO.getConceptName())) {
                throw new RRException("Concept Name不能为空");
            }
            if (StrUtil.isBlank(excelDTO.getPreferredName())) {
                throw new RRException("Preferred Name不能为空");
            }

            if (dict.contains(excelDTO.getConceptId())) {
                continue;
            }
            UmlsConcept umlsConcept = new UmlsConcept();
            umlsConcept.setCreater(userId);
            umlsConcept.setCreateTime(new Date());
            umlsConcept.setProjectId(projectId);
            umlsConcept.setStatus(StatusEnum.enable.getValue());
            umlsConcept.setConceptId(excelDTO.getConceptId());
            umlsConcept.setConceptName(excelDTO.getConceptName());
            umlsConcept.setPreferredName(excelDTO.getPreferredName());
            umlsConcept.setSemanticTypes(excelDTO.getSemanticTypes());
            umlsConcepts.add(umlsConcept);
        }
        if (CollUtil.isNotEmpty(umlsConcepts)) {
            this.saveBatch(umlsConcepts);
        }
    }

}
