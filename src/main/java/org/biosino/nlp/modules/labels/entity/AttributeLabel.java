package org.biosino.nlp.modules.labels.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR> @date 2020-12-21 14:44:13
 */
@Data
@TableName("t_attribute_label")
public class AttributeLabel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField("entity_label_id")
    private Long entityLabelId;

    /**
     * 标签名称
     */
    private String name;

    private String field;

    private Integer orderNumber;

    /**
     * 停启状态(1是正常，0是禁用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 逻辑删除 1代表删除 0代表未删除
     */
    @TableLogic
    private Integer deleted;


    /**
     * SCDP 中的字段
     */
    private String pkgId;
    private String pkgCode;
    private String pkgVersion;
    private String pkgName;
    private String labelId;
    private String labelCode;
    private String labelVersion;
    private String labelSerCode;
}
