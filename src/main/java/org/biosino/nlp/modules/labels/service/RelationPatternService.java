package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.labels.dto.PatternDTO;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-01 20:42:07
 */
public interface RelationPatternService extends IService<RelationPattern> {

    String saveImage(Long id, MultipartFile multipartFile);

    PageUtils queryPage(PatternDTO patternDTO);

    void updateRelationPattern(RelationPattern relationPattern);

    void saveOne(RelationPattern relationPattern);

    boolean existByName(String name, Long projectId);

    RelationPattern getByProjectIdAndPatternId(Long projectId, Long patternId);

    Boolean removePatternsByIds(List<Long> asList);

    List<RelationPattern> findAllEnableByProjectId(Long projectId);
}

