package org.biosino.nlp.modules.labels.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.nlp.modules.project.dto.BaseDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/7 11:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UmlsConceptDTO extends BaseDTO {
    private String conceptId;
    private String conceptName;
    private String preferredName;
    private String semanticTypes;
    private Integer status;
    private Integer creater;
    private Date createTime;
    private Long projectId;

}
