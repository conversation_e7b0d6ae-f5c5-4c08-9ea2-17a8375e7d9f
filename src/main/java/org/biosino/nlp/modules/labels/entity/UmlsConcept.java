package org.biosino.nlp.modules.labels.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-05-07 09:41:27
 */
@Data
@TableName("t_umls_concept")
public class UmlsConcept implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 逻辑删除 1代表删除 0代表未删除
     */
    @TableId
    private Long id;
    /**
     * 标签id
     */
    private String conceptId;
    /**
     * 标签名字
     */
    private String conceptName;
    /**
     * preferred_name
     */
    private String preferredName;
    /**
     * semantic_types
     */
    private String semanticTypes;
    /**
     * 停启状态(1是正常，0是禁用)
     */
    private Integer status;
    /**
     * 创建人id
     */
    private Long creater;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 所属项目id
     */
    private Long projectId;
    /**
     * 逻辑删除 1代表删除 0代表未删除
     */
    @TableLogic
    private Integer deleted;

}
