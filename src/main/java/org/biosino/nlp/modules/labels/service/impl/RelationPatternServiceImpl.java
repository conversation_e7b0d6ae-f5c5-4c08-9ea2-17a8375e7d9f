package org.biosino.nlp.modules.labels.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.FileUtils;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.labels.dao.RelationPatternDao;
import org.biosino.nlp.modules.labels.dto.PatternDTO;
import org.biosino.nlp.modules.labels.dto.SchemaDataDTO;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.labels.service.RelationPatternService;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;


/**
 * <AUTHOR> Li
 * @date 2021-01-25 10:06:31
 */
@Service("relationPatternService")
public class RelationPatternServiceImpl extends ServiceImpl<RelationPatternDao, RelationPattern> implements RelationPatternService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RelationshipRepository relationshipRepository;

    @Autowired
    private RelationLabelService relationLabelService;

    @Autowired
    private EntityLabelService entityLabelService;

    @Override
    public PageUtils queryPage(PatternDTO dto) {
        LambdaQueryWrapper<RelationPattern> queryWrapper = Wrappers.<RelationPattern>lambdaQuery();

        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), RelationPattern::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getDescription()), RelationPattern::getDescription, dto.getDescription())
                .eq(dto.getStatus() != null, RelationPattern::getStatus, dto.getStatus())
                .eq(dto.getProjectId() != null, RelationPattern::getProjectId, dto.getProjectId());
        IPage<RelationPattern> page = this.page(new org.biosino.nlp.common.utils.Query<RelationPattern>().getPage(dto),
                queryWrapper.orderByAsc(RelationPattern::getOrderNum));
        return new PageUtils(page);
    }

    @Override
    public void updateRelationPattern(RelationPattern relationPattern) {
        String schemaData = relationPattern.getSchemaData();
        Integer status = relationPattern.getStatus();
        if (schemaData != null) {
            try {
                JSONArray jsonArray = JSON.parseArray(schemaData);
                List<SchemaDataDTO> dtos = new ArrayList<>();
                Set<Integer> orderSet = new HashSet<>();
                for (Object obj : jsonArray) {
                    SchemaDataDTO dataDTO = JSON.parseObject(obj.toString(), SchemaDataDTO.class);
                    if (dataDTO.getSubject().getMultiple() != null && dataDTO.getSubject().getMultiple() &&
                            dataDTO.getObjects().getMultiple() != null && dataDTO.getObjects().getMultiple()) {
                        throw new RRException("三元组模板中，禁止多对多关系，请至少设置一边的multiple为false");
                    }
                    if (dataDTO.getOrder() == null) {
                        throw new RRException("模板中order字段必须填写");
                    }
                    if (!orderSet.add(dataDTO.getOrder())) {
                        throw new RRException("模板中order字段不能重复");
                    }
                    if (dataDTO.getDefaultRelation() != null && !relationLabelService.existsEnableByIdAndProjectId(dataDTO.getDefaultRelation(), relationPattern.getProjectId())) {
                        throw new RRException(StrUtil.format("defaultRelation：{} 在此项目中不存在，需使用此项目存在且启用的关系标签id", dataDTO.getDefaultRelation()));
                    }
                    Set<Long> entityLabelIds = new HashSet<>();
                    Optional.of(dataDTO.getSubject())
                            .map(SchemaDataDTO.Lang::getEntityTypes)
                            .ifPresent(entityLabelIds::addAll);
                    Optional.of(dataDTO.getObjects())
                            .map(SchemaDataDTO.Lang::getEntityTypes)
                            .ifPresent(entityLabelIds::addAll);
                    for (Long entityLabelId : entityLabelIds) {
                        if (entityLabelId != null && !entityLabelService.existsEnableByIdAndProjectId(entityLabelId, relationPattern.getProjectId())) {
                            throw new RRException(StrUtil.format("entityTypes：{} 在此项目中不存在,需使用此项目存在且启用的实体标签id", entityLabelId));
                        }
                    }
                    dtos.add(dataDTO);
                }
                relationPattern.setSchemaData(JSON.toJSONString(dtos));
            } catch (JSONException e) {
                throw new RRException("JSON内容错误");
            }
        }
        if (status != null) {
            if (status.equals(StatusEnum.disable.getValue())) {
                if (relationshipRepository.existsByPatternIdAndDeleted(relationPattern.getId().intValue(), false)) {
                    throw new RRException("已被使用，不能禁用");
                }
            }
        }
        this.updateById(relationPattern);
    }

    @Override
    public void saveOne(RelationPattern relationPattern) {
        relationPattern.setCreateTime(new Date());
        relationPattern.setSchemaData("[]");
        relationPattern.setStatus(StatusEnum.enable.getValue());
        this.save(relationPattern);
    }

    @Override
    public boolean existByName(String name, Long projectId) {
        LambdaQueryWrapper<RelationPattern> query = Wrappers.<RelationPattern>lambdaQuery();
        query.eq(RelationPattern::getName, name).eq(RelationPattern::getProjectId, projectId);
        List<RelationPattern> relationPatternList = baseMapper.selectList(query);
        return relationPatternList.size() > 0;
    }

    @Override
    public RelationPattern getByProjectIdAndPatternId(Long projectId, Long patternId) {
        LambdaQueryWrapper<RelationPattern> query = Wrappers.<RelationPattern>lambdaQuery();
        query.eq(RelationPattern::getId, patternId).eq(RelationPattern::getProjectId, projectId);
        return baseMapper.selectOne(query);
    }

    @Override
    public List<RelationPattern> findAllEnableByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<RelationPattern>lambdaQuery()
                .eq(RelationPattern::getProjectId, projectId)
                .eq(RelationPattern::getStatus, StatusEnum.enable.getValue()));
    }

    @Override
    public Boolean removePatternsByIds(List<Long> asList) {
        List<Relationship> list = new ArrayList<>();
        for (Long id : asList) {
            List<Relationship> relationships = mongoTemplate.find(Query.query(Criteria.where("pattern_id").is(id)), Relationship.class);
            list.addAll(relationships);
            if (relationships.size() == 0) {
                this.removeById(id);
            }
        }
        return list.size() > 0;
    }

    /**
     * 将图片转为Base64编码并保存
     *
     * @param id
     * @param multipartFile
     */
    @Override
    public String saveImage(Long id, MultipartFile multipartFile) {
        File file = FileUtils.transferToFile(multipartFile);
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        String[] image = {"png", "gif", "pjp", "jpg", "pjpeg", "jpeg", "jpeg"};
        List<String> imageArr = Arrays.asList(image);
        if (!imageArr.contains(suffix)) {
            throw new RRException("请上传图片");
        }
        if (multipartFile.getSize() > 100 * 1024) {
            throw new RRException("图片大小不能超过100KB，请重新上传");
        }
        String encode = Base64.encode(file);
        String imageEncode = "data:image/" + suffix + ";base64," + encode;
        RelationPattern pattern = new RelationPattern();
        pattern.setId(id);
        pattern.setImage(imageEncode);
        try {
            this.updateById(pattern);
        } catch (Exception e) {
            throw new RRException("文件过大");
        } finally {
            FileUtil.del(file);
        }
        return imageEncode;
    }

}
