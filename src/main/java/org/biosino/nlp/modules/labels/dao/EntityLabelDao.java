package org.biosino.nlp.modules.labels.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.entity.EntityLabel;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020-12-01 10:54:33
 */
@Mapper
public interface EntityLabelDao extends BaseMapper<EntityLabel> {

    @Select("SELECT IFNULL(MAX(order_num), 0) num FROM t_entity_label WHERE project_id = #{projectId}")
    Integer selectMaxOrderNum(@Param("projectId") Long projectId);

    List<LabelUseVo> getLabelUseInfo(List<String> queryCode);
}
