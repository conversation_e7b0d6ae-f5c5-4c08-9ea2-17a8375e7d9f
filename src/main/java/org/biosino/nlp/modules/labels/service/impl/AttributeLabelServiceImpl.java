package org.biosino.nlp.modules.labels.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.dao.AttributeLabelDao;
import org.biosino.nlp.modules.labels.dao.EntityLabelDao;
import org.biosino.nlp.modules.labels.dto.AttributeDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.project.dto.BaseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AttributeLabelServiceImpl extends ServiceImpl<AttributeLabelDao, AttributeLabel> implements AttributeLabelService {

    @Autowired
    private AttributesRepository attributeRepository;

    @Autowired
    private EntityLabelDao entityLabelDao;

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public void add(AttributeLabel attributeLabel) {
        attributeLabel.setCreateTime(new Date());
        final Integer orderNumber = attributeLabel.getOrderNumber();
        int num = orderNumber == null ? 1 : orderNumber;
        if (attributeLabel.getId() == null) {

            if (orderNumber == null) {
                LambdaQueryWrapper<AttributeLabel> lambdaQuery = Wrappers.<AttributeLabel>lambdaQuery();
                lambdaQuery.eq(AttributeLabel::getEntityLabelId, attributeLabel.getEntityLabelId())
                        .isNotNull(AttributeLabel::getOrderNumber)
                        .orderByDesc(AttributeLabel::getOrderNumber);

                final BaseDTO param = new BaseDTO();
                param.setLimit(1);
                IPage<AttributeLabel> page = this.page(new Query<AttributeLabel>().getPage(param), lambdaQuery);
                List<AttributeLabel> records = page.getRecords();
                if (CollUtil.isNotEmpty(records)) {
                    num = records.get(0).getOrderNumber() + 1;
                }
            }
        }
        attributeLabel.setOrderNumber(num);

        attributeLabel.setStatus(StatusEnum.enable.getValue());
        // 前端会做一个非空的判断
        this.saveOrUpdate(attributeLabel);
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public void removeLabelById(Long id) {
        if (attributeRepository.existsByAttrLabelId(id)) {
            AttributeLabel attributeLabel = this.getById(id);
            throw new RRException(StrUtil.format("属性标签：{} 已被使用无法删除", attributeLabel.getName()));
        }
        this.removeById(id);
    }

    @Override
    public List<AttributeLabel> findAllByEntityLabelId(Long entityLabelId) {
        Wrapper<AttributeLabel> queryWrapper = new LambdaQueryWrapper<AttributeLabel>().eq(AttributeLabel::getEntityLabelId, entityLabelId)
                .orderByAsc(AttributeLabel::getOrderNumber);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttributeLabel> findAllEnableByEntityLabelIdIn(Collection<Long> entityLabelIds) {
        if (CollUtil.isEmpty(entityLabelIds)) {
            return new ArrayList<>();
        }
        Wrapper<AttributeLabel> queryWrapper = new LambdaQueryWrapper<AttributeLabel>()
                .in(AttributeLabel::getEntityLabelId, entityLabelIds)
                .eq(AttributeLabel::getStatus, StatusEnum.enable.getValue())
                .orderByAsc(AttributeLabel::getOrderNumber);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Long, List<AttributeLabel>> findAllEnableMapByEntityLabelIdIn(Collection<Long> entityLabelIds) {
        if (CollUtil.isEmpty(entityLabelIds)) {
            return new LinkedHashMap<>();
        }
        final Wrapper<AttributeLabel> queryWrapper = new LambdaQueryWrapper<AttributeLabel>()
                .in(AttributeLabel::getEntityLabelId, entityLabelIds)
                .eq(AttributeLabel::getStatus, StatusEnum.enable.getValue())
                .orderByAsc(AttributeLabel::getOrderNumber);

        final List<AttributeLabel> list = this.list(queryWrapper);

        final Map<Long, List<AttributeLabel>> map = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            for (AttributeLabel attributeLabel : list) {
                final Long entityLabelId = attributeLabel.getEntityLabelId();
                List<AttributeLabel> labels = map.get(entityLabelId);
                if (labels == null) {
                    labels = new ArrayList<>();
                }
                labels.add(attributeLabel);
                map.put(entityLabelId, labels);
            }
        }
        return map;
    }

    @Override
    public boolean existByNameInProject(AttributeDTO dto) {
        int count = 0;
        if (StrUtil.isBlank(dto.getName())) {
            throw new RRException("参数错误，标签名不能为空");
        }
        if (dto.getEntityLabelId() == null) {
            throw new RRException("参数错误，实体标签不能为空");
        }
        EntityLabel entityLabel = entityLabelDao.selectById(dto.getEntityLabelId());
        if (entityLabel == null) {
            throw new RRException("数据库中未查询到该实体标签");
        }
        if (ObjectUtil.isNotNull(dto)) {
            // 获取项目下所有实体标签的ID
            List<EntityLabel> entityLabels = entityLabelDao.selectList(Wrappers.<EntityLabel>lambdaQuery()
                    .eq(EntityLabel::getProjectId, entityLabel.getProjectId()));

            for (EntityLabel label : entityLabels) {
                if (StrUtil.trim(label.getName()).equalsIgnoreCase(StrUtil.trim(dto.getName()))) {
                    count++;
                }
            }

            if (!entityLabels.isEmpty()) {
                List<Long> entityLabelIds = entityLabels.stream().map(EntityLabel::getId).collect(Collectors.toList());
                // 查询整个项目中是否存在相同名称的属性标签
                count += this.count(Wrappers.<AttributeLabel>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dto.getName()), AttributeLabel::getName, dto.getName())
                        .in(AttributeLabel::getEntityLabelId, entityLabelIds)
                        .ne(dto.getId() != null, AttributeLabel::getId, dto.getId()));
            }
        }
        return count > 0;
    }

    @Override
    public boolean existByFieldInProject(AttributeDTO dto) {
        int count = 0;
        if (StrUtil.isBlank(dto.getField())) {
            throw new RRException("参数错误，field不能为空");
        }
        if (dto.getEntityLabelId() == null) {
            throw new RRException("参数错误，实体标签不能为空");
        }
        EntityLabel entityLabel = entityLabelDao.selectById(dto.getEntityLabelId());
        if (entityLabel == null) {
            throw new RRException("数据库中未查询到该实体标签");
        }
        if (ObjectUtil.isNotNull(dto)) {
            // 获取项目下所有实体标签的ID
            List<EntityLabel> entityLabels = entityLabelDao.selectList(Wrappers.<EntityLabel>lambdaQuery()
                    .eq(EntityLabel::getProjectId, entityLabel.getProjectId()));

            if (!entityLabels.isEmpty()) {
                List<Long> entityLabelIds = entityLabels.stream().map(EntityLabel::getId).collect(Collectors.toList());

                // 查询整个项目中是否存在相同字段的属性标签
                count = this.count(Wrappers.<AttributeLabel>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dto.getField()), AttributeLabel::getField, dto.getField())
                        .in(AttributeLabel::getEntityLabelId, entityLabelIds)
                        .ne(dto.getId() != null, AttributeLabel::getId, dto.getId()));
            }
        }
        return count > 0;
    }

    @Override
    public List<AttributeLabel> findByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<AttributeLabel>lambdaQuery()
                .in(AttributeLabel::getId, ids)
                .eq(AttributeLabel::getStatus, StatusEnum.enable.getValue())
                .orderByAsc(AttributeLabel::getOrderNumber)
        );
    }

    @Override
    public AttributeLabel findById(Long id) {
        if (id == null) {
            return null;
        }
        return this.getById(id);
    }

    @Override
    public List<LabelUseVo> getLabelUseInfo(List<String> queryCode) {
        if (CollUtil.isEmpty(queryCode)) {
            return Collections.emptyList();
        }
        return this.baseMapper.getLabelUseInfo(queryCode);
    }
}
