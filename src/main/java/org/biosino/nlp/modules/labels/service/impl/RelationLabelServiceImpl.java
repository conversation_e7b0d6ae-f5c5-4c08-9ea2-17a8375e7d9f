package org.biosino.nlp.modules.labels.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.FileUtils;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.labels.dao.RelationLabelDao;
import org.biosino.nlp.modules.labels.dto.RelationDTO;
import org.biosino.nlp.modules.labels.dto.RelationExcelDTO;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


@Service("relationLabelService")
public class RelationLabelServiceImpl extends ServiceImpl<RelationLabelDao, RelationLabel> implements RelationLabelService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RelationshipRepository relationshipRepository;

    @Override
    public PageUtils queryPage(RelationDTO dto) {
        LambdaQueryWrapper<RelationLabel> queryWrapper = Wrappers.<RelationLabel>lambdaQuery();
        queryWrapper.eq(dto.getSpecial() != null, RelationLabel::getSpecial, dto.getSpecial())
                .like(StrUtil.isNotBlank(dto.getName()), RelationLabel::getName, dto.getName())
                .eq(dto.getStatus() != null, RelationLabel::getStatus, dto.getStatus())
                .eq(dto.getUserId() != null, RelationLabel::getUserId, dto.getUserId())
                .eq(dto.getProjectId() != null, RelationLabel::getProjectId, dto.getProjectId());

        IPage<RelationLabel> page = this.page(new Query<RelationLabel>().getPage(dto),
                queryWrapper.orderByAsc(RelationLabel::getCreateTime));

        return new PageUtils(page);
    }

    @Override
    public Boolean validateName(RelationDTO dto) {
        int count = 0;
        if (ObjectUtil.isNotNull(dto)) {
            count = this.count(Wrappers.<RelationLabel>lambdaQuery().eq(StrUtil.isNotBlank(dto.getName()), RelationLabel::getName, dto.getName())
                    .eq(dto.getProjectId() != null, RelationLabel::getProjectId, dto.getProjectId()));
        }
        return count > 0;
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public void saveRelationLabel(RelationLabel relationLabel, Long userId) {
        relationLabel.setCreateTime(new Date());
        relationLabel.setUserId(userId);
        this.save(relationLabel);
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RelationLabel> removeLabelsByIds(List<Long> ids) {
        List<RelationLabel> result = new ArrayList<>();
        for (Long id : ids) {
            RelationLabel relationLabel = this.baseMapper.selectById(id);
            if (relationshipRepository.existsByRelationId(id)) {
                result.add(relationLabel);
            } else {
                this.removeById(id);
            }
        }
        return result;
    }

    /**
     * 批量禁用
     */
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RelationLabel> changeStatus(Integer status, List<Long> ids) {
        List<RelationLabel> result = new ArrayList<>();
        // 如果是禁用，使用后的不能被禁用
        if (status.equals(StatusEnum.disable.getValue())) {
            for (Long id : ids) {
                RelationLabel relationLabel = this.baseMapper.selectById(id);
                if (relationshipRepository.existsByRelationId(id)) {
                    result.add(relationLabel);
                } else {
                    this.update(Wrappers.<RelationLabel>lambdaUpdate()
                            .eq(RelationLabel::getId, id).set(RelationLabel::getStatus, StatusEnum.disable.getValue()));
                }
            }
        } else {
            ids.forEach(id -> {
                this.update(Wrappers.<RelationLabel>lambdaUpdate()
                        .eq(RelationLabel::getId, id).set(RelationLabel::getStatus, StatusEnum.enable.getValue()));
            });
        }
        return result;
    }

    @Override
    public RelationLabel findById(Integer id) {
        return this.getById(id);
    }

    @Override
    public List<RelationLabel> getLabels(Long projectId) {
        return this.list(Wrappers.<RelationLabel>lambdaQuery().eq(RelationLabel::getProjectId, projectId)
                .eq(RelationLabel::getStatus, StatusEnum.enable.getValue()));
    }

    /**
     * 获取当前项目标签的用户信息
     *
     * @param dto
     * @return
     */
    @Override
    public Map<Long, String> getUsers(RelationDTO dto) {
        LambdaQueryWrapper<RelationLabel> wrapper = Wrappers.<RelationLabel>lambdaQuery().eq(dto.getProjectId() != null, RelationLabel::getProjectId, dto.getProjectId());
        List<RelationLabel> list = this.list(wrapper);
        Map<Long, String> userMap = new HashMap<>(16);
        list.stream().distinct().filter(Objects::nonNull).forEach(x -> {
            userMap.put(x.getUserId(), sysUserService.findNameById(x.getUserId()));
        });
        return userMap;
    }

    @Override
    public List<RelationLabel> findByIn(Set<Long> ids) {
        LambdaQueryWrapper<RelationLabel> wrapper = Wrappers.<RelationLabel>lambdaQuery().in(RelationLabel::getId, ids);
        return this.list(wrapper);
    }

    /**
     * 用户自己上传的关系标签
     */
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public Map<Integer, ArrayList<String>> uploadFile(Long projectId, MultipartFile multipartFile, Long userId) {
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        if (!"xlsx".equals(suffix) && !"xls".equals(suffix)) {
            throw new RRException("文件格式错误，请上传 Excel 文件");
        }
        File file = FileUtils.transferToFile(multipartFile);
        return validAndSave(projectId, file, userId);
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public void updateLabelById(RelationLabel relationLabel) {
        if (relationLabel.getStatus() != null) {
            if (relationLabel.getStatus().equals(StatusEnum.disable.getValue())) {
                if (relationshipRepository.existsByRelationId(relationLabel.getId())) {
                    throw new RRException("该标签已被使用，不能禁用");
                }
            }
        }
        this.updateById(relationLabel);
    }

    @Override
    @SneakyThrows
    public List<RelationExcelDTO> exportByProjectId(Long projectId) {
        List<RelationLabel> list = this.list(Wrappers.<RelationLabel>lambdaQuery().eq(RelationLabel::getProjectId, projectId));
        return list.stream().map(x -> {
            RelationExcelDTO dto = new RelationExcelDTO();
            dto.setName(x.getName());
            dto.setDescription(x.getDescription());
            if (x.getSpecial() == 1) {
                dto.setSpecial("是");
            } else {
                dto.setSpecial("否");
            }
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RelationLabel> findAllByIdIn(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return baseMapper.selectBatchIds(ids);
    }

    @Override
    public List<RelationLabel> findAllEnableByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<RelationLabel>lambdaQuery()
                .eq(RelationLabel::getProjectId, projectId)
                .eq(RelationLabel::getStatus, StatusEnum.enable.getValue()));
    }

    public boolean existsEnableByIdAndProjectId(Long id, Long projectId) {
        return this.count(Wrappers.<RelationLabel>lambdaQuery()
                .eq(RelationLabel::getId, id)
                .eq(RelationLabel::getProjectId, projectId)
                .eq(RelationLabel::getStatus, StatusEnum.enable.getValue())) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<Integer, ArrayList<String>> validAndSave(Long projectId, File file, Long userId) {
        if (!file.exists()) {
            throw new RRException("文件上传出错");
        }

        ExcelReader reader = ExcelUtil.getReader(file);
        reader.addHeaderAlias("标签名称", "name");
        reader.addHeaderAlias("标签描述", "description");
        reader.addHeaderAlias("是否特殊标签", "special");
        List<RelationExcelDTO> relationLabels = reader.readAll(RelationExcelDTO.class)
                .stream().map(BeanUtil::trimStrFields).collect(Collectors.toList());

        if (CollUtil.isEmpty(relationLabels)) {
            throw new RRException("数据不能为空");
        }

        Map<Integer, ArrayList<String>> map = new LinkedHashMap<>();
        Set<String> labelNameSet = new HashSet<>();
        for (int i = 0; i < relationLabels.size(); i++) {
            RelationExcelDTO dto = relationLabels.get(i);
            // 校验数据
            String name = dto.getName();
            String description = dto.getDescription();
            String special = dto.getSpecial();

            Set<String> msg = new LinkedHashSet<>();
            if (StrUtil.isBlank(name)) {
                msg.add("标签名称不能为空");
            }
            if (StrUtil.isNotBlank(name)) {
                if (this.count(Wrappers.<RelationLabel>lambdaQuery()
                        .eq(RelationLabel::getProjectId, projectId)
                        .eq(RelationLabel::getName, name))
                        > 0) {
                    msg.add("标签名 " + name + " 已在项目中存在");
                }
                if (!labelNameSet.add(name)) {
                    msg.add("标签名 " + name + " 重复");
                }
                if (name.length() > 20) {
                    msg.add("标签名长度不能大于20个字符");
                }
            }
            if (StrUtil.isNotBlank(description) && description.length() > 50) {
                msg.add("标签说明长度不能大于50个字符");
            }
            if (StrUtil.isNotBlank(special) && !CollUtil.contains(CollUtil.newArrayList("是", "否"), special)) {
                msg.add("是否特殊标签填写错误，只能填“是”、“否”或者不填");
            }
            if (!msg.isEmpty()) {
                map.put(i + 2, new ArrayList<>(msg));
            }
        }
        if (map.size() > 0) {
            return map;
        } else {
            for (RelationExcelDTO excelDTO : relationLabels) {
                RelationLabel relationLabel = new RelationLabel();
                relationLabel.setUserId(userId);
                relationLabel.setCreateTime(new Date());
                relationLabel.setProjectId(projectId);
                relationLabel.setStatus(StatusEnum.enable.getValue());
                relationLabel.setName(excelDTO.getName());
                if (StrUtil.isNotBlank(excelDTO.getDescription())) {
                    relationLabel.setDescription(excelDTO.getDescription());
                }
                relationLabel.setSpecial("是".equals(StrUtil.trim(excelDTO.getSpecial())) ? 1L : 0);
                this.save(relationLabel);
            }

        }
        return Collections.emptyMap();
    }
}
