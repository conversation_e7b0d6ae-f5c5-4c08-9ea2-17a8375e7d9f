package org.biosino.nlp.modules.labels.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.dto.UmlsConceptDTO;
import org.biosino.nlp.modules.labels.entity.UmlsConcept;
import org.biosino.nlp.modules.labels.service.UmlsConceptService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-05-07 09:41:27
 */
@RestController
@RequestMapping("labels/umlsconcept")
public class UmlsConceptController extends AbstractController {
    @Autowired
    private UmlsConceptService umlsConceptService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(UmlsConceptDTO dto) {
        PageUtils page = umlsConceptService.queryPage(dto);
        Map<Long, String> userMap = umlsConceptService.getUsers(dto);
        return R.success(page).put("userList", userMap);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        UmlsConcept umlsConcept = umlsConceptService.getById(id);

        return R.success(umlsConcept);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody UmlsConcept umlsConcept) {
        umlsConceptService.saveUmlsConcept(umlsConcept, getUserId());
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody UmlsConcept umlsConcept) {
        umlsConceptService.updateById(BeanUtil.trimStrFields(umlsConcept));
        return R.ok();
    }

    /**
     * 批量改变状态
     */
    @RequestMapping("/status/{status}")
    public R status(@PathVariable("status") Integer status, @RequestBody Long[] ids) {
        List<UmlsConcept> result = umlsConceptService.changeStatus(status, Arrays.asList(ids));
        return result.size() == 0 ? R.ok() : R.error(JSON.toJSONString(result));
    }

    /**
     * ConceptId 字段校验
     */
    @RequestMapping("/validateConceptId")
    public R validateName(UmlsConceptDTO dto) {
        Boolean result = umlsConceptService.validateConceptId(dto);
        return R.success(result);
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        List<UmlsConcept> result = umlsConceptService.removeUmlsConceptsByIds(Arrays.asList(ids));
        return result.size() == 0 ? R.ok() : R.error(JSON.toJSONString(result));
    }

    /**
     * 批量上传关系标签
     */
    @RequestMapping("/uploadFile")
    public R uploadFile(Long projectId, MultipartFile file) {
        umlsConceptService.uploadFile(projectId, file, getUserId());
        return R.ok();
    }

    /**
     * 批量导出关系标签
     */
    @RequestMapping("/exportByProjectId/{projectId}")
    public R findAllByProjectId(@PathVariable Long projectId) {
        return R.success(umlsConceptService.exportByProjectId(projectId));
    }
}
