package org.biosino.nlp.modules.labels.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 标签库
 *
 * <AUTHOR>
 * @date 2020-12-01 10:54:33
 */
@Data
@TableName("t_entity_label")
public class EntityLabel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 标签名字
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 停启状态(1是正常，0是禁用)
     */
    private Integer status;
    /**
     * 颜色（大写带‘#’）
     */
    private String color;

    private Integer orderNum;
    /**
     * 消歧(1为‘是’，0为‘否’)
     */
    private Boolean disambiguate;

    private Date createTime;

    /**
     * 逻辑删除字段(1是删除，0是未删除)
     */
    @TableLogic
    private Integer deleted;


    /**
     * SCDP 中的字段
     */
    private String pkgId;
    private String pkgCode;
    private String pkgVersion;
    private String pkgName;
    private String labelId;
    private String labelCode;
    private String labelVersion;
    private String labelSerCode;
}
