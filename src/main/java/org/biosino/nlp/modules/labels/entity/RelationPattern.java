package org.biosino.nlp.modules.labels.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-04-01 20:42:07
 */
@Data
@TableName("t_relation_pattern")
public class RelationPattern implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 关系模板名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 模板JSON
     */
    private String schemaData;
    /**
     * 停启状态(1是正常，0是禁用)
     */
    private Integer status;
    /**
     * 图片的base64编码
     */
    private String image;
    /**
     * 排序字段
     */
    private Integer orderNum;
    /**
     * 0是公共标签，其他是私有标签
     */
    private Long projectId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 逻辑删除 1代表删除 0代表未删除
     */
    @TableLogic
    private Integer deleted;

}
