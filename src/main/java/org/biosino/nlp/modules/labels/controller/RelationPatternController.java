package org.biosino.nlp.modules.labels.controller;

import cn.hutool.core.bean.BeanUtil;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.dto.PatternDTO;
import org.biosino.nlp.modules.labels.entity.RelationPattern;
import org.biosino.nlp.modules.labels.service.RelationPatternService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;


/**
 * <AUTHOR>
 * @date 2021-04-01 20:42:07
 */
@RestController
@RequestMapping("labels/relation/pattern")
public class RelationPatternController {
    @Autowired
    private RelationPatternService relationPatternService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(PatternDTO patternDTO) {
        PageUtils page = relationPatternService.queryPage(patternDTO);
        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        RelationPattern relationPattern = relationPatternService.getById(id);
        return R.success(relationPattern);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    //@CacheEvict(value = {"existTags_cache"}, allEntries = true)
    public R save(@RequestBody RelationPattern relationPattern) {
        relationPatternService.saveOne(relationPattern);
        return R.ok();
    }

    @RequestMapping("/existByName")
    public R existByName(String name, Long projectId) {
        boolean existByName = relationPatternService.existByName(name, projectId);
        return R.success(existByName);
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    //@CacheEvict(value = {"existTags_cache"}, allEntries = true)
    public R update(@RequestBody RelationPattern relationPattern) {
        relationPatternService.updateRelationPattern(BeanUtil.trimStrFields(relationPattern));
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    //@CacheEvict(value = {"existTags_cache"}, allEntries = true)
    public R delete(@RequestBody Long[] ids) {
        Boolean result = relationPatternService.removePatternsByIds(Arrays.asList(ids));

        return result ? R.error("该模板已被使用,无法删除!") : R.ok();
    }

    /**
     * 保存图片
     */
    //@CacheEvict(value = {"existTags_cache"}, allEntries = true)
    @RequestMapping("/saveImage")
    public R saveImage(Long id, @RequestParam("file") MultipartFile multipartFile) {
        return R.success(relationPatternService.saveImage(id, multipartFile));
    }

}
