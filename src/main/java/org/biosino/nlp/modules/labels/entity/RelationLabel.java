package org.biosino.nlp.modules.labels.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-01-25 10:06:31
 */
@Data
@TableName("t_relation_label")
public class RelationLabel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 标签名字
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 停启状态(1是正常，0是禁用)
     */
    private Integer status;

    /**
     * 特殊标签
     */
    private Long special;

    /**
     * 逻辑删除 1代表删除 0代表未删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 0是公共标签，其他是私有标签
     */
    private Long projectId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private Long userId;

}
