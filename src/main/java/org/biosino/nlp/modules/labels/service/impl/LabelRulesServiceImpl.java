package org.biosino.nlp.modules.labels.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.modules.labels.dao.LabelRulesDao;
import org.biosino.nlp.modules.labels.entity.LabelRules;
import org.biosino.nlp.modules.labels.service.LabelRulesService;
import org.biosino.nlp.modules.project.entity.Project;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标签规则配置 Service 实现
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class LabelRulesServiceImpl extends ServiceImpl<LabelRulesDao, LabelRules> implements LabelRulesService {

    @Autowired
    private ProjectService projectService;

    @Override
    public LabelRules getByProjectId(Long projectId) {
        LambdaQueryWrapper<LabelRules> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LabelRules::getProjectId, projectId);
        return this.getOne(queryWrapper);
    }

    @Override
    public void saveOrUpdate(Long projectId, String content, Long userId) {
        LabelRules existingRules = getByProjectId(projectId);

        if (existingRules != null) {
            // 更新现有规则
            existingRules.setContent(content);
            existingRules.setUpdateTime(new Date());
            existingRules.setUpdateUserId(userId);
            this.updateById(existingRules);
        } else {
            // 创建新规则
            LabelRules newRules = new LabelRules();
            newRules.setProjectId(projectId);
            newRules.setContent(content);
            newRules.setCreateTime(new Date());
            newRules.setUpdateTime(new Date());
            newRules.setCreateUserId(userId);
            newRules.setUpdateUserId(userId);
            newRules.setDeleted(0);
            this.save(newRules);
        }
    }

    @Override
    public String getLabelRuleExample(Long projectId, String labelName) {
        LabelRules labelRules = getByProjectId(projectId);

        if (labelRules == null || labelRules.getContent() == null) {
            return "";
        }

        String content = labelRules.getContent();
        return extractLabelSection(content, labelName);
    }

    @Override
    public String getLabelRuleExampleWithProject(Long projectId, String labelName) {
        StringBuilder result = new StringBuilder();

        // 获取特定标签的规则示例
        String labelExample = getLabelRuleExample(projectId, labelName);
        if (labelExample != null && !labelExample.trim().isEmpty()) {
            result.append(labelExample.trim());
        }

        return result.toString();
    }

    /**
     * 从完整的Markdown内容中提取特定标签的部分（不包含标签标题）
     */
    private String extractLabelSection(String content, String labelName) {
        if (content == null || labelName == null) {
            return "";
        }

        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inTargetSection = false;
        boolean foundSection = false;

        for (String line : lines) {
            // 检查是否是标签标题（## 标签名）
            if (line.trim().matches("^##\\s+.+$")) {
                String currentLabel = line.trim().replaceFirst("^##\\s+", "");
                if (currentLabel.equals(labelName)) {
                    inTargetSection = true;
                    foundSection = true;
                    // 不添加标签标题，只添加内容
                } else if (inTargetSection) {
                    // 遇到其他标签，结束当前标签的提取
                    break;
                } else {
                    inTargetSection = false;
                }
            } else if (inTargetSection) {
                // 在目标标签部分内，添加所有内容（除了标签标题）
                result.append(line).append("\n");
            }
        }

        if (!foundSection) {
            return "";
        }

        return result.toString().trim();
    }

    @Override
    public List<Map<String, Object>> getLabelRulesList(Long projectId, String labelName) {
        LabelRules labelRules = getByProjectId(projectId);
        List<Map<String, Object>> rules = new ArrayList<>();

        if (labelRules == null || labelRules.getContent() == null) {
            return rules;
        }

        String content = labelRules.getContent();
        String labelSection = extractLabelSection(content, labelName);

        if (labelSection.isEmpty()) {
            return rules;
        }

        // 解析规则标题（### 规则X）
        String[] lines = labelSection.split("\n");
        Pattern rulePattern = Pattern.compile("^###\\s+(.+)$");

        for (String line : lines) {
            Matcher matcher = rulePattern.matcher(line.trim());
            if (matcher.matches()) {
                String ruleTitle = matcher.group(1);
                Map<String, Object> rule = new HashMap<>();
                rule.put("value", ruleTitle);
                rule.put("label", ruleTitle);
                rules.add(rule);
            }
        }

        return rules;
    }

    @Override
    public void addRuleExample(Long projectId, String labelName, String ruleId, String exampleContent, String exampleType, String entityId, Long userId) {
        LabelRules labelRules = getByProjectId(projectId);

        if (labelRules == null) {
            throw new RuntimeException("项目规则不存在");
        }

        String content = labelRules.getContent();
        if (content == null) {
            content = "";
        }

        // 在指定标签和规则下添加示例
        String updatedContent = addExampleToLabelRule(content, labelName, ruleId, exampleContent, exampleType);

        // 更新规则内容
        labelRules.setContent(updatedContent);
        labelRules.setUpdateTime(new Date());
        labelRules.setUpdateUserId(userId);
        this.updateById(labelRules);
    }

    /**
     * 在指定标签的指定规则下添加示例
     */
    private String addExampleToLabelRule(String content, String labelName, String ruleId, String exampleContent, String exampleType) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inTargetLabel = false;
        boolean inTargetRule = false;
        boolean exampleAdded = false;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];

            // 检查是否是目标标签
            if (line.trim().matches("^##\\s+.+$")) {
                String currentLabel = line.trim().replaceFirst("^##\\s+", "");
                inTargetLabel = currentLabel.equals(labelName);
                inTargetRule = false;
            }
            // 检查是否是目标规则
            else if (inTargetLabel && line.trim().matches("^###\\s+.+$")) {
                String currentRule = line.trim().replaceFirst("^###\\s+", "");
                inTargetRule = currentRule.equals(ruleId);
            }

            result.append(line).append("\n");

            // 如果在目标规则中，寻找合适的位置插入示例
            if (inTargetRule && !exampleAdded) {
                String targetExampleType = "positive".equals(exampleType) ? "- 正例:" : "- 反例:";

                // 检查当前行是否是目标示例类型
                if (line.trim().equals(targetExampleType)) {
                    // 找到目标示例类型，在其下面添加新示例
                    // 跳过现有的示例项，找到合适的插入位置
                    int j = i + 1;
                    while (j < lines.length && lines[j].trim().startsWith("    - ")) {
                        result.append(lines[j]).append("\n");
                        j++;
                    }
                    // 在现有示例后添加新示例
                    result.append("    - ").append(exampleContent).append("\n");
                    exampleAdded = true;
                    i = j - 1; // 调整索引，因为我们已经处理了一些行
                    continue;
                }

                // 检查是否到达规则末尾（下一个规则或标签开始）
                boolean isRuleEnd = false;
                if (i + 1 < lines.length) {
                    String nextLine = lines[i + 1].trim();
                    isRuleEnd = nextLine.matches("^###\\s+.+$") || nextLine.matches("^##\\s+.+$");
                } else {
                    isRuleEnd = true; // 文件末尾
                }

                if (isRuleEnd) {
                    // 在规则末尾添加新的示例类型和示例
                    result.append(targetExampleType).append("\n");
                    result.append("    - ").append(exampleContent).append("\n");
                    exampleAdded = true;
                    inTargetRule = false;
                }
            }
        }

        return result.toString();
    }

    @Override
    public String getCombinedRulesContent(Long projectId) {
        StringBuilder combinedContent = new StringBuilder();

        // 1. 获取项目的标注规范
        Project project = projectService.getById(projectId);
        if (project != null && project.getMarkdownContent() != null && !project.getMarkdownContent().trim().isEmpty()) {
            combinedContent.append(project.getMarkdownContent().trim());
            combinedContent.append("\n\n");
        }

        // 2. 获取标签规则
        LabelRules labelRules = getByProjectId(projectId);
        if (labelRules != null && labelRules.getContent() != null && !labelRules.getContent().trim().isEmpty()) {
            combinedContent.append(labelRules.getContent().trim());
        }

        return combinedContent.toString();
    }

}
