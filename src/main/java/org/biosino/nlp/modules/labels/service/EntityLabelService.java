package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.dto.EntityLabelExcelDTO;
import org.biosino.nlp.modules.labels.dto.ScdpLabelDTO;
import org.biosino.nlp.modules.labels.dto.SearchScdpLabelDTO;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020-12-01 10:54:33
 */
public interface EntityLabelService extends IService<EntityLabel> {

//    PageUtils queryPage(Map<String, Object> params);
//
//    List<EntityLabel> listWithTree(Long id);
//
//    List<EntityLabel> removeLabelsByIds(List<Long> ids);
//
//    boolean existByCode(Map<String, Object> params);
//
//    boolean existByCode(String code);
//
//    Long getFatherIdByCode(String fatherCode);
//

    //
    List<EntityLabel> findAllEnableByProjectId(Long projectId);

    //
//    List<EntityLabel> getSecondLabelByProjectId(Long projectId);
//
//    List<EntityLabel> getEnableSecondLabels(Long labelId);
//
//    List<EntityLabel> getSecondLabelsById(Long labelId);
//
//    void banAll(Long id);
//
//    List<EntityLabel> getTagList(Long projectId);
//
    void saveOne(EntityLabel entityLabel);

    /**
     * 查询全部
     *
     * @return
     */
    List<EntityLabel> findAll(Map<String, Object> params);

    void deleteOne(Long id);


    void update(EntityLabel entityLabel);

    void deleteMore(List<Long> idList);

    void changeStatus(Integer i, List<Long> asList) throws Exception;

    boolean existByName(Map<String, Object> params);

    Map<Integer, ArrayList<String>> uploadFile(Long projectId, MultipartFile multipartFile);

    List<EntityLabelExcelDTO> exportByProjectId(Long projectId);

    boolean existsEnableByIdAndProjectId(Long id, Long projectId);

    void updateEntity(EntityLabel entityLabel);

    List<String> findAllLabelNameWithAttrByProjectId(Long projectId);

    List<ScdpLabelDTO> searchScdpLabel(SearchScdpLabelDTO dto);

    List<LabelUseVo> getLabelUseInfo(List<String> queryCode);
}

