package org.biosino.nlp.modules.labels.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签规则配置
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@TableName("t_label_rules")
public class LabelRules implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 规则内容（Markdown格式）
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 逻辑删除字段(1是删除，0是未删除)
     */
    @TableLogic
    private Integer deleted;
}
