package org.biosino.nlp.modules.labels.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/7 10:21
 */
@Data
public class SchemaDataDTO {
    @JSONField(ordinal = 1)
    private Integer order;

    @JSONField(ordinal = 2)
    private Boolean required;

    @JSONField(ordinal = 3)
    private Lang subject;

    @JSONField(ordinal = 4)
    private List<String> annotationOptions;

    @JSONField(ordinal = 5)
    private Long defaultRelation;

    @JSONField(ordinal = 6)
    private Lang objects;

    @Data
    public static class Lang {
        @JSONField(ordinal = 1)
        private String name;

        @JSONField(ordinal = 2)
        private String foreign;

        @JSONField(ordinal = 3)
        private Boolean multiple;

        @JSONField(ordinal = 4)
        private List<Long> entityTypes;
    }
}
