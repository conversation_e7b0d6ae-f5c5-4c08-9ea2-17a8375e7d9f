package org.biosino.nlp.modules.labels.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/9/1
 */
@Data
public class ScdpLabelDTO {
    // 标签包ID（只是冗余）
    private String pkgId;
    // 标签包编码（和pkgVersion一起确定唯一记录）
    private String pkgCode;
    // 标签包版本（和pkgCode一起确定唯一记录）
    private String pkgVersion;
    // 标签包名称
    private String pkgName;
    // 标签ID （只是冗余）
    private String labelId;
    // 标签编码 （和labelVersion一起确定唯一记录）
    private String labelCode;
    // 标签版本（和labelCode一起确定唯一记录）
    private String labelVersion;
    // 标签业务编码（包内唯一，是用户自行定义的一个编码可能bnlp有用）
    private String labelSerCode;
    // 标签名称
    private String labelName;
    // 标签的定义描述
    private String labelDescription;
}
