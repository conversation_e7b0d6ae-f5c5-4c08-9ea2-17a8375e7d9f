package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.labels.dto.UmlsConceptDTO;
import org.biosino.nlp.modules.labels.dto.UmlsConceptExcelDTO;
import org.biosino.nlp.modules.labels.entity.UmlsConcept;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-05-07 09:41:27
 */
public interface UmlsConceptService extends IService<UmlsConcept> {

    PageUtils queryPage(UmlsConceptDTO dto);

    void saveUmlsConcept(UmlsConcept umlsConcept, Long userId);

    Map<Long, String> getUsers(UmlsConceptDTO dto);

    List<UmlsConcept> changeStatus(Integer status, List<Long> ids);

    List<UmlsConcept> removeUmlsConceptsByIds(List<Long> ids);

    Boolean validateConceptId(UmlsConceptDTO dto);

    UmlsConcept findByConceptId(String conceptId, Long projectId);

    List<UmlsConcept> findByConceptName(String conceptName, Long projectId);

    void uploadFile(Long projectId, MultipartFile multipartFile, Long userId);

    List<UmlsConceptExcelDTO> exportByProjectId(Long projectId);
}

