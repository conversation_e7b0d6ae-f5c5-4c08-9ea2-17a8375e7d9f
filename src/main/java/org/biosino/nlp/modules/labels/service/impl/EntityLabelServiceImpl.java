package org.biosino.nlp.modules.labels.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.common.enums.StatusEnum;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.utils.FileUtils;
import org.biosino.nlp.modules.api.vo.LabelUseVo;
import org.biosino.nlp.modules.labels.dao.EntityLabelDao;
import org.biosino.nlp.modules.labels.dto.EntityLabelExcelDTO;
import org.biosino.nlp.modules.labels.dto.ScdpLabelDTO;
import org.biosino.nlp.modules.labels.dto.SearchScdpLabelDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class EntityLabelServiceImpl extends ServiceImpl<EntityLabelDao, EntityLabel> implements EntityLabelService {

    @Autowired
    private EntityRepository entityRepository;

    @Autowired
    private EntityLabelDao entityLabelDao;

//    @Autowired
//    private EntityCustomRepository entityCustomRepository;

    @Autowired
    private AttributeLabelService attributeLabelService;

    @Value("${api.scdp-api:}")
    private String SCDP_API_URL;

    private final String[] COLORS = {"#89C5F6", "#8DFFFF", "#8FF2CE", "#A4D8C2", "#C2EB7C", "#DAABEF", "#ED8C8C", "#F2D643", "#F5E8C8", "#FE8463"};

    @Override
    public List<EntityLabel> findAll(Map<String, Object> params) {
        String projectIdStr = params.get("projectId").toString();
        if (StrUtil.isBlank(projectIdStr)) {
            throw new RRException("传入参数不能为空");
        }
        Long projectId = Long.parseLong(projectIdStr);

        LambdaQueryWrapper<EntityLabel> queryWrapper = new LambdaQueryWrapper<EntityLabel>()
                .eq(EntityLabel::getProjectId, projectId).orderByAsc(EntityLabel::getOrderNum);
        return this.list(queryWrapper);
    }

    /**
     * 删除单个
     *
     * @param id
     */
    @Override
    public void deleteOne(Long id) {
        this.removeById(id);
    }

    /**
     * 多个删除、批量删除
     */
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMore(List<Long> asList) {
        List<String> result = new ArrayList<>();
        if (!asList.isEmpty()) {
            for (Long id : asList) {
                EntityLabel entityLabel = baseMapper.selectById(id);
                if (entityRepository.existsByLabelIdAndSourceNotAndDeleted(id, PreSourceEnum.SELF.getId(), false)) {
                    result.add(entityLabel.getName());
                }
            }
        }
        if (!result.isEmpty()) {
            throw new RRException(StrUtil.format("实体标签：{}，已被使用，无法删除", StrUtil.join(";", result)));
        } else {
            asList.forEach(this::deleteOne);
        }
    }

    /**
     * 首先需要结合name、color双重查询该记录是否已存在
     *
     * @param entityLabel
     */
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    public void saveOne(EntityLabel entityLabel) {

        if (StrUtil.isBlank(entityLabel.getName()) || StrUtil.isBlank(entityLabel.getColor())) {
            throw new RRException("传入参数不能为空");
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("projectId", entityLabel.getProjectId());
        params.put("name", entityLabel.getName());

        if (existByName(params)) {
            throw new RRException("该标签已经存在");
        }
        entityLabel.setColor(entityLabel.getColor().toUpperCase());
        entityLabel.setCreateTime(new Date());
        this.save(entityLabel);
    }

    /**
     * 修改
     */
    public void update(EntityLabel entityLabel) {
        if (entityLabel == null || entityLabel.getId() == null) {
            throw new RRException("标签对象不能为空");
        }
        this.updateById(entityLabel);
    }
//    /**
//     * 实体标签列表数据
//     *
//     * @param params 查询条件
//     * @return
//     */
//    @Override
//    public PageUtils queryPage(Map<String, Object> params) {
//        // 拼接查询条件
//        String dataForm = params.get("dataForm").toString();
//        EntityLabel queryVo = BeanUtil.trimStrFields(JSONArray.parseObject(dataForm, EntityLabel.class));
//        LambdaQueryWrapper<EntityLabel> queryWrapper = Wrappers.<EntityLabel>lambdaQuery();
//        if (StrUtil.isNotBlank(queryVo.getValue())) {
//            queryWrapper.like(EntityLabel::getValue, queryVo.getValue());
//        }
//        if (StrUtil.isNotBlank(queryVo.getName())) {
//            queryWrapper.like(EntityLabel::getName, queryVo.getName());
//        }
//        if (queryVo.getStatus() != null) {
//            queryWrapper.eq(EntityLabel::getStatus, queryVo.getStatus());
//        }
//        if (queryVo.getProjectId() != null) {
//            queryWrapper.eq(EntityLabel::getProjectId, queryVo.getProjectId());
//        }
//        // 对查询的数据进行分页
//        IPage<EntityLabel> page = this.page(new Query<EntityLabel>().getPage(params), queryWrapper.orderByDesc(EntityLabel::getCreateTime));
//        // 获取当前页的数据
//        List<EntityLabel> records = page.getRecords();
//        // 使用map优化查询符合条件子节点的速度
//        List<EntityLabel> collect = records.stream()
//                .collect(Collectors.groupingBy(EntityLabel::getPid))
//                .entrySet().stream()
//                .flatMap(entity -> {
//                    List<EntityLabel> entityLabels = new ArrayList<>();
//                    if (entity.getKey() == 0) {
//                        entityLabels.addAll(baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery().
//                                in(EntityLabel::getPid, entity.getValue().stream().map(EntityLabel::getId).collect(Collectors.toList()))
//                                .eq(queryVo.getStatus() != null, EntityLabel::getStatus, queryVo.getStatus())));
//                    } else {
//                        entityLabels.add(baseMapper.selectOne(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getId, entity.getKey()).eq(EntityLabel::getProjectId, queryVo.getProjectId())));
//                    }
//                    entityLabels.addAll(entity.getValue());
//
//                    return entityLabels.stream().distinct();
//                }).distinct().collect(Collectors.toList());
//        // 将查出来的实体标签封装为父子结构
//        List<EntityLabel> list = listWithTree(collect);
//        // 返回当前页数据
//        return new PageUtils(list, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
//    }


//    /**
//     * 查询指定父节点的组装父子结构
//     */
//    @Override
//    public List<EntityLabel> listWithTree(Long id) {
//        // 1.查出所有分类
//        List<EntityLabel> entityLabels = baseMapper.selectList(null);
//        // 2.组装父子结构，找到所有的同级分类
//        return entityLabels.stream().filter(x -> x.getPid() == 0)
//                .map((x) -> {
//                    x.setChildrens(getChildren(x, entityLabels));
//                    return x;
//                }).filter(x -> x.getId().equals(id))
//                .collect(Collectors.toList());
//    }

//    public List<EntityLabel> listWithTree(List<EntityLabel> entityLabels) {
//        // 2.组装父子结构，找到所有的同级分类
//        return entityLabels.stream().filter(x -> x.getPid() == 0)
//                .map((x) -> {
//                    x.setChildrens(getChildren(x, entityLabels));
//                    return x;
//                })
//                .collect(Collectors.toList());
//    }

//    /**
//     * 递归查找所有标签的子标签
//     */
//    private List<EntityLabel> getChildren(EntityLabel root, List<EntityLabel> all) {
//        return all.stream().filter(x -> x.getPid().equals(root.getId()))
//                .peek(x -> x.setChildrens(getChildren(x, all))).collect(Collectors.toList());
//    }

//    /**
//     * 根据标签ID，批量删除
//     *
//     * @param asList 标签ID集合
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public List<EntityLabel> removeLabelsByIds(List<Long> asList) {
//        List<EntityLabel> result = new ArrayList<>();
//        if (asList.size() > 0) {
//            for (Long id : asList) {
//                EntityLabel entityLabel = baseMapper.selectById(id);
//                if (entityLabel.getPid() == 0) {
//                    if (entityRepository.existsByLabelId(id)) {
//                        result.add(entityLabel);
//                    }
//                } else {
//                    if (entityCustomRepository.findSubLabelByID(id)) {
//                        result.add(entityLabel);
//                    }
//                }
//            }
//        }
//        if (result.size() > 0) {
//            return result;
//        } else {
//            asList.forEach(this::deleteParAndChildById);
//        }
//        return result;
//    }

//    @Transactional(rollbackFor = Exception.class)
//    public void deleteParAndChildById(Long id) {
//        List<EntityLabel> entityLabels = baseMapper.selectList(null);
//        entityLabels.add(baseMapper.selectById(id));
//        List<EntityLabel> collect = entityLabels.stream().filter(x -> x.getId().equals(id)).map((x) -> {
//                    x.setChildrens(getChildren(x, entityLabels));
//                    return x;
//                }).filter(x -> x.getId().equals(id))
//                .collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(collect)) {
//            for (EntityLabel entityLabel : collect) {
//                List<Long> ids = getChildrenIds(entityLabel);
//                baseMapper.deleteBatchIds(ids);
//            }
//        }
//    }

    //    @Override
//    public boolean existByCode(Map<String, Object> params) {
//        Object code = params.get("code");
//        Object projectId = params.get("projectId");
//
//        LambdaQueryWrapper<EntityLabel> queryWrapper = Wrappers.<EntityLabel>lambdaQuery();
//        queryWrapper.eq(ObjectUtil.isNotNull(code), EntityLabel::getValue, code);
//        queryWrapper.eq(ObjectUtil.isNotNull(projectId), EntityLabel::getProjectId, projectId);
//
//        return this.count(queryWrapper) != 0;
//    }
//    /**
//     * 是否存在此code标签
//     *
//     * @param code
//     * @return
//     */
//    @Override
//    public boolean existByCode(String code) {
////        QueryWrapper<EntityLabel> queryWrapper = new QueryWrapper<>();
////        queryWrapper.eq("code", code);
//        List<EntityLabel> entityLabels = baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getValue, code));
//        return entityLabels.size() > 0;
//    }
//    /**
//     * 查找当前组装好的根Label所有的子节点id
//     */
//    public List<Long> getChildrenIds(EntityLabel root) {
//        List<Long> ids = new ArrayList<>();
//        ids.add(root.getId());
//        if (CollUtil.isNotEmpty(root.getChildrens())) {
//            for (EntityLabel child : root.getChildrens()) {
//                ids.addAll(getChildrenIds(child));
//            }
//        }
//        return ids;
//    }
//    /**
//     * 代码查询父标签的id
//     *
//     * @param code
//     * @return
//     */
//    @Override
//    public Long getFatherIdByCode(String code) {
////        List<EntityLabel> entityLabels = baseMapper.selectList(new QueryWrapper<EntityLabel>().eq("code", code).eq("level", 1).eq("pid", 0));
//        List<EntityLabel> entityLabels = baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getValue, code)
//                .eq(EntityLabel::getLevel, 1).eq(EntityLabel::getPid, 0));
//        if (entityLabels.size() == 1) {
//            return entityLabels.get(0).getId();
//        }
//        return -1L;
//    }
//
//    /**
//     * 批量改变状态
//     *
//     * @param status
//     * @param asList
//     * @return
//     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    public void changeStatus(Integer status, List<Long> asList) {
        if (Arrays.asList(StatusEnum.values()).contains(status)) {
            throw new RRException("请修改为正确的状态");
        }
        List<String> result = new ArrayList<>();
        if (status == StatusEnum.disable.getValue()) {
            for (Long id : asList) {
                EntityLabel entityLabel = baseMapper.selectById(id);
                if (entityRepository.existsByLabelIdAndDeleted(id, false)) {
                    result.add(entityLabel.getName());
                } else {
                    this.update(Wrappers.<EntityLabel>lambdaUpdate().eq(EntityLabel::getId, id).set(EntityLabel::getStatus, StatusEnum.disable.getValue()));
                }
            }
        } else {
            this.update(Wrappers.<EntityLabel>lambdaUpdate().in(EntityLabel::getId, asList).set(EntityLabel::getStatus, StatusEnum.enable.getValue()));
        }
        if (!result.isEmpty()) {
            throw new RRException(StrUtil.format("实体标签：{} 已被使用，无法禁用", StrUtil.join(";", result)));
        }
    }

    @Override
    public boolean existByName(Map<String, Object> params) {
        Object name = params.get("name");
        Object projectId = params.get("projectId");
        LambdaQueryWrapper<EntityLabel> queryWrapper = Wrappers.<EntityLabel>lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotNull(name), EntityLabel::getName, name);
        queryWrapper.eq(ObjectUtil.isNotNull(projectId), EntityLabel::getProjectId, projectId);
        return this.count(queryWrapper) != 0;
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public Map<Integer, ArrayList<String>> uploadFile(Long projectId, MultipartFile multipartFile) {
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        if (!"xlsx".equals(suffix) && !"xls".equals(suffix)) {
            throw new RRException("文件格式错误，请上传 Excel 文件");
        }
        File file = FileUtils.transferToFile(multipartFile);
        return validAndSave(projectId, file);
    }

    public boolean existsEnableByIdAndProjectId(Long id, Long projectId) {
        return this.count(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getId, id)
                .eq(EntityLabel::getProjectId, projectId)
                .eq(EntityLabel::getStatus, StatusEnum.enable.getValue())) > 0;
    }

    @CacheEvict(value = {"getFirstLabels_cache"}, allEntries = true)
    @Override
    public void updateEntity(EntityLabel entityLabel) {
        if (entityLabel.getStatus().equals(StatusEnum.disable.getValue())
                && entityLabel.getId() != null
                && entityRepository.existsByLabelIdAndDeleted(entityLabel.getId(), false)
        ) {
            throw new RRException(StrUtil.format("实体标签：{} 已被使用,无法禁用", entityLabel.getName()));
        }
        this.updateById(entityLabel);
    }

    @Override
    public List<EntityLabelExcelDTO> exportByProjectId(Long projectId) {
        List<EntityLabel> list = this.list(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getProjectId, projectId)
                .orderByAsc(EntityLabel::getOrderNum));
        return list.stream().map(x -> {
            EntityLabelExcelDTO dto = new EntityLabelExcelDTO();
            dto.setName(x.getName());
            dto.setDescription(x.getDescription());
            dto.setDisambiguate(Boolean.TRUE.equals(x.getDisambiguate()) ? "是" : "否");
            dto.setColor(x.getColor());
            dto.setAttr(initAttrStr(x));
            return dto;
        }).collect(Collectors.toList());
    }

    private String initAttrStr(final EntityLabel x) {
        return attributeLabelService.list(Wrappers.<AttributeLabel>lambdaQuery().eq(AttributeLabel::getEntityLabelId, x.getId()))
                .stream().map(y -> y.getName() + ":" + y.getField()).collect(Collectors.joining(";"));
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<Integer, ArrayList<String>> validAndSave(Long projectId, File file) {
        if (!file.exists()) {
            throw new RRException("文件上传出错");
        }

        List<EntityLabelExcelDTO> entityLabels;
        try (ExcelReader reader = ExcelUtil.getReader(file)) {
            reader.addHeaderAlias("标签名", "name");
            reader.addHeaderAlias("标签说明", "description");
            reader.addHeaderAlias("消歧", "disambiguate");
            reader.addHeaderAlias("颜色", "color");
            reader.addHeaderAlias("属性", "attr");
            entityLabels = reader.readAll(EntityLabelExcelDTO.class)
                    .stream().map(BeanUtil::trimStrFields).collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(entityLabels)) {
            throw new RRException("数据不能为空");
        }
        Map<Integer, ArrayList<String>> map = new LinkedHashMap<>();
        Set<String> labelNameSet = new HashSet<>();
        for (int i = 0; i < entityLabels.size(); i++) {
            EntityLabelExcelDTO dto = entityLabels.get(i);
            // 校验数据
            String name = dto.getName();
            String description = dto.getDescription();
            String color = dto.getColor();
            String disambiguate = dto.getDisambiguate();

            Set<String> msg = new LinkedHashSet<>();
            if (StrUtil.isBlank(name)) {
                msg.add("标签名不能为空");
            }
            if (StrUtil.isNotBlank(name)) {
                if (this.count(Wrappers.<EntityLabel>lambdaQuery()
                        .eq(EntityLabel::getProjectId, projectId)
                        .eq(EntityLabel::getName, name))
                        > 0) {
                    msg.add("标签名 " + name + " 已在项目中存在");
                }
                if (!labelNameSet.add(name)) {
                    msg.add("标签名 " + name + " 重复");
                }
                if (name.length() > 20) {
                    msg.add("标签名长度不能大于20个字符");
                }
            }

            if (StrUtil.isNotBlank(description) && description.length() > 50) {
                msg.add("标签说明长度不能大于50个字符");
            }
            if (StrUtil.isNotBlank(disambiguate) && !CollUtil.contains(CollUtil.newArrayList("是", "否"), disambiguate)) {
                msg.add("消歧填写错误，只能填“是”、“否”或者不填");
            }
            if (StrUtil.isNotBlank(color) && !color.matches("^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$")) {
                msg.add("颜色值不合法");
            }
            String attr = dto.getAttr();
            if (StrUtil.isNotBlank(attr)) {
                String str = attr.replace("：", ":").replace("；", ";").trim();
                String[] split = str.split(";");
                if (split.length == 0) {
                    msg.add("属性格式填写错误");
                    break;
                }
                HashSet<String> nameSet = new HashSet<>();
                HashSet<String> fieldSet = new HashSet<>();
                for (String s : split) {
                    String[] textArr = s.split(":");
                    if (textArr.length != 2) {
                        msg.add("属性格式填写错误");
                        break;
                    }
                    String attrName = textArr[0].trim();
                    String field = textArr[1].trim();

                    if (StrUtil.isBlank(attrName)) {
                        msg.add("属性名不能为空");
                    }
                    if (attrName.length() > 20) {
                        msg.add("属性名长度不能大于20个字符");
                    }
                    if (StrUtil.isBlank(field)) {
                        msg.add("属性字段不能为空");
                    }
                    if (field.length() > 20) {
                        msg.add("属性字段长度不能大于20个字符");
                    }
                    if (!field.matches("^(?!_)[a-zA-Z0-9_]+$")) {
                        msg.add("属性字段必须是英文、下划线、数字，不能下划线开头");
                    }
                    if (!nameSet.add(attrName)) {
                        msg.add("属性名" + attrName + " 不能重复");
                    }
                    if (!fieldSet.add(field)) {
                        msg.add("属性字段" + field + " 不能重复");
                    }
                }
            }
            if (!msg.isEmpty()) {
                map.put(i + 2, new ArrayList<>(msg));
            }
        }
        // 校验不通过
        if (map.size() > 0) {
            return map;
        } else {
            // 保存数据
            int count = 0;

            Integer maxOrderNum = entityLabelDao.selectMaxOrderNum(projectId);
            if (maxOrderNum == null) {
                maxOrderNum = 0;
            }

            for (EntityLabelExcelDTO dto : entityLabels) {
                EntityLabel label = new EntityLabel();
                label.setProjectId(projectId);
                label.setName(dto.getName());
                label.setDescription(dto.getDescription());
                label.setStatus(StatusEnum.enable.getValue());
                label.setOrderNum(++maxOrderNum);
                label.setCreateTime(new Date());
                if (StrUtil.isNotBlank(dto.getDisambiguate())) {
                    if (dto.getDisambiguate().equals("是")) {
                        label.setDisambiguate(true);
                    } else {
                        label.setDisambiguate(false);
                    }
                } else {
                    label.setDisambiguate(false);
                }
                if (StrUtil.isBlank(dto.getColor())) {
                    label.setColor(COLORS[count % COLORS.length]);
                    count++;
                } else {
                    label.setColor(dto.getColor());
                }
                this.save(label);
                Long entityLabelId = label.getId();
                String attr = dto.getAttr();

                if (StrUtil.isNotBlank(attr)) {
                    String str = attr.replace("：", ":").replace("；", ";").trim();
                    String[] split = str.split(";");
                    int i = 0;
                    for (String s : split) {
                        String[] textArr = s.split(":");
                        String attrName = textArr[0].trim();
                        String field = textArr[1].trim();
                        AttributeLabel attributeLabel = new AttributeLabel();
                        attributeLabel.setName(attrName);
                        attributeLabel.setField(field);
                        attributeLabel.setOrderNumber(++i);
                        attributeLabel.setEntityLabelId(entityLabelId);
                        attributeLabel.setCreateTime(new Date());
                        attributeLabel.setStatus(StatusEnum.enable.getValue());
                        attributeLabelService.save(attributeLabel);
                    }
                }
            }
            return Collections.emptyMap();
        }
    }

//    /**
//     * 检查父节点状态
//     *
//     * @param asList
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void checkStatus(List<Long> asList) {
//        List<Long> rootIds = baseMapper.selectBatchIds(asList).stream().map(EntityLabel::getPid).distinct().collect(Collectors.toList());
//        rootIds.forEach(rootId -> {
//            int count = this.count(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getPid, rootId).eq(EntityLabel::getStatus, StatusEnum.enable.getValue()));
//            if (count > 0) {
//                this.update(Wrappers.<EntityLabel>lambdaUpdate().eq(EntityLabel::getId, rootId).set(EntityLabel::getStatus, StatusEnum.enable.getValue()));
//            } else {
//                this.update(Wrappers.<EntityLabel>lambdaUpdate().eq(EntityLabel::getId, rootId).set(EntityLabel::getStatus, StatusEnum.disable.getValue()));
//            }
//        });
//    }


    /**
     * 根据projectId查询出和该项目有关联的所有一级标签
     */
    @Override
    public List<EntityLabel> findAllEnableByProjectId(Long projectId) {
        if (projectId == null) {
            return null;
        }
        // 获取当前项目的所有一级标签
        /*return this.baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery()
                .eq(EntityLabel::getProjectId, projectId)
                .eq(EntityLabel::getStatus, StatusEnum.enable.getValue())
                .eq(EntityLabel::getPid, 0));*/
        return this.baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery()
                .eq(EntityLabel::getProjectId, projectId)
                .eq(EntityLabel::getStatus, StatusEnum.enable.getValue())
                .orderByAsc(EntityLabel::getOrderNum));
    }

    @Override
    public List<String> findAllLabelNameWithAttrByProjectId(Long projectId) {
        List<EntityLabel> entityLabels = findAllEnableByProjectId(projectId);
        List<Long> entityLabelIds = entityLabels.stream().map(EntityLabel::getId).collect(Collectors.toList());
        Map<Long, List<AttributeLabel>> entityLabelIdToAttrLabelMap = attributeLabelService.findAllEnableMapByEntityLabelIdIn(entityLabelIds);

        ArrayList<String> labelList = new ArrayList<>();

        for (EntityLabel label : entityLabels) {
            labelList.add(label.getName());

            List<AttributeLabel> attributeLabels = entityLabelIdToAttrLabelMap.get(label.getId());
            if (CollUtil.isNotEmpty(attributeLabels)) {
                for (AttributeLabel attributeLabel : attributeLabels) {
                    labelList.add(label.getName() + ":" + attributeLabel.getName());
                }
            }
        }
        return labelList;
    }

    /**
     * 去查询数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<ScdpLabelDTO> searchScdpLabel(SearchScdpLabelDTO dto) {
        /* Map<String, Object> params = new HashMap<>();
        params.put("name", dto.getName());
        params.put("page", dto.getPage());
        params.put("limit", dto.getLimit());
        String responseResult = HttpUtil.get(SCDP_API_URL + "/getLabelListByName", params, 30 * 1000);
        JSONObject entries = JSONUtil.parseObj(responseResult);
        List<ScdpLabelDTO> result = entries.getJSONArray("data").toList(ScdpLabelDTO.class); */
        ArrayList<ScdpLabelDTO> result = new ArrayList<>();
        for (Integer i = 0; i < dto.getLimit(); i++) {
            ScdpLabelDTO scdpLabelDTO = new ScdpLabelDTO();
            //     // 标签包ID（只是冗余）
            //     private String pkgId;
            //     // 标签包编码（和pkgVersion一起确定唯一记录）
            //     private String pkgCode;
            //     // 标签包版本（和pkgCode一起确定唯一记录）
            //     private String pkgVersion;
            //     // 标签包名称
            //     private String pkgName;
            //     // 标签ID （只是冗余）
            //     private String labelId;
            //     // 标签编码 （和labelVersion一起确定唯一记录）
            //     private String labelCode;
            //     // 标签版本（和labelCode一起确定唯一记录）
            //     private String labelVersion;
            //     // 标签业务编码（包内唯一，是用户自行定义的一个编码可能bnlp有用）
            //     private String labelSerCode;
            //     // 标签名称
            //     private String labelName;
            //     // 标签的定义描述
            //     private String labelDescription;

            scdpLabelDTO.setPkgId("pkgId" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setPkgName("pkgName" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setPkgCode("pkgCode" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setPkgVersion("pkgVersion" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelId("labelId" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelCode("labelCode" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelVersion("labelVersion" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelSerCode("labelSerCode" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelName("labelName" + dto.getPage() * dto.getLimit() + i);
            scdpLabelDTO.setLabelDescription("labelDescription" + dto.getPage() * dto.getLimit() + i);
            result.add(scdpLabelDTO);
        }
        return result;
    }

    @Override
    public List<LabelUseVo> getLabelUseInfo(List<String> queryCode) {
        if (CollUtil.isEmpty(queryCode)) return Collections.emptyList();
        return this.baseMapper.getLabelUseInfo(queryCode);
    }

    //    /**
//     * 根据projectId查询出和该项目有关联所有启用的标签
//     */
//    @Override
//    public List<EntityLabel> getSecondLabelByProjectId(Long projectId) {
//        // 获取当前项目的所有二级标签
//        return this.baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery()
//                .eq(EntityLabel::getProjectId, projectId)
//                .eq(EntityLabel::getStatus, StatusEnum.enable.getValue()));
//    }

//    /**
//     * 根据一级标签查询所有启用的二级标签
//     */
//    @Override
//    public List<EntityLabel> getEnableSecondLabels(Long labelId) {
//        LambdaQueryWrapper<EntityLabel> wrapper = Wrappers.<EntityLabel>lambdaQuery()
//                .eq(EntityLabel::getStatus, StatusEnum.enable.getValue())
//                .eq(EntityLabel::getPid, labelId);
//        return baseMapper.selectList(wrapper);
//    }

//    /**
//     * 根据batch_id查询出和该批次有关联的所有一级标签
//     */
//    @Override
//    public List<EntityLabel> getSecondLabelsById(Long labelId) {
//        return baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getPid, labelId));
//    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void banAll(Long id) {
//        this.update(Wrappers.<EntityLabel>lambdaUpdate().eq(EntityLabel::getId, id).set(EntityLabel::getStatus, StatusEnum.disable.getValue()));
//        this.update(Wrappers.<EntityLabel>lambdaUpdate().eq(EntityLabel::getPid, id).set(EntityLabel::getStatus, StatusEnum.disable.getValue()));
//    }

//    /**
//     * 获得已启用一级标签
//     */
//    @Override
//    public List<EntityLabel> getTagList(Long projectId) {
//        return baseMapper.selectList(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getPid, 0).eq(EntityLabel::getStatus, StatusEnum.enable.getValue()).eq(EntityLabel::getProjectId, projectId));
//    }

//    /**
//     * 保存
//     */
//    @Override
//    public void saveOne(EntityLabel entityLabel) {
//        // 如果是新增一级标签 且 是启用状态，则判断数量是否大于9
//        if (entityLabel.getPid() == 0 && StatusEnum.enable.getValue() == entityLabel.getStatus()) {
//            List<EntityLabel> tagList = getTagList(entityLabel.getProjectId());
//            if (tagList.size() >= 9) {
//                throw new RRException("启用的一级标签数量不能大于9");
//            }
//        }
//        final Date currentTime = new Date();
//        entityLabel.setCreateTime(currentTime);
//        this.save(entityLabel);
//        checkStatus(this.list(Wrappers.<EntityLabel>lambdaQuery().eq(EntityLabel::getPid, entityLabel.getPid())).stream().map(EntityLabel::getId).collect(Collectors.toList()));
//    }
}
