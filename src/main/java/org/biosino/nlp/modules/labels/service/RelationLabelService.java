package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.labels.dto.RelationDTO;
import org.biosino.nlp.modules.labels.dto.RelationExcelDTO;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-01-25 10:06:31
 */
public interface RelationLabelService extends IService<RelationLabel> {

    PageUtils queryPage(RelationDTO dto);

    Boolean validateName(RelationDTO dto);

    void saveRelationLabel(RelationLabel relationLabel, Long userId);

    List<RelationLabel> removeLabelsByIds(List<Long> ids);

    List<RelationLabel> changeStatus(Integer status, List<Long> ids);

    RelationLabel findById(Integer id);

    /**
     * 获取项目所有启用的关系标签
     */
    List<RelationLabel> getLabels(Long projectId);

    Map<Long, String> getUsers(RelationDTO dto);

    List<RelationLabel> findByIn(Set<Long> ids);

    Map<Integer, ArrayList<String>> uploadFile(Long projectId, MultipartFile multipartFile, Long userId);

    void updateLabelById(RelationLabel relationLabel);

    boolean existsEnableByIdAndProjectId(Long id, Long projectId);

    List<RelationExcelDTO> exportByProjectId(Long projectId);

    List<RelationLabel> findAllByIdIn(Collection<Long> ids);

    List<RelationLabel> findAllEnableByProjectId(Long projectId);
}

