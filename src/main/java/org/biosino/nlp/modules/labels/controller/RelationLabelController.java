package org.biosino.nlp.modules.labels.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.dto.RelationDTO;
import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021-01-25 10:06:31
 */
@RestController
@RequestMapping("labels/relation")
public class RelationLabelController extends AbstractController {

    @Autowired
    private RelationLabelService relationLabelService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(RelationDTO dto) {
        PageUtils page = relationLabelService.queryPage(dto);
        Map<Long, String> userMap = relationLabelService.getUsers(dto);
        return R.ok().put("page", page).put("userList", userMap);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        RelationLabel relationLabel = relationLabelService.getById(id);
        return R.ok().put("data", relationLabel);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody RelationLabel relationLabel) {
        relationLabelService.saveRelationLabel(BeanUtil.trimStrFields(relationLabel), getUserId());
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody RelationLabel relationLabel) {
        relationLabelService.updateLabelById(BeanUtil.trimStrFields(relationLabel));
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        List<RelationLabel> result = relationLabelService.removeLabelsByIds(Arrays.asList(ids));
        return result.size() == 0 ? R.ok() : R.error(JSON.toJSONString(result));
    }

    /**
     * code 字段校验
     */
    @RequestMapping("/validateName")
    public R validateName(RelationDTO dto) {
        Boolean result = relationLabelService.validateName(dto);
        return R.success(result);
    }

    /**
     * 批量改变状态
     */
    @RequestMapping("/status/{status}")
    public R status(@PathVariable("status") Integer status, @RequestBody Long[] ids) {
        List<RelationLabel> result = relationLabelService.changeStatus(status, Arrays.asList(ids));
        return result.size() == 0 ? R.ok() : R.error(JSON.toJSONString(result));
    }

    /**
     * 获取关系标签
     */
    @RequestMapping("/getRelationLabels")
    public R getRelations(Long projectId) {
        List<RelationLabel> list = relationLabelService.getLabels(projectId);
        return R.success(list);
    }

    /**
     * 批量上传关系标签
     */
    @RequestMapping("/uploadRelationFile")
    public R uploadRelationFile(Long projectId, MultipartFile file) {
        return R.success(relationLabelService.uploadFile(projectId, file, getUserId()));
    }

    /**
     * 批量导出关系标签
     */
    @RequestMapping("/exportByProjectId/{projectId}")
    public R findAllByProjectId(@PathVariable Long projectId) {
        return R.success(relationLabelService.exportByProjectId(projectId));
    }

}
