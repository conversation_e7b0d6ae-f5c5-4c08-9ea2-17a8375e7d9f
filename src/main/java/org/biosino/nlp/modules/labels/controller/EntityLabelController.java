package org.biosino.nlp.modules.labels.controller;

import cn.hutool.core.bean.BeanUtil;
import org.biosino.nlp.common.annotation.SysLog;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.labels.dto.ScdpLabelDTO;
import org.biosino.nlp.modules.labels.dto.SearchScdpLabelDTO;
import org.biosino.nlp.modules.labels.entity.AttributeLabel;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.entity.LabelRules;
import org.biosino.nlp.modules.labels.service.AttributeLabelService;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.biosino.nlp.modules.labels.service.LabelRulesService;
import org.biosino.nlp.modules.project.service.ProjectService;
import org.biosino.nlp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;


/**
 * <AUTHOR> Li
 * @date 2020-12-01 10:54:33
 */
@RestController
@RequestMapping("/label/entity")
public class EntityLabelController extends AbstractController {

    @Autowired
    private EntityLabelService entityLabelService;

    @Autowired
    private AttributeLabelService attributeLabelService;

    @Autowired
    private LabelRulesService labelRulesService;

    @Autowired
    private ProjectService projectService;

    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        List<EntityLabel> entityLabels = entityLabelService.findAll(params);
        return R.ok().put("data", entityLabels);
    }


    @SysLog("删除实体标签")
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        entityLabelService.deleteMore(Arrays.asList(ids));
        return R.ok();
    }

    @SysLog("更新实体标签")
    @RequestMapping("/update")
    public R update(@RequestBody EntityLabel entityLabel) {
        entityLabelService.updateEntity(BeanUtil.trimStrFields(entityLabel));
        return R.ok();
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Integer id) {
        EntityLabel entityLabel = entityLabelService.getById(id);
        return R.ok().put("data", entityLabel);
    }

    @SysLog("新增实体标签")
    @RequestMapping("/save")
    public R save(@RequestBody EntityLabel entityLabel) {
        entityLabelService.saveOne(BeanUtil.trimStrFields(entityLabel));
        return R.ok();
    }

    @SysLog("修改实体标签状态")
    @RequestMapping("/status/{status}")
    public R status(@PathVariable("status") Integer status, @RequestBody Long[] ids) throws Exception {
        entityLabelService.changeStatus(status, Arrays.asList(ids));
        return R.ok();
    }

    /**
     * name 字段校验
     */
    @RequestMapping("/existByName")
    public R existByName(@RequestParam Map<String, Object> params) {
        boolean result = entityLabelService.existByName(params);
        return R.success(result);
    }

    /**
     * 批量上传实体标签
     */
    @SysLog("批量上传实体标签")
    @RequestMapping("/importData")
    public R uploadRelationFile(Long projectId, MultipartFile file) {
        Map<Integer, ArrayList<String>> map = entityLabelService.uploadFile(projectId, file);
        return R.success(map);
    }

    /**
     * 批量导出关系标签
     */
    @RequestMapping("/exportByProjectId/{projectId}")
    public R findAllByProjectId(@PathVariable Long projectId) {
        return R.success(entityLabelService.exportByProjectId(projectId));
    }

    /**
     * 获取项目的所有标签（实体标签和属性标签）
     */
    @RequestMapping("/getAllLabelsWithAttributes")
    public R getAllLabelsWithAttributes(@RequestParam Long projectId) {
        try {
            // 获取实体标签
            Map<String, Object> params = new HashMap<>();
            params.put("projectId", projectId);
            List<EntityLabel> entityLabels = entityLabelService.findAll(params);

            // 获取所有属性标签
            List<Map<String, Object>> allLabels = new ArrayList<>();

            // 添加实体标签
            for (EntityLabel entityLabel : entityLabels) {
                Map<String, Object> labelMap = new HashMap<>();
                labelMap.put("id", entityLabel.getId());
                labelMap.put("name", entityLabel.getName());
                labelMap.put("type", "entity");
                allLabels.add(labelMap);

                // 获取该实体标签下的属性标签
                List<AttributeLabel> allByEntityLabelId = attributeLabelService.findAllByEntityLabelId(entityLabel.getId());
                for (AttributeLabel attributeLabel : allByEntityLabelId) {
                    Map<String, Object> attrMap = new HashMap<>();
                    attrMap.put("id", attributeLabel.getId());
                    attrMap.put("name", attributeLabel.getName());
                    attrMap.put("type", "attribute");
                    attrMap.put("entityLabelId", entityLabel.getId());
                    attrMap.put("entityLabelName", entityLabel.getName());
                    allLabels.add(attrMap);
                }
            }

            return R.ok().put("data", allLabels);
        } catch (Exception e) {
            return R.error("获取标签失败：" + e.getMessage());
        }
    }

    /**
     * 获取标签规则
     */
    @RequestMapping("/getLabelRules")
    public R getLabelRules(@RequestParam Long projectId) {
        try {
            LabelRules labelRules = labelRulesService.getByProjectId(projectId);
            return R.ok().put("data", labelRules);
        } catch (Exception e) {
            return R.error("获取标签规则失败：" + e.getMessage());
        }
    }

    /**
     * 获取合并的标注规范（项目标注规范 + 标签规则）
     */
    @RequestMapping("/getCombinedRules")
    public R getCombinedRules(@RequestParam Long projectId) {
        try {
            String combinedContent = labelRulesService.getCombinedRulesContent(projectId);

            // 构造返回对象，保持与原有接口兼容
            LabelRules result = new LabelRules();
            result.setProjectId(projectId);
            result.setContent(combinedContent);

            return R.ok().put("data", result);
        } catch (Exception e) {
            return R.error("获取合并标注规范失败：" + e.getMessage());
        }
    }

    /**
     * 保存标签规则
     */
    @SysLog("保存标签规则")
    @RequestMapping("/saveLabelRules")
    public R saveLabelRules(@RequestBody Map<String, Object> params) {
        try {
            Long projectId = Long.valueOf(params.get("projectId").toString());
            String content = params.get("content").toString();
            Long userId = getUserId();

            labelRulesService.saveOrUpdate(projectId, content, userId);
            return R.ok();
        } catch (Exception e) {
            return R.error("保存标签规则失败：" + e.getMessage());
        }
    }

    /**
     * 获取特定标签的规则示例（包含项目标注规范）
     */
    @RequestMapping("/getLabelRuleExample")
    public R getLabelRuleExample(@RequestParam Long projectId, @RequestParam String labelName) {
        try {
            String example = labelRulesService.getLabelRuleExampleWithProject(projectId, labelName);
            return R.ok().put("data", example);
        } catch (Exception e) {
            return R.error("获取标签规则示例失败：" + e.getMessage());
        }
    }

    /**
     * 获取特定标签的规则列表
     */
    @RequestMapping("/getLabelRulesList")
    public R getLabelRulesList(@RequestParam Long projectId, @RequestParam String labelName) {
        try {
            List<Map<String, Object>> rules = labelRulesService.getLabelRulesList(projectId, labelName);
            return R.ok().put("data", rules);
        } catch (Exception e) {
            return R.error("获取标签规则列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加规则示例
     */
    @SysLog("添加规则示例")
    @RequestMapping("/addRuleExample")
    public R addRuleExample(@RequestBody Map<String, Object> params) {
        try {
            Long projectId = Long.valueOf(params.get("projectId").toString());
            String ruleId = params.get("ruleId").toString();
            String exampleContent = params.get("exampleContent").toString();
            String exampleType = params.get("exampleType").toString();
            String entityId = params.get("entityId").toString();
            String labelName = params.get("labelName").toString();
            Long userId = getUserId();

            labelRulesService.addRuleExample(projectId, labelName, ruleId, exampleContent, exampleType, entityId, userId);
            return R.ok();
        } catch (Exception e) {
            return R.error("添加规则示例失败：" + e.getMessage());
        }
    }

    /**
     *
     */
    @RequestMapping("/searchScdpLabel")
    public R searchScdpLabel(SearchScdpLabelDTO dto) {
        List<ScdpLabelDTO> result = entityLabelService.searchScdpLabel(dto);
        return R.success(result);
    }
}
