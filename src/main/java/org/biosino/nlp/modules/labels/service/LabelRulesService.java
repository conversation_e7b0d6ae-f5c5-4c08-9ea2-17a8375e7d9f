package org.biosino.nlp.modules.labels.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.labels.entity.LabelRules;

import java.util.List;
import java.util.Map;

/**
 * 标签规则配置 Service
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface LabelRulesService extends IService<LabelRules> {

    /**
     * 根据项目ID获取标签规则
     *
     * @param projectId 项目ID
     * @return 标签规则
     */
    LabelRules getByProjectId(Long projectId);

    /**
     * 保存或更新标签规则
     *
     * @param projectId 项目ID
     * @param content   规则内容
     * @param userId    用户ID
     */
    void saveOrUpdate(Long projectId, String content, Long userId);

    /**
     * 获取特定标签的规则示例
     *
     * @param projectId 项目ID
     * @param labelName 标签名称
     * @return 该标签的规则示例（Markdown格式）
     */
    String getLabelRuleExample(Long projectId, String labelName);

    /**
     * 获取特定标签的规则示例（包含项目标注规范）
     *
     * @param projectId 项目ID
     * @param labelName 标签名称
     * @return 该标签的规则示例，包含项目标注规范（Markdown格式）
     */
    String getLabelRuleExampleWithProject(Long projectId, String labelName);

    /**
     * 获取特定标签的规则列表
     *
     * @param projectId 项目ID
     * @param labelName 标签名称
     * @return 该标签的规则列表
     */
    List<Map<String, Object>> getLabelRulesList(Long projectId, String labelName);

    /**
     * 添加规则示例
     *
     * @param projectId 项目ID
     * @param labelName 标签名称
     * @param ruleId 规则ID
     * @param exampleContent 示例内容
     * @param exampleType 示例类型（positive/negative）
     * @param entityId 实体ID
     * @param userId 用户ID
     */
    void addRuleExample(Long projectId, String labelName, String ruleId, String exampleContent, String exampleType, String entityId, Long userId);

    /**
     * 获取合并的规则内容（项目标注规范 + 标签规则）
     *
     * @param projectId 项目ID
     * @return 合并后的Markdown内容
     */
    String getCombinedRulesContent(Long projectId);
}
