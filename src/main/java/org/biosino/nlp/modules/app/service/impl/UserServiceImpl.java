package org.biosino.nlp.modules.app.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.codec.digest.DigestUtils;
import org.biosino.nlp.common.exception.RRException;
import org.biosino.nlp.common.validator.Assert;
import org.biosino.nlp.modules.app.dao.UserDao;
import org.biosino.nlp.modules.app.entity.UserEntity;
import org.biosino.nlp.modules.app.form.LoginForm;
import org.biosino.nlp.modules.app.service.UserService;
import org.springframework.stereotype.Service;


@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserDao, UserEntity> implements UserService {

    @Override
    public UserEntity queryByMobile(String mobile) {
        return baseMapper.selectOne(new QueryWrapper<UserEntity>().eq("mobile", mobile));
    }

    @Override
    public long login(LoginForm form) {
        UserEntity user = queryByMobile(form.getMobile());
        Assert.isNull(user, "手机号或密码错误");

        //密码错误
        if (!user.getPassword().equals(DigestUtils.sha256Hex(form.getPassword()))) {
            throw new RRException("手机号或密码错误");
        }

        return user.getUserId();
    }

    @Override
    public String findNameById(Long id) {
        QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
        wrapper.select("username").eq("user_id", id);
        UserEntity entity = baseMapper.selectOne(wrapper);
        if (entity != null) {
            return entity.getUsername();
        }
        return null;
    }
}
