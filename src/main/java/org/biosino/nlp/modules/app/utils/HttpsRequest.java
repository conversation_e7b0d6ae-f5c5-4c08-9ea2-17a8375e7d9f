package org.biosino.nlp.modules.app.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;

import java.net.URLDecoder;

/**
 * 关闭SSL证书校验发送get请求
 *
 * <AUTHOR>
 */
public class HttpsRequest {

    public static JSONObject get(String url) {

        JSONObject jsonResult = null;
        try {
            url = URLDecoder.decode(url, "UTF-8");

            HttpClient httpsClient;

            httpsClient = HttpsClient.getInstance();
            HttpGet request = new HttpGet(url);
            HttpResponse response = httpsClient.execute(request);

            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String strResult = EntityUtils.toString(response.getEntity());
                jsonResult = JSONObject.parseObject(strResult);
            } else {
                throw new RuntimeException("通信异常:" + url);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonResult;
    }
}
