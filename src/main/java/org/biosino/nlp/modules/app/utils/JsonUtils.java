package org.biosino.nlp.modules.app.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON工具类
 */
public class JsonUtils {

    /**
     * 剔除空串，空对象，空数组等数据
     */
    public static Object removeEmpty(Object json) {
        if (json == null) {
            return null;
        }
        try {
            if (json instanceof JSONObject) {
                JSONObject jsonObj = (JSONObject) json;
                List<String> keyList = new ArrayList<>();
                for (String k : jsonObj.keySet()) {
                    String value = jsonObj.get(k).toString();

                    // 剔除俩端空格
                    jsonObj.put(k, value.trim());

                    // 剔除空数组，空对象
                    if (isEmpty(value)) {
                        keyList.add(k);
                    } else {
                        if (isJsonObj(value)) {
                            jsonObj.put(k, removeEmpty(JSONObject.parseObject(value)));
                        } else {
                            if (isJsonArr(value)) {
                                jsonObj.put(k, removeEmpty(JSONArray.parseArray(value)));
                            }
                        }
                    }
                }
                for (String k : keyList) {
                    jsonObj.remove(k);
                }
                return jsonObj;

            } else if (json instanceof JSONArray) {

                JSONArray jsonArr = (JSONArray) json;
                int len = jsonArr.size();
                for (int i = 0; i < len; ++i) {
                    String val = jsonArr.get(i).toString();
                    if (isEmpty(val)) {
                        jsonArr.remove(i);
                    } else {
                        jsonArr.set(i, removeEmpty(jsonArr.get(i)));
                    }
                }
                return jsonArr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isJsonObj(Object o) {
        try {
            JSONObject.parseObject(o.toString());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private static boolean isEmpty(String value) {
        return StrUtil.isEmpty(value) || "{}".equals(value) || "[]".equals(value)
                || "[{}]".equals(value) || "{[]}".equals(value);
    }

    public static boolean isJsonArr(Object o) {
        try {
            JSONArray.parseArray(o.toString());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
