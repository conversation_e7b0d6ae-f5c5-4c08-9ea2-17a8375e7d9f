package org.biosino.nlp.modules.app.service;


import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.modules.app.entity.UserEntity;
import org.biosino.nlp.modules.app.form.LoginForm;

/**
 * 用户
 */
public interface UserService extends IService<UserEntity> {

    UserEntity queryByMobile(String mobile);

    /**
     * 用户登录
     *
     * @param form 登录表单
     * @return 返回用户ID
     */
    long login(LoginForm form);

    /**
     * 根据用户ID，查询用户名
     *
     * @param id 用户ID
     * @return 用户名
     */
    String findNameById(Long id);
}
