package org.biosino.nlp.modules.form.controller;

import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.form.dao.mongo.FormRepository;
import org.biosino.nlp.modules.form.entity.mongo.Form;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/12/16 14:12
 */
@RestController
@RequestMapping("/form")
public class FormController {
    @Autowired
    private FormRepository formRepository;

    @RequestMapping("/list/{id}")
    public R list(@PathVariable("id") String id) {

//        String dataForm = params.get("dataForm").toString();
//        Form queryVo = BeanUtil.trimStrFields(JSONArray.parseObject(dataForm, Form.class));
//        if (StrUtil.isNotBlank(queryVo.getId())) {
        Optional<Form> byId = formRepository.findById(id);
        if (byId.isPresent()) {
            return R.success(byId.get());
        }
//        }
        return R.ok();
    }

    @RequestMapping("/save")
//    @RequiresPermissions("label:list")
    public R save(@RequestBody Form form) {
        formRepository.save(form);
//         formRepository.save(form);
        return R.ok();
    }

}
