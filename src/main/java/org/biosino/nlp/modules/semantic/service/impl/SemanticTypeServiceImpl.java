package org.biosino.nlp.modules.semantic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.Query;
import org.biosino.nlp.modules.semantic.dao.SemanticTypeDao;
import org.biosino.nlp.modules.semantic.entity.SemanticType;
import org.biosino.nlp.modules.semantic.service.SemanticTypeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service("semanticTypesService")
public class SemanticTypeServiceImpl extends ServiceImpl<SemanticTypeDao, SemanticType> implements SemanticTypeService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SemanticType> page = this.page(
                new Query<SemanticType>().getPage(params),
                new QueryWrapper<SemanticType>()
        );

        return new PageUtils(page);
    }

    @Override
    public List<String> listByNames(List<String> semanticTypes) {
        List<String> list = new ArrayList<>();
        for (String type : semanticTypes) {
            SemanticType semanticType = baseMapper.selectOne(Wrappers.<SemanticType>lambdaQuery().eq(SemanticType::getSimple, type));
            list.add(semanticType.getName());
        }
        return list;
    }

}
