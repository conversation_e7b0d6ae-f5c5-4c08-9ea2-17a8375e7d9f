package org.biosino.nlp.modules.semantic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.modules.semantic.entity.SemanticType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-01-11 21:51:57
 */
public interface SemanticTypeService extends IService<SemanticType> {

    PageUtils queryPage(Map<String, Object> params);

    List<String> listByNames(List<String> semanticTypes);
}

