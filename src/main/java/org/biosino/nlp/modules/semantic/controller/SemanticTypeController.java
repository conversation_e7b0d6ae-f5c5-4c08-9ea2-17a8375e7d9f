package org.biosino.nlp.modules.semantic.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.biosino.nlp.common.utils.PageUtils;
import org.biosino.nlp.common.utils.R;
import org.biosino.nlp.modules.semantic.entity.SemanticType;
import org.biosino.nlp.modules.semantic.service.SemanticTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021-01-11 21:51:57
 */
@RestController
@RequestMapping("history/semantictypes")
public class SemanticTypeController {
    @Autowired
    private SemanticTypeService semanticTypeService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("history:semantictypes:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = semanticTypeService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("history:semantictypes:info")
    public R info(@PathVariable("id") Long id) {
        SemanticType semanticType = semanticTypeService.getById(id);

        return R.ok().put("semanticType", semanticType);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("history:semantictypes:save")
    public R save(@RequestBody SemanticType semanticType) {
        semanticTypeService.save(semanticType);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("history:semantictypes:update")
    public R update(@RequestBody SemanticType semanticType) {
        semanticTypeService.updateById(semanticType);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("history:semantictypes:delete")
    public R delete(@RequestBody Long[] ids) {
        semanticTypeService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

}
