package org.biosino.nlp.modules.semantic.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-01-11 21:51:57
 */
@Data
@TableName("t_semantic_types")
public class SemanticType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     *
     */
    private String simple;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String name;

}
