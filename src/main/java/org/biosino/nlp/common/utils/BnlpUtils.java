package org.biosino.nlp.common.utils;

import cn.hutool.core.collection.CollUtil;

import java.util.List;

public class BnlpUtils {

    public static final String CONTENT = "content";

    public static final String ANNOTATION = "annotations";

    public static final String ID = "id";

    public static final List<String> IGNORE_FIELDS =
            CollUtil.newArrayList("cls", "title", "content", "name", "src", "formats", "rowspan");

    public static final List<String> BATCH_IGNORE_FIELDS =
            CollUtil.newArrayList("cls", "title", "id", "name", "src", "formats", "rowspan");

}
