package org.biosino.nlp.common.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.util.Map;

@Slf4j
public class MathUtils {

    /**
     * 计算 Fleiss' Kappa (κ)
     *
     * @param dataMat 数据矩阵
     * @param N       行数
     * @param k       列数
     * @param n       标注员数量
     * @return k
     */
    public static double fleissKappa(double[][] dataMat, int N, int k, int n) {
        double sum = 0.0;
        double p0 = 0.0;

        // Step 1: 计算 P0
        for (int i = 0; i < N; i++) {
            double temp = 0.0;
            for (int j = 0; j < k; j++) {
                sum += dataMat[i][j];
                temp += Math.pow(dataMat[i][j], 2);
            }
            temp -= n;
            temp /= (n - 1) * n;
            p0 += temp;
        }
        p0 = p0 / N;

        // Step 2: 计算 pe
        double[] ysum = new double[k];
        for (int j = 0; j < k; j++) {
            for (int i = 0; i < N; i++) {
                ysum[j] += dataMat[i][j];
            }
            ysum[j] = Math.pow(ysum[j] / sum, 2);
        }

        double pe = 0.0;
        for (double y : ysum) {
            pe += y;
        }

        // Step 3: 计算 Fleiss' Kappa
        if ((1 - pe) == 0) {
            return 0D;
        }
        return (p0 - pe) / (1 - pe);
    }


    /**
     * Gwet's AC1核心方法
     * @param inputMatrix
     */
    public static double gwetsAC1(double[][] inputMatrix) {

        // 步骤1：将输入矩阵转换为标注者选择列表
        String[] rater1 = new String[inputMatrix.length];
        String[] rater2 = new String[inputMatrix.length];
        for (int i = 0; i < inputMatrix.length; i++) {
            if (inputMatrix[i][0] == 2 && inputMatrix[i][1] == 0) {
                rater1[i] = "A";
                rater2[i] = "A";
            } else if (inputMatrix[i][0] == 1 && inputMatrix[i][1] == 1) {
                rater1[i] = "A";
                rater2[i] = "B";
            }
        }

        // 步骤2：构建混淆矩阵
        int[][] confusionMatrix = new int[2][2]; // [A, B] 类别索引0=A,1=B
        for (int i = 0; i < rater1.length; i++) {
            int r1Idx = "A".equals(rater1[i]) ? 0 : 1;
            int r2Idx = "A".equals(rater2[i]) ? 0 : 1;
            confusionMatrix[r1Idx][r2Idx]++;
        }

        // 步骤3：计算Gwet's AC1
        return calculateGwetAC1(confusionMatrix);
    }

    // 计算Gwet's AC1核心方法
    private static double calculateGwetAC1(int[][] confusionMatrix) {
        int total = 0;
        for (int[] row : confusionMatrix) {
            for (int count : row) {
                total += count;
            }
        }

        // 观测一致性 Po = 对角线元素之和 / 总样本数
        double po = (confusionMatrix[0][0] + confusionMatrix[1][1]) / (double) total;

        // 类别数 k=2（A和B）
        int k = 2;

        // 期望一致性 Pe = 1 - (k-1)/k
        double pe = 1.0 - (k - 1.0) / k;

        // AC1公式
        return (po - pe) / (1 - pe);
    }

    /**
     * 计算 Krippendorff's Alpha
     *
     * @param matrix 矩阵
     * @return a
     */
    public static double calculateKrippendorffAlpha(int[][] matrix) {
        // 定义一个 JSON 格式的矩阵
//        String jsonMatrix = "[[1.0, 1.0], [2.0, 2.0], [5.0, 5.0], [3.0, 4.0], [0.0, 0.0]]";
        String jsonMatrix = JSON.toJSONString(matrix);

        String scriptPath = Constant.getHomeDir() + "/script/krippendorff_alpha.py";

        if (!FileUtil.exist(scriptPath)) {
            throw new ServiceException("krippendorff_alpha.py文件不存在");
        }

        File tempFile = null;

        // 调用 Python 脚本
        try {
            tempFile = File.createTempFile(IdUtils.fastSimpleUUID(), ".json");
            try (FileWriter writer = new FileWriter(tempFile)) {
                writer.write(jsonMatrix);
            }

            // 创建 ProcessBuilder
            ProcessBuilder processBuilder = new ProcessBuilder("python3", scriptPath, tempFile.getAbsolutePath());
            // 将错误流合并到输出流
            processBuilder.redirectErrorStream(true);

            // 启动进程
            Process process = processBuilder.start();

            // 获取输出结果
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            // 等待进程完成
            int i = process.waitFor();
            log.debug("返回码：{}, 返回结果：{}", i, output);
            if ("None".equalsIgnoreCase(output.toString())) {
                return 0D;
            }
            double result = Double.parseDouble(output.toString());
//            if (result < 0) {
//                return 0D;
//            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("krippendorff alpha运算出错，请联系管理员");
        } finally {
            // 删除临时文件
            if (tempFile != null) {
                tempFile.delete();
            }
        }
    }

    /**
     * 打印矩阵
     */
    public static void printMatrix(double[][] matrix) {
        System.out.println("[");
        for (int i = 0; i < matrix.length; i++) {
            System.out.print("  [");
            for (int j = 0; j < matrix[i].length; j++) {
                System.out.print(matrix[i][j]);
                if (j < matrix[i].length - 1) {
                    System.out.print(", ");
                }
            }
            System.out.print("]");
            if (i < matrix.length - 1) {
                System.out.println(",");
            }
        }
        System.out.println("\n]");
    }

    public static void printMatrix(int[][] matrix) {
        System.out.println("[");
        for (int i = 0; i < matrix.length; i++) {
            System.out.print("  [");
            for (int j = 0; j < matrix[i].length; j++) {
                System.out.print(matrix[i][j]);
                if (j < matrix[i].length - 1) {
                    System.out.print(", ");
                }
            }
            System.out.print("]");
            if (i < matrix.length - 1) {
                System.out.println(",");
            }
        }
        System.out.println("\n]");
    }

    public static int calculateDendrogram(Map<String, Map<String, Double>> data, Long projectId) {
        // 定义一个 JSON 格式的矩阵
        String jsonMatrix = JSON.toJSONString(data);

        String scriptPath = Constant.getHomeDir() + "/script/dendrogram.py";

        if (!FileUtil.exist(scriptPath)) {
            throw new ServiceException("dendrogram.py文件不存在");
        }

        File tempFile = null;

        // 调用 Python 脚本
        try {
            tempFile = File.createTempFile(IdUtils.fastSimpleUUID(), ".json");
            try (FileWriter writer = new FileWriter(tempFile)) {
                writer.write(jsonMatrix);
            }
            File pngFile = new File(Constant.getHomeDir(Constant.DirectoryEnum.temp), projectId + ".png");

            // 创建 ProcessBuilder
            ProcessBuilder processBuilder = new ProcessBuilder("python3", scriptPath, tempFile.getAbsolutePath(), pngFile.getAbsolutePath());
            // 将错误流合并到输出流
            processBuilder.redirectErrorStream(true);

            // 启动进程
            Process process = processBuilder.start();

            // 获取输出结果
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            // 等待进程完成
            int i = process.waitFor();
            log.debug("返回码：{}, 返回结果：{}", i, output);
            return i;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("krippendorff alpha运算出错，请联系管理员");
        } finally {
            // 删除临时文件
            if (tempFile != null) {
                tempFile.delete();
            }
        }
    }


}
