package org.biosino.nlp.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.common.exception.RRException;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * 下载工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DownloadUtil {
    /**
     * 缓存时间秒数
     */
    private static final long CACHE_TIME_SECOND = 12 * 60 * 60;

    public static void download(File file, HttpServletResponse response) throws IOException {
        download(file, null, response, false);
    }

    public static void download(File file, String downloadName, HttpServletResponse response) throws IOException {
        download(file, downloadName, response, false);
    }

    public static void downloadAndDelete(File tempFile, HttpServletResponse response) throws IOException {
        download(tempFile, null, response, true);
    }

    public static void download(File file, String downloadName, HttpServletResponse response, boolean deleteAfterDownload) throws IOException {
        response.reset();
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        if (file == null || !file.isFile() || !file.exists() || !file.canRead()) {
            response.setContentType(MediaType.TEXT_HTML_VALUE);
            try (PrintWriter writer = response.getWriter()) {
                writer.write("The file you downloaded does not exist!");
            }
            return;
        }
        response.setContentType(MediaType.MULTIPART_FORM_DATA_VALUE);
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + (StrUtil.isBlank(downloadName) ? percentEncode(file.getName()) : percentEncode(downloadName)));
        // FileUtil.writeToStream(file, os);
        try (InputStream is = new FileInputStream(file);
             OutputStream os = response.getOutputStream()) {
            IoUtil.copy(is, os);
            IoUtil.close(os);
            IoUtil.close(is);
            if (deleteAfterDownload) {
                FileUtil.del(file);
            }
        }
    }

    public static void downloadByStream(InputStream is, String downloadName, HttpServletResponse response) throws IOException {
        response.reset();
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        if (is == null) {
            response.setContentType(MediaType.TEXT_HTML_VALUE);
            try (PrintWriter writer = response.getWriter()) {
                writer.write("The file you downloaded does not exist!");
            }
            return;
        }
        response.setContentType(MediaType.MULTIPART_FORM_DATA_VALUE);
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + (StrUtil.isBlank(downloadName) ? percentEncode("download_file") : percentEncode(downloadName)));
        try (OutputStream os = response.getOutputStream()) {
            IoUtil.copy(is, os);
            IoUtil.close(os);
        } finally {
            IoUtil.close(is);
        }
    }


    /**
     * 在浏览器中预览pdf，不直接下载文件
     *
     * @param file
     * @param fileName
     * @return
     */
    public static ResponseEntity<Resource> previewPDF(final File file, String fileName) {
        if (!file.getName().toLowerCase().endsWith(".pdf")) {
            throw new RRException("非法的PDF文件名");
        }
        if (fileName != null && !fileName.toLowerCase().endsWith(".pdf")) {
            fileName += ".pdf";
        }
        return downLoadWithResource(file, fileName, true, true, MediaType.APPLICATION_PDF);
    }

    /**
     * 使用ResponseEntity下载文件
     *
     * @param file
     * @param fileName
     * @return
     */
    public static ResponseEntity<Resource> downLoadWithResource(final File file, final String fileName) {
        return downLoadWithResource(file, fileName, false, true, null);
    }

    public static ResponseEntity<Resource> downLoadWithResource(final File file, final String fileName, final boolean isInline,
                                                                final boolean noCache, final MediaType mediaType) {
        if (file == null || !file.exists() || !file.isFile() || !file.canRead()) {
            return ResponseEntity.badRequest().build();
        }
        try {
            final HttpHeaders headers = new HttpHeaders();
            // 发给客户端的额外header名称
            final String otherHeaderKey = "filename";
            headers.setAccessControlExposeHeaders(Collections.singletonList(otherHeaderKey));
            final String encodeFileName = percentEncode(fileName == null ? file.getName() : fileName);
            headers.add(otherHeaderKey, encodeFileName);
            if (isInline) {
                // 浏览器嵌入显示，例如pdf预览
                headers.setContentDisposition(ContentDisposition.parse(String.format("inline; filename=\"%s\"", encodeFileName)));
            } else {
                headers.setContentDisposition(ContentDisposition.parse(String.format("attachment; filename=\"%s\"", encodeFileName)));
            }
            headers.setContentType(mediaType == null ? MediaType.APPLICATION_OCTET_STREAM : mediaType);
            if (noCache) {
                headers.setPragma("no-cache");
                headers.setCacheControl("no-cache, no-store, must-revalidate");
            } else {
                // headers.setExpires(0);
                headers.setCacheControl("max-age=" + CACHE_TIME_SECOND);
            }
            final FileSystemResource fileResource = new FileSystemResource(file);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileResource.contentLength())
                    .body(fileResource);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

}
