package org.biosino.nlp.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;

/**
 * 常量
 *
 * <AUTHOR>
 */
@Component
public class Constant {
    /**
     * 超级管理员ID
     */
    public static final int SUPER_ADMIN = 1;

    /**
     * 自动审核员的ID
     */
    public static final long AUTO_AUDITOR = 2L;
    /**
     * 当前页码
     */
    public static final String PAGE = "page";
    /**
     * 每页显示记录数
     */
    public static final String LIMIT = "limit";
    /**
     * 排序字段
     */
    public static final String ORDER_FIELD = "sidx";
    /**
     * 排序方式
     */
    public static final String ORDER = "order";
    /**
     * 升序
     */
    public static final String ASC = "asc";

    /**
     * 全部标注状态
     */
    public static final String ALL = "all";

    public static final String LIMIT_ONE = "limit 1";

    /**
     * 菜单类型
     */
    public enum MenuType {
        /**
         * 目录
         */
        CATALOG(0),
        /**
         * 菜单
         */
        MENU(1),
        /**
         * 按钮
         */
        BUTTON(2);

        private int value;

        MenuType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 定时任务状态
     */
    public enum ScheduleStatus {
        /**
         * 正常
         */
        NORMAL(0),
        /**
         * 暂停
         */
        PAUSE(1);

        private int value;

        ScheduleStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 云服务商
     */
    public enum CloudService {
        /**
         * 七牛云
         */
        QINIU(1),
        /**
         * 阿里云
         */
        ALIYUN(2),
        /**
         * 腾讯云
         */
        QCLOUD(3);

        private int value;

        CloudService(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 资源文件夹
     */
    public static String dataHome;
    public static String bfmsApi;

    @Value("${app.dataHome}")
    public void setDataHome(String dataHome) {
        Constant.dataHome = dataHome;
    }

    @Value("${api.article-api:https://idc.biosino.org/bfms-api/api/article}")
    public void setBfmsApi(String bfmsApi) {
        Constant.bfmsApi = bfmsApi;
    }

    public enum DirectoryEnum {
        // 文件夹
        pdf, temp, project_pdf, doc, attachment, download, script, ai_anno
    }

    public static File getHomeDir(DirectoryEnum dir) {
        File home = new File(Constant.dataHome, dir.name());
        if (!home.exists()) {
            home.mkdirs();
        }
        return home;
    }

    public static File getHomeDir() {
        File home = new File(Constant.dataHome);
        if (!home.exists()) {
            home.mkdirs();
        }
        return home;
    }

    public static File getTempDir(String prefix) {
        File dir;
        if (StrUtil.isBlank(prefix)) {
            dir = FileUtil.file(Constant.dataHome, DirectoryEnum.temp.name(), IdUtil.getSnowflakeNextIdStr());
        } else {
            dir = FileUtil.file(Constant.dataHome, DirectoryEnum.temp.name(), prefix, IdUtil.getSnowflakeNextIdStr());
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    /**
     * 导出任务的目录
     */
    public static File createDownloadDir(String id) {
        File dir = FileUtil.file(Constant.getHomeDir(Constant.DirectoryEnum.download),
                DateUtil.format(new Date(), "yyyy-MM"),
                DateUtil.format(new Date(), "dd"), id);
        FileUtil.mkdir(dir);
        return dir;
    }
}
