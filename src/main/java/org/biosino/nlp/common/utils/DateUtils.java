package org.biosino.nlp.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期处理
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     *
     * @param week 周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return 返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date    日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date  日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date   日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date  日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * 获取当前日期的时分秒MD5
     */
    public static String getMd5Date() {
        LocalDateTime dateTime = LocalDateTime.now(ZoneOffset.UTC);
        String format = DateUtil.format(dateTime, "yyyyMMddHHmm");
        return new MD5().digestHex(format).substring(0, 6);
    }

    /**
     * 计算得到MongoDB存储的日期
     *
     * @param date
     * @return
     */
    public static Date getMongoDate(final Date date) {
        /*Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.HOUR_OF_DAY, 8);
        return ca.getTime();*/
        return date;
    }

    public static String getUpdateDateTime(Date postTimeDate) {
        String result = DateUtil.format(postTimeDate, "yyyy-MM-dd HH:mm");
        // 将日期转换成毫秒值
        long postTimeSecond = postTimeDate.getTime();
        // 上周
        cn.hutool.core.date.DateTime lastWeek = DateUtil.lastWeek();
        // 超过一周直接显示日期
        if (postTimeDate.before(lastWeek)) {
            return result;
        }
        // 当前的时间
        Date now = new Date();
        // 现在日历对象
        Calendar nowC = Calendar.getInstance();
        // 把时间分钟秒设置为0，就是昨天晚上0点的日历
        nowC.set(Calendar.HOUR_OF_DAY, 0);
        nowC.set(Calendar.MINUTE, 0);
        nowC.set(Calendar.SECOND, 0);
        // 把昨天的日历转换成日期
        Date yesterday = nowC.getTime();
        // 前天日历
        nowC.add(Calendar.DAY_OF_MONTH, -1);
        // 把前天的日历转换成日期
        Date beforeYesterday = nowC.getTime();

        // 此时毫秒值
        long nowSecond = now.getTime();
        // 昨天毫秒值
        long ySecondTemp = yesterday.getTime();
        // 前天毫秒值
        long bSecondTemp = beforeYesterday.getTime();
        // 将两个日期转换成毫秒值,之后得到秒差值
        long second = (nowSecond - postTimeSecond) / 1000;
        long ySecond = (nowSecond - ySecondTemp) / 1000;
        long bSecond = (nowSecond - bSecondTemp) / 1000;

        // 调用毫秒差值判断方法
        if (second >= bSecond) {
            result = second / 60 / 60 / 24 + "天前";
        } else if (second >= ySecond) {
            result = "昨天";
        } else if (second >= 0) {
            if (second < 60) {
                result = "刚刚";
            } else if (second / 60 < 30) {
                result = second / 60 + "分钟前";
            } else if (second / 60 < 60) {
                result = "半小时前";
            } else if (second / 60 / 60 < 24) {
                result = second / 60 / 60 + "小时前";
            }
        }
        return result;
    }
}
