package org.biosino.nlp.common.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-01-18 16:18
 */
public class HttpUtils {

    private static final int TIMEOUT = 30 * 1000;

    public static String httpGet(String url) {
        return httpGet(url, TIMEOUT);
    }

    public static String httpGet(String url, Integer timeout) {
        try {
            HttpResponse response = HttpRequest.get(url).timeout(timeout).execute();
            if (response.getStatus() == HttpStatus.HTTP_OK) {
                Resp resp = JSONObject.parseObject(response.body(), Resp.class);
                return resp.getData().toString();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Data
    public static class Resp {
        private Integer code;
        private String msg;
        private Object data;
    }
}
