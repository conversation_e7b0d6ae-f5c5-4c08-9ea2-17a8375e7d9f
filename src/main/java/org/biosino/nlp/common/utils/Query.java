package org.biosino.nlp.common.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.biosino.nlp.common.xss.SQLFilter;
import org.biosino.nlp.modules.project.dto.BaseDTO;

import java.util.Map;

/**
 * 查询参数
 */
public class Query<T> {

    public IPage<T> getPage(BaseDTO dto) {
        return getPage(dto, false);
    }

    public IPage<T> getPage(BaseDTO dto, boolean toUnderline) {
        int curPage = 0;
        int limit = 10;

        if (dto != null && dto.getPage() != null) {
            curPage = dto.getPage();
        }
        if (dto != null && dto.getLimit() != null) {
            limit = dto.getLimit();
        }
        // 分页对象
        Page<T> page = new Page<>(curPage, limit);

        if (dto != null && StrUtil.isNotBlank(dto.getOrderBy())) {

            // 排序字段
            // 防止SQL注入（因为order是通过拼接SQL实现排序的，会有SQL注入风险）
            // String orderField = SQLFilter.sqlInject(dto.getOrderBy());
            String orderField = dto.getOrderBy();
            if (toUnderline) {
                orderField = StrUtil.toUnderlineCase(dto.getOrderBy());
            }
            Boolean orderType = dto.getIsAsc();

            // 前端字段排序
            if (StringUtils.isNotEmpty(orderField) && orderType != null) {
                if (orderType) {
                    return page.addOrder(OrderItem.asc(orderField));
                } else {
                    return page.addOrder(OrderItem.desc(orderField));
                }
            }
        }
        return page;
    }

    public IPage<T> getPage(BaseDTO dto, String defaultOrderField, boolean isAsc) {
        if (dto.getOrderBy() == null || dto.getIsAsc() == null) {
            dto.setOrderBy(defaultOrderField);
            dto.setIsAsc(isAsc);
        }
        return getPage(dto);
    }

    public IPage<T> getPage(BaseDTO dto, String defaultOrderField, boolean isAsc, boolean toUnderline) {
        if (dto.getOrderBy() == null || dto.getIsAsc() == null) {
            dto.setOrderBy(defaultOrderField);
            dto.setIsAsc(isAsc);
        }
        return getPage(dto, toUnderline);
    }

    public IPage<T> getPage(Map<String, Object> params) {
        return this.getPage(params, null, false);
    }

    public IPage<T> getPage(Map<String, Object> params, String defaultOrderField, boolean isAsc) {
        //分页参数
        long curPage = 1;
        long limit = 10;

        if (params.get(Constant.PAGE) != null) {
            curPage = Long.parseLong((String) params.get(Constant.PAGE));
        }
        if (params.get(Constant.LIMIT) != null) {
            limit = Long.parseLong((String) params.get(Constant.LIMIT));
        }

        //分页对象
        Page<T> page = new Page<>(curPage, limit);

        //分页参数
        params.put(Constant.PAGE, page);

        //排序字段
        //防止SQL注入（因为sidx、order是通过拼接SQL实现排序的，会有SQL注入风险）
        String orderField = SQLFilter.sqlInject((String) params.get(Constant.ORDER_FIELD));
        String order = (String) params.get(Constant.ORDER);


        //前端字段排序
        if (StringUtils.isNotEmpty(orderField) && StringUtils.isNotEmpty(order)) {
            if (Constant.ASC.equalsIgnoreCase(order)) {
                return page.addOrder(OrderItem.asc(orderField));
            } else {
                return page.addOrder(OrderItem.desc(orderField));
            }
        }

        //没有排序字段，则不排序
        if (StringUtils.isBlank(defaultOrderField)) {
            return page;
        }

        //默认排序
        if (isAsc) {
            page.addOrder(OrderItem.asc(defaultOrderField));
        } else {
            page.addOrder(OrderItem.desc(defaultOrderField));
        }

        return page;
    }
}
