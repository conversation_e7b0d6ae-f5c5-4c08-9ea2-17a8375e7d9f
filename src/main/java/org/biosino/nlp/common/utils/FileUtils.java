package org.biosino.nlp.common.utils;

import ch.qos.logback.core.encoder.ByteArrayUtil;
import cn.hutool.core.io.FileUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 */
public class FileUtils {

    /**
     * multipartFile转File
     */
    public static File transferToFile(MultipartFile multipartFile) {
        if (multipartFile == null) {
            return null;
        }
        // 选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        File file = null;
        try {
            file = File.createTempFile("Temp_", multipartFile.getOriginalFilename());
            multipartFile.transferTo(file);
            file.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    public static boolean isUTF8(File file) {
        byte[] buf = FileUtil.readBytes(file);
        String content = FileUtil.readString(file, StandardCharsets.UTF_8);
        return ByteArrayUtil.toHexString(buf).equals(ByteArrayUtil.toHexString(content.getBytes(StandardCharsets.UTF_8)));
    }
}
