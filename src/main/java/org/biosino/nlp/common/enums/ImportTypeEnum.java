package org.biosino.nlp.common.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;

import java.util.List;

/**
 * 导入类型枚举类
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Getter
public enum ImportTypeEnum {
    /**
     * 自有文章导入
     */
    article_bnlp(1),
    article_bfms(2),
    pre_anno_entity(3),
    pre_anno_attr(4),
    pre_anno_relation(5),
    verify_anno_entity(6);

    private final int value;

    ImportTypeEnum(int value) {
        this.value = value;
    }


    public static List<Integer> allPreTypeIds() {
        return CollUtil.toList(pre_anno_entity.getValue(), pre_anno_attr.getValue(), pre_anno_relation.getValue());
    }
}
