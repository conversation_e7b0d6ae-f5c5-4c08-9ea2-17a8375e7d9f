package org.biosino.nlp.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 预标注系统 枚举配置
 */
@Getter
public enum PreSourceEnum {
    //
//    MPA("微生物组表型", "http://localhost:8084/bfms/api/preAnnotation/mpa/article", true),
//    KGCOV("新冠知识图谱", "http://localhost:8084/bfms/api/preAnnotation/kgcov/article", false),
    SELF(1, "原文", null, false),
    CUSTOMIZE(2, "预标注", null, false);

    private int id;
    private String title;
    private String api;
    private Boolean pmid;

    PreSourceEnum(int id, String title, String api, Boolean pmid) {
        this.id = id;
        this.title = title;
        this.api = api;
        this.pmid = pmid;
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>(4);
        for (PreSourceEnum statusEnum : PreSourceEnum.values()) {
            map.put(statusEnum.name(), statusEnum.getTitle());
        }
        return map;
    }

    public static Optional<PreSourceEnum> findByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        for (PreSourceEnum statusEnum : PreSourceEnum.values()) {
            if (statusEnum.name().equalsIgnoreCase(name)) {
                return Optional.of(statusEnum);
            }
        }
        return Optional.empty();
    }

    public static Optional<PreSourceEnum> findById(Integer id) {
        if (id == null) {
            return Optional.empty();
        }
        for (PreSourceEnum statusEnum : PreSourceEnum.values()) {
            if (statusEnum.getId() == id) {
                return Optional.of(statusEnum);
            }
        }
        return Optional.empty();
    }

    public static Optional<PreSourceEnum> findByTitle(String title) {
        if (StrUtil.isBlank(title)) {
            return Optional.empty();
        }
        for (PreSourceEnum statusEnum : PreSourceEnum.values()) {
            if (statusEnum.getTitle().equals(title)) {
                return Optional.of(statusEnum);
            }
        }
        return Optional.empty();
    }

    public static Map<String, PreSourceEnum> findAllInMap() {
        Map<String, PreSourceEnum> map = new HashMap<>();
        PreSourceEnum[] values = PreSourceEnum.values();
        for (PreSourceEnum value : values) {
            map.put(value.name(), value);
        }
        return map;
    }
}
