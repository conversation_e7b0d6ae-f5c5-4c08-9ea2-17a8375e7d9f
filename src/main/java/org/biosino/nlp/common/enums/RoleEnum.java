package org.biosino.nlp.common.enums;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020-12-03 10:00:41
 */
public enum RoleEnum {

    /**
     * 角色
     */
    annotator(2L),
    auditor(3L),
    projectAdmin(8L),
    projectWatcher(9L),
    rootAdmin(1L);

    private final long id;

    RoleEnum(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }

    public static Optional<RoleEnum> findById(final Long id) {
        if (id == null) {
            return Optional.empty();
        }
        RoleEnum[] values = RoleEnum.values();
        for (RoleEnum value : values) {
            if (id.equals(value.getId())) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }
}
