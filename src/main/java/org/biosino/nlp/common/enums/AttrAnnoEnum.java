package org.biosino.nlp.common.enums;

import lombok.Getter;

import java.util.Optional;

/**
 * 是否为属性标注
 */
@Getter
public enum AttrAnnoEnum {
    /**
     * 实体标注
     */
    not_attr(0),
    /**
     * 属性标注
     */
    is_attr(1);
    private int code;

    AttrAnnoEnum(int code) {
        this.code = code;
    }

    public static Optional<AttrAnnoEnum> findByCode(Integer code) {
        if (code == null) {
            return Optional.empty();
        }
        for (AttrAnnoEnum e : AttrAnnoEnum.values()) {
            if (e.getCode() == code) {
                return Optional.of(e);
            }
        }
        return Optional.empty();
    }
}
