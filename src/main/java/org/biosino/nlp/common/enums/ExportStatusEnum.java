package org.biosino.nlp.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@Getter
public enum ExportStatusEnum {
    /**
     * 导出失败
     */
    fail(-1),
    /**
     * 待导出
     */
    wait(0),
    /**
     * 导出中
     */
    processing(1),
    /**
     * 导出结束
     */
    finish(2);

    private final int code;

    ExportStatusEnum(int code) {
        this.code = code;
    }
}
