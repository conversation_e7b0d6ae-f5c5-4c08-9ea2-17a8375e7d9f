package org.biosino.nlp.common.enums;

/**
 * 批次文章导入状态
 *
 * <AUTHOR>
 * @date 2020-12-15 09:17
 */
public enum ImportStatusEnum {

    /**
     * 导入失败
     */
    fail(-1),
    /**
     * 待导入
     */
    wait(0),
    /**
     * 导入中
     */
    processing(1),
    /**
     * 导入结束
     */
    finish(2),
    /**
     * 暂停
     */
    pause(3);

    private final int value;

    ImportStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
