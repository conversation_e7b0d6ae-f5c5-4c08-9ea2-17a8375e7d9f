package org.biosino.nlp.common.enums;

import java.util.Optional;

/**
 * 消岐来源
 *
 * <AUTHOR>
 * @date 2020-12-3 10:49
 */
public enum ConceptTypeEnum {

    /**
     *
     */
    self(1),
    umls(2);

    private final int value;

    ConceptTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static Optional<ConceptTypeEnum> fromValue(Integer value) {
        if (value == null) {
            return Optional.empty();
        }
        for (ConceptTypeEnum e : ConceptTypeEnum.values()) {
            if (e.value == value) {
                return Optional.of(e);
            }
        }
        return Optional.empty();
    }
}
