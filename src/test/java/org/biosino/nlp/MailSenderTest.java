package org.biosino.nlp;

import org.biosino.nlp.modules.project.service.DataExportService;
import org.biosino.nlp.modules.project.service.MailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@SpringBootTest
public class MailSenderTest {
    @Autowired
    private MailService mailService;

    @Autowired
    private DataExportService dataExportService;

    @Test
    public void test1() {
        mailService.sendSimpleMail("<EMAIL>", "文件下载", "哈哈哈");
    }

    @Test
    public void test2() {
        dataExportService.sendDownloadMail("385ba0cb51efcc8009e113c4bab052c8");
    }
}
