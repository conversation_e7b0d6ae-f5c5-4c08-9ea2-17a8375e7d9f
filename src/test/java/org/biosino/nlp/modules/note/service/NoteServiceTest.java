package org.biosino.nlp.modules.note.service;

import org.biosino.nlp.modules.note.dao.NoteDao;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2021-01-05 14:54
 */
@SpringBootTest
class NoteServiceTest {

    @Autowired
    private NoteDao noteDao;

    @Test
    void avgAnnoTime() {
        Object time = noteDao.avgAnnotatorTime(8L, 9L);
        System.out.println(time);
    }

}
