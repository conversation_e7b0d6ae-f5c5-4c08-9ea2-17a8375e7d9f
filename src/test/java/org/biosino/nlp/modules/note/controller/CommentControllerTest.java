package org.biosino.nlp.modules.note.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.biosino.nlp.modules.note.dto.QuestionDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Comment Controller Test
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@SpringBootTest
@AutoConfigureTestMvc
public class CommentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testSubmitQuestion() throws Exception {
        QuestionDTO questionDTO = new QuestionDTO();
        questionDTO.setProjectId(1L);
        questionDTO.setBatchId(1L);
        questionDTO.setNoteId(1L);
        questionDTO.setTaskId(1L);
        questionDTO.setQuestionText("这是一个测试提问");
        questionDTO.setViolateRule("规则1");
        questionDTO.setEntityId("test-entity-id");
        questionDTO.setIsAttributeQuestion(false);
        questionDTO.setCurrentUniqueId("test-unique-id");

        mockMvc.perform(post("/comment/submitQuestion")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(questionDTO)))
                .andExpect(status().isOk());
    }

    /**
     * 测试实体和属性关联逻辑的说明：
     *
     * 数据存储逻辑：
     * 1. entityIds: 存储m_entity表中的记录ID（用户标注的实体）
     * 2. attributeIds: 存储m_attributes表中的记录ID（实体和属性的关联关系）
     *
     * 查找逻辑：
     * 1. 通过entity_id到m_attributes找出所有关联记录 -> 存储到attributeIds
     * 2. 通过这些m_attributes记录的attribute_id，到m_entity表中查找对应的实体记录 -> 存储到entityIds
     *
     * 例如：
     * - 用户提问实体A（entity_id = "entity_001"）
     * - 查找m_attributes表中entity_id = "entity_001"的所有记录：[attr_record_1, attr_record_2]
     * - attributeIds = ["attr_record_1", "attr_record_2"]
     * - 从这些记录中获取attribute_id：["attr_001", "attr_002"]
     * - 查找m_entity表中id为"attr_001"或"attr_002"的所有记录
     * - entityIds = ["entity_001", "attr_001", "attr_002"] (原始实体ID + 属性实体ID)
     */
}
