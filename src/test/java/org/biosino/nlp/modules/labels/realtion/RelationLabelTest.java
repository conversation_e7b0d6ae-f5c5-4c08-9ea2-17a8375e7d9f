package org.biosino.nlp.modules.labels.realtion;

import org.biosino.nlp.modules.labels.entity.RelationLabel;
import org.biosino.nlp.modules.labels.service.RelationLabelService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

/**
 * <AUTHOR> @date 2020/12/22 9:33
 */
@SpringBootTest
public class RelationLabelTest {
    @Autowired
    RelationLabelService relationLabelService;
    private final Date nowDate = new Date();
    ;

    @Test
    public void fakeData() {
        for (int i = 0; i < 100; i++) {
            RelationLabel relationLabel = new RelationLabel();
            relationLabel.setSpecial((long) i);
            relationLabel.setName("name" + i);
            relationLabel.setDescription("description" + i);
            relationLabel.setProjectId(0L);
            relationLabel.setStatus(1);
            relationLabel.setDeleted(0);
            relationLabel.setCreateTime(nowDate);
            relationLabel.setUserId(10L);
            relationLabelService.save(relationLabel);
        }
    }
}
