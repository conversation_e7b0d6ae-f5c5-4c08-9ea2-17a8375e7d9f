package org.biosino.nlp.modules.labels.service.impl;

import org.assertj.core.util.Maps;
import org.biosino.nlp.modules.labels.entity.EntityLabel;
import org.biosino.nlp.modules.labels.service.EntityLabelService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> @date 2020/12/10 21:12
 */
@SpringBootTest
class EntityLabelServiceImplTest {


    @Autowired
    private EntityLabelService entityLabelService;

//    @Test
//    void queryPage() {
//        long l = System.currentTimeMillis();
//        for (int i = 0; i < 100; i++) {
//            entityLabelService.queryPage(Maps.newHashMap("dataForm", "{'code':'0'}"));
//        }
//        System.out.println((System.currentTimeMillis() - l) / 1000.00);
//    }

    @Test
    void findAll(){
        /*for (EntityLabel entityLabel : entityLabelService.findAll()) {
            System.out.println(entityLabel.toString());
        }*/

    }
}
