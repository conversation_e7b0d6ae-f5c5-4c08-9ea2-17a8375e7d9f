package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.BatchVo;
import org.biosino.nlp.modules.project.entity.Batch;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-30 11:50
 */
class BatchMapperTest {

    @Test
    void t1() {
        Batch batch = new Batch();
        batch.setName("222");
        List<Batch> batches = Arrays.asList(new Batch(), batch);
        BatchVo vo = BatchMapper.INSTANCE.copy(batch);
        System.out.println(vo);

        for (BatchVo item : BatchMapper.INSTANCE.copy(batches)) {
            System.out.println(item);
        }

    }

    @Test
    void t2() {
        List<Batch> batches = null;
        List<BatchVo> list = BatchMapper.INSTANCE.copy(batches);
        System.out.println(list);
    }
}
