package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.service.ApiService;
import org.biosino.nlp.modules.api.vo.BatchVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-30 12:26
 */
@SpringBootTest
class ApiServiceTest {

    @Autowired
    private ApiService apiService;

    @Test
    void findAllBatchInfo() {
        List<BatchVo> list = apiService.findAllBatchInfo("f69c3c8b6be84ddc88e08f362dbd0226");
        list.forEach(it -> System.out.println(it));
    }
}
