package org.biosino.nlp.modules.api.mapper;

import org.biosino.nlp.modules.api.vo.EntityDataVo;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2020-12-30 14:50
 */
class EntityMapperTest {

    @Test
    void copy() {
        Entity entity = new Entity();
        entity.setArticleId("poop");
        EntityDataVo copy = EntityMapper.INSTANCE.copy(entity);
        System.out.println(copy);
    }
}
