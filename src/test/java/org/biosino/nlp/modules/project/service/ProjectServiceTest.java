package org.biosino.nlp.modules.project.service;

import org.biosino.nlp.modules.project.entity.Project;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-07 15:49
 */
@SpringBootTest
class ProjectServiceTest {

    @Autowired
    private ProjectService projectService;

    @Test
    void t1() {
        List<Project> projects = projectService.participateProject(5L, 2L);
        for (Project project : projects) {
            System.out.println(project);
        }
    }

}
