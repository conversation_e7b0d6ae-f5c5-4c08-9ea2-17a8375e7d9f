package org.biosino.nlp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.biosino.nlp.common.enums.RoleEnum;
import org.biosino.nlp.modules.note.dto.AssignTaskDTO;
import org.biosino.nlp.modules.note.dto.TaskDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.enums.NoteStepEnum;
import org.biosino.nlp.modules.note.service.NoteTaskService;
import org.biosino.nlp.modules.note.vo.TaskListVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class TaskTest {
    @Autowired
    private NoteTaskService noteTaskService;

    @Test
    public void queryAnnotatorTaskList() {
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setPage(1);
        taskDTO.setLimit(10);
        taskDTO.setOrderBy("noteId");
        taskDTO.setIsAsc(false);

        taskDTO.setBatchId(1L);
        taskDTO.setRoleId(RoleEnum.annotator.getId());
        taskDTO.setActiveStep("all");
        taskDTO.setActiveStep(NoteStepEnum.marked.name());
        taskDTO.setUserId(2L);
//        taskDTO.setArticleId("21");
//        taskDTO.setArticleName("VE");
        /*IPage<TaskListVO> page = noteTaskService.queryTaskList(taskDTO);
        List<TaskListVO> records = page.getRecords();
        for (TaskListVO record : records) {
            System.out.println(record.toString());
        }*/
    }

    @Test
    public void queryAuditorTaskList() {
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setPage(1);
        taskDTO.setLimit(10);
//        taskDTO.setOrderBy("articleName");
//        taskDTO.setIsAsc(false);

        taskDTO.setBatchId(1L);
        taskDTO.setRoleId(RoleEnum.auditor.getId());
//        taskDTO.setActiveStep("all");
        taskDTO.setActiveStep(NoteStepEnum.marked.name());
        taskDTO.setUserId(7L);
        taskDTO.setAnnotatorId(2L);
//        taskDTO.setArticleId("21");
//        taskDTO.setArticleName("VE");
        /*IPage<TaskListVO> page = noteTaskService.queryTaskList(taskDTO);
        List<TaskListVO> records = page.getRecords();
        for (TaskListVO record : records) {
            System.out.println(record.toString());
        }*/
    }

    @Test
    public void assignAnnotatorTask1() {
        AssignTaskDTO dto = new AssignTaskDTO();
        dto.setBatchId(2L);
        dto.setUserId(2L);
        dto.setRoleId(2L);
        NoteTask noteTask = noteTaskService.assignAnnotatorTask(dto);
        System.out.println(noteTask);
        System.out.println("+++++++++++++++++++++++++++++++++++++");
        System.out.println(noteTask.getTaskId());
    }

    @Test
    public void assignAnnotatorTask2() {
        AssignTaskDTO dto = new AssignTaskDTO();
        dto.setBatchId(2L);
        dto.setUserId(4L);
        dto.setRoleId(2L);
        NoteTask noteTask = noteTaskService.assignAnnotatorTask(dto);
        System.out.println(noteTask);
        System.out.println("+++++++++++++++++++++++++++++++++++++");
        System.out.println(noteTask.getTaskId());
    }

    @Test
    public void assignAuditorTask() {
        AssignTaskDTO dto = new AssignTaskDTO();
        dto.setBatchId(1L);
        dto.setUserId(54L);
        dto.setRoleId(3L);
        dto.setAnnotatorId(2L);
        NoteTask noteTask = noteTaskService.assignAuditorTask(dto);
        System.out.println(noteTask);
        System.out.println("+++++++++++++++++++++++++++++++++++++");
        System.out.println(noteTask.getTaskId());
    }

    @Test
    public void submitAnnotatorTask() {
        Long b = 11205L;
        noteTaskService.submitAnnotatorTask(b, false);
    }

    @Test
    public void submitAuditorTask() {
        Long b = 11205L;
        noteTaskService.submitAuditorTask(b);
    }
}
