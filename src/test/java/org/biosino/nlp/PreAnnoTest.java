package org.biosino.nlp;

import cn.hutool.core.util.IdUtil;
import org.biosino.nlp.common.enums.AttrAnnoEnum;
import org.biosino.nlp.common.enums.PreSourceEnum;
import org.biosino.nlp.modules.api.mapper.AttributeEntityMapper;
import org.biosino.nlp.modules.api.mapper.EntityMapper;
import org.biosino.nlp.modules.api.mapper.RelationMapper;
import org.biosino.nlp.modules.note.dao.NoteTaskDao;
import org.biosino.nlp.modules.note.dao.mongo.AttributesRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dao.mongo.RelationshipRepository;
import org.biosino.nlp.modules.note.dto.AnnoDTO;
import org.biosino.nlp.modules.note.entity.NoteTask;
import org.biosino.nlp.modules.note.entity.mongo.Attributes;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.biosino.nlp.modules.note.service.RelationshipService;
import org.biosino.nlp.modules.note.service.impl.NoteServiceImpl;
import org.biosino.nlp.modules.note.service.impl.RelationshipServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Li
 * @date 2023/5/19
 */
@SpringBootTest
public class PreAnnoTest {
    @Autowired
    private DocumentService documentService;
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private AttributesRepository attributesRepository;
    @Autowired
    private RelationshipRepository relationshipRepository;
    @Autowired
    private RelationshipService relationshipService;

    @Autowired
    private NoteTaskDao noteTaskDao;

    @Test
    public void t1() {
        Document document = documentService.getDocumentById("dg-jrss:0000045SELF1");
        loadPreRelation(document, 144L, 337L, null);
    }


    public void loadPreRelation(Document document, Long taskId, Long noteId, Long userId) {
        Date now = new Date();
        NoteTask noteTask = noteTaskDao.selectById(taskId);
        // 如果是 1 证明已经导入过直接return
        if (noteTask.getPreRelationImported() == 1) {
            return;
        }
        Long projectId = noteTask.getProjectId();
        Long batchId = noteTask.getBatchId();
        String articleId = document.getArticleId();

        ArrayList<Entity> entityList = new ArrayList<>();
        ArrayList<Attributes> attributesList = new ArrayList<>();
        ArrayList<Relationship> relationList = new ArrayList<>();


        List<Relationship> relationshipList = relationshipRepository.findPreAnnoRelation(noteId);
        // 提取出所有涉及实体id
        Set<String> entitySet = relationshipList.stream()
                .flatMap(r -> r.getItems().stream())
                .flatMap(item -> Stream.of(item.getSubject(), item.getObjects()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // 加载所有实体的属性标注
        List<Attributes> attributes = attributesRepository.findAllEntityIdIn(entitySet);
        // 对属性标注根据entityId进行分组
        Map<String, List<Attributes>> entityAttrMap = attributes.stream()
                .collect(Collectors.groupingBy(Attributes::getEntityId));
        //  提取划词属性
        List<String> attrEntityIds = attributes.stream().map(Attributes::getAttributeId).collect(Collectors.toList());
        // 查询所有的划词属性实体
        List<Entity> attrEntityList = entityRepository.findAllByIdInAndDeleted(attrEntityIds, false);
        Map<String, Entity> attrEntityMap = attrEntityList.stream().collect(Collectors.toMap(Entity::getId, x -> x));
        // 查询所有的关系实体
        Map<String, Entity> entityIdMap = entityRepository.findAllByIdInAndDeleted(entitySet, false).stream()
                .collect(Collectors.toMap(Entity::getId, x -> x));

        for (Relationship relationship : relationshipList) {
            Relationship data = RelationMapper.INSTANCE.copyBean(relationship);
            // 重新设置一个id
            data.setId(IdUtil.simpleUUID());
            data.setTaskId(taskId);
            data.setSource(PreSourceEnum.SELF.getId());
            data.setCreateTime(now);
            data.setImportLogId(null);
            data.setAnnotatorId(userId);

            ArrayList<Relationship.RelationItem> newItems = new ArrayList<>();
            for (Relationship.RelationItem item : relationship.getItems()) {
                Relationship.RelationItem newItem = new Relationship.RelationItem();

                newItem.setOrder(item.getOrder());
                newItem.setNegation(item.getNegation());
                newItem.setAnnotations(item.getAnnotations());
                newItem.setRelation(item.getRelation());

                List<String> subject = item.getSubject();
                boolean allSubjectIsEntity = subject.stream().allMatch(s -> s.length() == 32);
                if (allSubjectIsEntity) {
                    List<String> newSubject = subject.stream().map(x -> {
                        // 把每一个实体重新导入
                        String entityId = IdUtil.simpleUUID();
                        Entity entity = EntityMapper.INSTANCE.copyBean(entityIdMap.get(x));
                        entity.setId(entityId);
                        entity.setTaskId(taskId);
                        entity.setCreateTime(now);
                        entity.setSource(PreSourceEnum.SELF.getId());
                        entity.setImportLogId(null);
                        entity.setAnnotatorId(userId);
                        List<AnnoDTO.EntityInfo> collect = entity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                        entity.setEntityInfos(collect);
                        String md5 = NoteServiceImpl.genEntityStrMd5(collect, entity.getLabelId(), AttrAnnoEnum.not_attr.getCode());
                        entity.setMd5(md5);

                        // 找到这个实体下面的属性
                        if (entityAttrMap.containsKey(x)) {
                            List<Attributes> list = entityAttrMap.get(x);
                            // 构建新的属性
                            for (Attributes attr : list) {
                                Attributes newAttr = AttributeEntityMapper.INSTANCE.copyBean(attr);
                                newAttr.setId(IdUtil.simpleUUID());
                                newAttr.setEntityId(entityId);
                                newAttr.setTaskId(taskId);
                                if (attr.getAttributeId() != null) {
                                    Entity attrEntity = attrEntityMap.get(attr.getAttributeId());
                                    Entity newAttrEntity = EntityMapper.INSTANCE.copyBean(attrEntity);
                                    String newAttrEntityId = IdUtil.simpleUUID();

                                    newAttrEntity.setId(newAttrEntityId);
                                    newAttrEntity.setCreateTime(now);
                                    newAttrEntity.setSource(PreSourceEnum.SELF.getId());
                                    newAttrEntity.setImportLogId(null);
                                    newAttrEntity.setTaskId(taskId);
                                    newAttrEntity.setAnnotatorId(userId);
                                    List<AnnoDTO.EntityInfo> newAttrEntityInfo = attrEntity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                                    newAttrEntity.setEntityInfos(newAttrEntityInfo);
                                    newAttrEntity.setMd5(NoteServiceImpl.genEntityStrMd5(newAttrEntityInfo, null, AttrAnnoEnum.is_attr.getCode()));
                                    newAttr.setAttributeId(newAttrEntityId);
                                    entityList.add(newAttrEntity);
                                }
                                attributesList.add(newAttr);
                            }
                        }

                        // 添加新的实体
                        entityList.add(entity);
                        return entityId;
                    }).collect(Collectors.toList());
                    newItem.setSubject(newSubject);
                } else {
                    newItem.setSubject(item.getSubject());
                }
                List<String> objects = item.getObjects();
                boolean allObjectsIsEntity = objects.stream().allMatch(s -> s.length() == 32);
                if (allObjectsIsEntity) {
                    List<String> newObject = objects.stream().map(x -> {
                        // 把每一个实体重新导入
                        String entityId = IdUtil.simpleUUID();
                        Entity entity = EntityMapper.INSTANCE.copyBean(entityIdMap.get(x));
                        entity.setId(entityId);
                        entity.setTaskId(taskId);
                        entity.setCreateTime(now);
                        entity.setSource(PreSourceEnum.SELF.getId());
                        entity.setImportLogId(null);
                        entity.setAnnotatorId(userId);
                        List<AnnoDTO.EntityInfo> collect = entity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                        entity.setEntityInfos(collect);
                        String md5 = NoteServiceImpl.genEntityStrMd5(collect, entity.getLabelId(), AttrAnnoEnum.not_attr.getCode());
                        entity.setMd5(md5);

                        // 找到这个实体下面的属性
                        if (entityAttrMap.containsKey(x)) {
                            List<Attributes> list = entityAttrMap.get(x);
                            // 构建新的属性
                            for (Attributes attr : list) {
                                Attributes newAttr = AttributeEntityMapper.INSTANCE.copyBean(attr);
                                newAttr.setId(IdUtil.simpleUUID());
                                newAttr.setEntityId(entityId);
                                newAttr.setTaskId(taskId);
                                if (attr.getAttributeId() != null) {
                                    Entity attrEntity = attrEntityMap.get(attr.getAttributeId());
                                    Entity newAttrEntity = EntityMapper.INSTANCE.copyBean(attrEntity);
                                    String newAttrEntityId = IdUtil.simpleUUID();

                                    newAttrEntity.setId(IdUtil.simpleUUID());
                                    newAttrEntity.setCreateTime(now);
                                    newAttrEntity.setSource(PreSourceEnum.SELF.getId());
                                    newAttrEntity.setImportLogId(null);
                                    newAttrEntity.setTaskId(taskId);
                                    newAttrEntity.setAnnotatorId(userId);
                                    List<AnnoDTO.EntityInfo> newAttrEntityInfo = attrEntity.getEntityInfos().stream().peek(y -> y.setUniqueid(IdUtil.simpleUUID())).collect(Collectors.toList());
                                    newAttrEntity.setEntityInfos(newAttrEntityInfo);
                                    newAttrEntity.setMd5(NoteServiceImpl.genEntityStrMd5(newAttrEntityInfo, null, AttrAnnoEnum.is_attr.getCode()));
                                    newAttr.setAttributeId(newAttrEntityId);
                                    entityList.add(newAttrEntity);
                                }
                                attributesList.add(newAttr);
                            }
                        }

                        // 添加新的实体
                        entityList.add(entity);
                        return entityId;
                    }).collect(Collectors.toList());
                    newItem.setObjects(newObject);
                } else {
                    newItem.setObjects(item.getObjects());
                }
                newItems.add(newItem);


            }
            String md5 = RelationshipServiceImpl.genRelationStrMd5(newItems, data.getPatternId(), entityList);
            boolean exists = relationshipRepository.existsByTaskIdAndNoteIdAndAnnotatorIdAndAuditorIdAndSourceAndMd5AndDeleted(taskId, noteId, userId, null, PreSourceEnum.SELF.getId(), md5, false);
            if (exists) {
                continue;
            }
            data.setMd5(md5);
            data.setAnnotatorId(userId);
            data.setItems(newItems);
            relationList.add(data);
        }
        attributesRepository.saveAll(attributesList);
        entityRepository.saveAll(entityList);
        relationshipRepository.saveAll(relationList);
        noteTask.setPreRelationImported(1);
    }
}
