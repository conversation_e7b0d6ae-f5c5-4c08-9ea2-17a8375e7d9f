package org.biosino.nlp;

import com.alibaba.fastjson.JSON;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.biosino.nlp.modules.note.dto.Graph;
import org.biosino.nlp.modules.note.entity.mongo.Relationship;
import org.biosino.nlp.modules.note.service.RelationshipMapService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

/**
 * 构建关系图
 */
@SpringBootTest
public class RelationMapTest {

    @Autowired
    private RelationshipMapService relationshipMapService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    void main() {
        Long noteId = 6L;
        Graph data = relationshipMapService.getData(noteId, null, null, null);
        System.out.println(data);
    }

    @Test
    void name() {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("note_id").is(0).and("project_id").is(1)),
                Aggregation.group("group").count().as("条数"));
        AggregationResults<BasicDBObject> pt =
                mongoTemplate.aggregate(aggregation, Relationship.class, BasicDBObject.class);

        for (DBObject obj : pt) {
            System.out.println(JSON.toJSONString(obj));
        }
    }
}
