package org.biosino.nlp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.biosino.nlp.common.enums.DeleteEnum;
import org.biosino.nlp.modules.api.service.TestService;
import org.biosino.nlp.modules.note.dao.mongo.EntityCustomRepository;
import org.biosino.nlp.modules.note.dao.mongo.EntityRepository;
import org.biosino.nlp.modules.note.dto.AnnotationsDTO;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.project.entity.mongo.PreAnnotation;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class MongoDBTest {

    private static final String ANNOTATION = "annotations";
    private static final String ID = "id";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private TestService testService;

//    @Autowired
//    private EntityCustomRepository entityCustomRepository;

    @Autowired
    private EntityRepository entityRepository;

    /**
     * 测试回填article功能
     */
    @Test
    void name() throws IOException {
        JSONObject jsonObject = testService.readJson("test_out.json");
        Document documentMongo = JSONObject.toJavaObject(jsonObject, Document.class);

        Map<String, List<AnnotationsDTO>> map = new HashMap<>();
        List<AnnotationsDTO> annotationsDTOS = JSONArray.parseArray("[\n" +
                "                    {\n" +
                "                        \"start\": 12,\n" +
                "                        \"end\": 132,\n" +
                "                        \"label\": 5\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"start\": 140,\n" +
                "                        \"end\": 169,\n" +
                "                        \"label\": 2\n" +
                "                    }\n" +
                "                ]", AnnotationsDTO.class);
        map.put("3QJ", annotationsDTOS);
        map.put("3aJ", annotationsDTOS);
        map.put("MRH", annotationsDTOS);
        fillAnnotation(documentMongo, map);
        System.out.println(documentMongo);
    }

    private static final List<String> ignoreFields =
            CollUtil.newArrayList("cls", "title", "content", "name", "src",
                    "formats", "rowspan");

    /**
     * 插入json数据到数据库
     */
    @Test
    void nameMongoDB() throws IOException {
        JSONObject jsonObject = testService.readJson("test_out_2.json");
        Document documentMongo = JSONObject.toJavaObject(jsonObject, Document.class);
        mongoTemplate.save(documentMongo);
    }

    void fillAnnotation(Object obj, Map<String, List<AnnotationsDTO>> map) {
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不能标注
                if (ignoreFields.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);

                // 找到对应的ID则设置值
                if (ID.equals(name) && map.containsKey(value.toString())) {
                    ReflectUtil.setFieldValue(currentObj, ANNOTATION, map.get(value.toString()));
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                if (value != null) {
                    list.add(value);
                }
            }
            if (list.isEmpty()) {
                break;
            }
        }
    }

    // 递归方式（舍弃，可能栈溢出）
    private void test(Object obj, Map<String, List<AnnotationsDTO>> map) {
        if (obj == null || map == null) {
            return;
        }
        Field[] fields = ReflectUtil.getFieldsDirectly(obj.getClass(), false);

        for (Field field : fields) {
            String name = field.getName();
            if (ignoreFields.contains(name)) {
                continue;
            }
            Object fieldValue = ReflectUtil.getFieldValue(obj, field);
            if (name.equals("id") && map.containsKey(fieldValue.toString())) {
                ReflectUtil.setFieldValue(obj, "annotations", map.get(fieldValue.toString()));
                return;
            } else if (fieldValue instanceof String || fieldValue instanceof Long || fieldValue instanceof Integer || fieldValue instanceof Document.TitleDTO) {
                // continue;
            } else if (fieldValue instanceof Collection) {
                for (Object object : (Collection<?>) fieldValue) {
                    test(object, map);
                }
            } else {
                test(fieldValue, map);
            }
        }
    }

    @Test
    public void t1() {
        System.out.println(entityRepository.findSubLabelByID(172L));
        System.out.println(entityRepository.findSubLabelByID(111111L));
    }

    @Test
    public void t2() {
        System.out.println(entityRepository.existsByLabelIdAndDeleted(146L, DeleteEnum.not_delete.getVal()));
        System.out.println(entityRepository.existsByLabelIdAndDeleted(111111L, DeleteEnum.not_delete.getVal()));
    }

    /*@Test
    public void t3() {
        PreAnnotation preAnnotation = new PreAnnotation();
        preAnnotation.setAnnotate("测试批注信息而已1");
        preAnnotation.setProjectId(4L);
        preAnnotation.setBatchId(9L);
        preAnnotation.setNoteId(108L);
        preAnnotation.setArticleId("29584707");
        preAnnotation.setTextId("d5b");
        preAnnotation.setStart(278);
        preAnnotation.setEnd(298);
        preAnnotation.setText("have revealed much r");
        preAnnotation.setLabel("ANTN");
//        preAnnotation.setSource(PreSourceEnum.MPA.name());
        preAnnotation.setId(getPreEntityId(preAnnotation));
        mongoTemplate.save(preAnnotation);

        PreAnnotation preAnnotation2 = new PreAnnotation();
        preAnnotation2.setAnnotate("测试批注信息而已2");
        preAnnotation2.setProjectId(4L);
        preAnnotation2.setBatchId(9L);
        preAnnotation2.setNoteId(108L);
        preAnnotation2.setArticleId("29584707");
        preAnnotation2.setTextId("d5b");
        preAnnotation2.setStart(439);
        preAnnotation2.setEnd(448);
        preAnnotation2.setText("mechanism");
        preAnnotation2.setLabel("META");
//        preAnnotation2.setSource(PreSourceEnum.MPA.name());
        preAnnotation2.setId(getPreEntityId(preAnnotation2));
        mongoTemplate.save(preAnnotation2);

        PreAnnotation preAnnotation3 = new PreAnnotation();
        preAnnotation3.setAnnotate("测试批注信息而已3");
        preAnnotation3.setProjectId(4L);
        preAnnotation3.setBatchId(9L);
        preAnnotation3.setNoteId(108L);
        preAnnotation3.setArticleId("29584707");
        preAnnotation3.setTextId("fdd");
        preAnnotation3.setStart(476);
        preAnnotation3.setEnd(481);
        preAnnotation3.setText("prot");
        preAnnotation3.setLabel("GOOD");
//        preAnnotation3.setSource(PreSourceEnum.MPA.name());
        preAnnotation3.setId(getPreEntityId(preAnnotation3));
        mongoTemplate.save(preAnnotation3);

    }*/

    private String getPreEntityId(PreAnnotation annoDTO) {
        return annoDTO.getNoteId() + "_" + annoDTO.getTextId() + "_" + annoDTO.getStart() + "_" + annoDTO.getEnd() + "_" + annoDTO.getSource();
    }

}
