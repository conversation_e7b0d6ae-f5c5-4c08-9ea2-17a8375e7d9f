package org.biosino.nlp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.Data;
import org.biosino.nlp.modules.note.entity.Note;
import org.biosino.nlp.modules.note.entity.mongo.Document;
import org.biosino.nlp.modules.note.entity.mongo.Entity;
import org.biosino.nlp.modules.note.service.DocumentService;
import org.biosino.nlp.modules.note.service.NoteService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 批量标注测试代码
 */
@SpringBootTest
public class BatchAnnotateTest {

    private static final String CONTENT = "content";
    private static final List<String> IGNORE_FIELDS =
            CollUtil.newArrayList("cls", "title", "id", "name", "src",
                    "formats", "rowspan");

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private NoteService noteService;

    @Test
    void name() {
        /*Long noteId = 224L;
        // 一级标签
        Long labelId = 901L;

        Note note = noteService.getById(noteId);

        // 查询出当前标注的所有数据
        Query query = Query.query(Criteria.where("note_id").is(noteId));
        query.fields().include("_id");
        List<String> ids = mongoTemplate.findDistinct(query, "_id", Entity.class, String.class);
        Set<String> idSet = new HashSet<>(ids);

        Document document = documentService.getDocumentById("23409136XML2");
        List<Annotate> annotateList = getAnnotation(document, "repeated");

        List<Entity> entityList = new ArrayList<>();
        for (Annotate annotate : annotateList) {
            String tempId = noteId + "_" + annotate.getTextId() + "_" + annotate.getStart() + "_" + annotate.getEnd();
            if (idSet.contains(tempId)) {
                continue;
            }
            Entity entity = new Entity();
            entity.setId(tempId);
            entity.setProjectId(note.getProjectId());
            entity.setBatchId(note.getBatchId());
            entity.setNoteId(noteId);
            entity.setArticleId(note.getArticleId());
            entity.setTextId(annotate.getTextId());
            entity.setText(annotate.getText());
            entity.setStart(annotate.getStart());
            entity.setEnd(annotate.getEnd());
            entity.setLabelId(labelId);
            // todo 改USER
            entity.setUserId(note.getAnnotator());
            entity.setCreateTime(new Date());
            entityList.add(entity);
        }
        mongoTemplate.insertAll(entityList);
        // 记录日志
        System.out.println(annotateList);*/
    }

    private List<Annotate> getAnnotation(Object obj, String text) {
        List<Annotate> annotateList = new ArrayList<>();
        // 创建一个总集合，用于临时存放待遍历数据
        List<Object> list = CollUtil.newArrayList(obj);

        while (CollUtil.isNotEmpty(list)) {
            // 删除第一个元素，并且返回该元素
            Object currentObj = list.remove(0);

            // 处理tables表格List<List<Object>>情况
            if (currentObj instanceof Collection) {
                list.addAll((Collection<?>) currentObj);
                continue;
            }

            // 取出当前临时对象所有属性域
            Field[] fields = ReflectUtil.getFieldsDirectly(currentObj.getClass(), false);

            for (Field field : fields) {
                String name = field.getName();
                // 在这里集合中的字段直接跳过，因为这些不是标注文本
                if (IGNORE_FIELDS.contains(name)) {
                    continue;
                }
                // 获取当前属性值
                Object value = ReflectUtil.getFieldValue(currentObj, field);
                if (value == null) {
                    continue;
                }
                // 找到对应的ID则设置值
                if (CONTENT.equals(name)) {
                    String textId = (String) ReflectUtil.getFieldValue(currentObj, "id");
                    System.out.println("textId：" + textId + " ++++++++++++++++++++++++++++++++++++++");
                    System.out.println(value);
                    List<Annotate> annotates = createAnnotate(textId, text, value.toString());
                    if (CollUtil.isNotEmpty(annotates)) {
                        annotateList.addAll(annotates);
                    }
                    continue;
                }
                // 忽略这些类型的字段
                if (value instanceof String || value instanceof Long || value instanceof Integer) {
                    continue;
                }
                // 如果是集合，则放入总集合继续遍历
                if (value instanceof Collection && CollUtil.isNotEmpty((Collection<?>) value)) {
                    list.addAll((Collection<?>) value);
                    continue;
                }
                // 放入自定义对象
                list.add(value);
            }
            if (list.isEmpty()) {
                break;
            }
        }
        return annotateList;
    }

    private List<Annotate> createAnnotate(String textId, String text, String content) {
        List<Annotate> annotateList = new ArrayList<>();

        //  定义该变量用于记录匹配"love"的元素前面的长度
        int frontLength = 0;

        int textLength = text.length();

        //只要该str字符串中有匹配"love"的元素，才进行以下操作
        while (content.contains(text)) {

            //定义该变量用于记录匹配"love"的元素在当前字符串的位置
            int index = content.indexOf(text);

            //匹配"love"的元素位置等于frontLength加上index；加1为了从1开始计数，更加直观：
            int start = index + frontLength;
            int end = index + frontLength + textLength;
            Annotate annotate = new Annotate();
            annotate.setTextId(textId);
            annotate.setText(text);
            annotate.setStart(start);
            annotate.setEnd(end);
            annotateList.add(annotate);
            System.out.println("Start: " + start + "; End: " + end);

            frontLength += (index + textLength);
            //将字符串中匹配"love"元素的前面部分及其本身截取，留下后面的部分
            content = content.substring(index + textLength);
        }
        return annotateList;
    }


    @Data
    private static class Annotate {
        private String textId;
        private String text;
        private Integer start;
        private Integer end;
    }

    @Test
    void name2() {
        String content = "I 2love Java I love Python I love Internet";
        String text = "ove";
        //  定义该变量用于记录匹配"love"的元素前面的长度
        int frontLength = 0;

        int textLength = text.length();

        //只要该str字符串中有匹配"love"的元素，才进行以下操作
        while (content.contains(text)) {

            //定义该变量用于记录匹配"love"的元素在当前字符串的位置
            int index = content.indexOf(text);

            //匹配"love"的元素位置等于frontLength加上index；加1为了从1开始计数，更加直观：
            int start = index + frontLength;
            int end = index + frontLength + textLength;

            System.out.println("Start: " + start + "; End: " + end);

            frontLength += (index + textLength);
            //将字符串中匹配"love"元素的前面部分及其本身截取，留下后面的部分
            content = content.substring(index + textLength);
        }
    }
}
