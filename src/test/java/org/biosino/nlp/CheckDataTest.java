package org.biosino.nlp;

import lombok.extern.slf4j.Slf4j;
import org.biosino.nlp.modules.project.service.impl.StatisticsServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class CheckDataTest {

    @Autowired
    private StatisticsServiceImpl statisticsService;

    @Test
    public void unHtmlEscape() {
//        statisticsService.calculationConsistencyByLabel(111L, null, 1196L);
        statisticsService.exportConsistencyToExcel(111L, null, "<EMAIL>");
    }
}

