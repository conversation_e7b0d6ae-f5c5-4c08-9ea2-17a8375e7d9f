package org.biosino.nlp;

import org.biosino.nlp.modules.note.service.impl.NoteTaskServiceImpl;
import org.biosino.nlp.modules.note.vo.PageIdsListVO;
import org.biosino.nlp.modules.project.dto.ArticleListDTO;
import org.biosino.nlp.modules.project.entity.Batch;
import org.biosino.nlp.modules.project.entity.VerifyTask;
import org.biosino.nlp.modules.project.service.BatchService;
import org.biosino.nlp.modules.project.service.StatisticsService;
import org.biosino.nlp.modules.project.service.VerifyTaskService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class BatchTest {
    @Autowired
    private BatchService batchService;
    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private NoteTaskServiceImpl noteTaskService;
    @Autowired
    private VerifyTaskService verifyTaskService;

    @Test
    public void name() {
        statisticsService.annotatedStatisticsJob();
    }

    @Test
    public void getArticleList() {
        ArticleListDTO dto = new ArticleListDTO();
        dto.setPage(1);
        dto.setLimit(10);
        dto.setOrderBy("article_id");
        dto.setIsAsc(false);

        dto.setBatchId(1L);
        dto.setAnnotatorId(55L);
//        dto.setStep(NoteStepEnum.marked.getCode());
        PageIdsListVO vo = batchService.getArticleList(dto);
        /*List<ArticleListVO> records = page.getRecords();
        for (ArticleListVO record : records) {
            System.out.println(record.toString());
        }*/
    }

    @Test
    public void fixSubmitData() {
        Batch batch = batchService.queryBatch(171L);
        VerifyTask verifyTask = new VerifyTask();
        verifyTask.setPreSourceId(236L);
        verifyTaskService.calculation(batch, verifyTask);
    }
}
