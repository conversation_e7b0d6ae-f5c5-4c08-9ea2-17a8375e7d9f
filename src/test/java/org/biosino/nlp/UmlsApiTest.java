package org.biosino.nlp;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.biosino.nlp.common.utils.DateUtils;
import org.biosino.nlp.modules.app.utils.HttpsRequest;
import org.junit.jupiter.api.Test;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-09 17:24
 */
public class UmlsApiTest {

    private static final String UMLS_API_URL = "https://idc.biosino.org/bnlp/utils-api";

    private static final String PLOSP_API_URL = "https://idc.biosino.org/plosp-api/api";

    private static final String TOKEN = "57956b9d8f664b64b044ae29af6a5ff232602cf19b9924c08a7331770c884946";

    /**
     * MD5生成的日期
     */
    @Test
    void md5Date() {
        String today = DateUtils.getMd5Date();
        System.out.println(today);
    }

    /**
     * 测试metamap请求是否正常
     */
    @Test
    void metamapTest() {
        String url = UMLS_API_URL + "/metamap/?text=metadata&retMax=10&pass=" + DateUtils.getMd5Date();
        System.out.println(url);
        System.out.println("============================================================");
        String result = HttpUtil.get(url);
        System.out.println(result);
    }

    /**
     * 根据ConceptID查询详情
     */
    @Test
    void umlsProxyTest() {
        String url = UMLS_API_URL + "/umls-proxy/forward/content/current/CUI/C1708992?pass=" + DateUtils.getMd5Date();
        System.out.println(url);
        System.out.println("============================================================");
        String result = HttpUtil.get(url);
        System.out.println(result);
    }

    /**
     * 测试请求plosp的文章是否正常
     */
    @Test
    void plospTest() {
        // PMC
//        String url = UMLS_API_URL + "/fulltext/article/pmc/PMC6690199?pass=" + DateUtils.getMd5Date();
        // PLOSP
        String url = UMLS_API_URL + "/fulltext/article/plosp/10028244?pass=" + DateUtils.getMd5Date();
        System.out.println(url);
        System.out.println("============================================================");
        String result = HttpUtil.get(url);
        System.out.println(result);
    }

    /**
     * 测试请求参考文献是否正常
     */
    @Test
    void referenceTest() {
        String url = PLOSP_API_URL + "/article/findByPmids.do?pmid=28986331&token=" + TOKEN;
        HttpRequest httpRequest = HttpRequest.get(url).setSSLProtocol("TLS");
        String respJson = httpRequest.execute().body();
        Resp resp = JSONObject.parseObject(respJson, Resp.class);
        if ("success".equals(resp.status)) {
            System.out.println(resp);
        } else {
            System.out.println("请求出错");
            System.out.println(resp);
        }
    }

    @Test
    void test() {
        String url = PLOSP_API_URL + "/article/findByPmids.do?pmid=28986331&token=" + TOKEN;
        JSONObject jsonObject = HttpsRequest.get(url);
        System.out.println(jsonObject);
    }


    @Data
    static class Resp {
        private String status;
        private List<References> articles;

        @Data
        public class References {

            @Indexed(unique = true)
            private Long pmid;

            @Indexed
            private Integer pmcId;

            private String journalTitle;

            private String title;

            private String abs;

            private String year;

            private String volume;

            private String issue;

            private String page;

            private String keyword;

            private String author;

            @Indexed
            private String doi;
        }
    }
}
