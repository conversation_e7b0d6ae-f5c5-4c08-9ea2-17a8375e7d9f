连接biosino堡垒机 -- mgmt-bnlp-45917
war包挂载目录/hostdir/bnlp-webapps
附件挂载目录/hostdir/bnlp-data3

# 查询bnlp相关pod
kubectl get po

# 重启tomcat（已集成Jenkins持续集成，无需手动重启）
kubectl delete po tomcat-bnlp-f967d4475-2kmw6

# 查看tomcat日志
kubectl logs -f tomcat-bnlp-f967d4475-2kmw6
kubectl logs -f tomcat-bnlp-f967d4475-2kmw6 | grep -i 接擰 es_index_queue
kubectl logs -f tomcat-bnlp-f967d4475-2kmw6 | grep es_index_queue
kubectl logs -f tomcat-bnlp-f967d4475-2kmw6 | grep _queue

# 查看描述
kubectl describe po tomcat-bnlp-f967d4475-2kmw6

#mysql
    #mysql备份数据到宿主机/home/<USER>/bnlp/mysql路径中
    kubectl exec mysql-0 -- mysqldump -R -E -uroot -p'bnlp@2021' bnlp3 --default-character-set=UTF8 > /home/<USER>/bnlp/mysql/bnlp3_20250709.sql

    #进入控制台
    kubectl exec mysql-0 -it bash

    mysql -uroot -pbnlp@2021

    show databases;
    use bnlp3;
    ALTER TABLE `t_note` ADD COLUMN `words_count` int(11) NOT NULL DEFAULT '0' COMMENT '文档字数' AFTER `document_id`;


#mongodb
    #备份mongodb数据
    kubectl exec mongo-0 -- mongodump -h localhost -u root -p 'bnlp@2021' -d bnlp3 --authenticationDatabase admin -o /backup/db
    #拷贝mongodb容器中/backup/db目录中的内容到宿主机的/home/<USER>/bnlp/mongodb目录中
    kubectl cp mongo-0:/backup/db /home/<USER>/bnlp/mongodb

    #进入mongodb容器终端
    kubectl exec mongo-0 -it bash
    #终端登录mogodb
    mongo -u root -p bnlp@2021 --authenticationDatabase admin

    #查看数据库
    show dbs;
    #切换数据库
    use bnlp3;
    #查看集合
    show collections;

    #数据查询
    db.m_entity.distinct('import_log_id',{ $and: [ { "source": 2 }, { "note_id": 8489 } ] });
    #删除索引
    db.getCollection("m_entity").dropIndex("create_time")
    #创建索引
    db.getCollection("m_entity").createIndex({
        create_time: NumberInt("-1")
    }, {
        name: "create_time"
    })


#nginx 进入控制台
    kubectl exec nginx-proxy-689d548cb9-bzl8w -it bash