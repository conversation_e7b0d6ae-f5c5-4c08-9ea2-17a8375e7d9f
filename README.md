# BNLP 3.0 (自然语言标注系统)

## 项目简介

项目甲方负责人：庄心昊

项目乙方负责人：罗瑞金

### GIT信息
后端Git：http://dev.biosino.org/git/NLP/bnlp-server  
前端Git：https://dev.biosino.org/git/NLP/bnlp-app

### 相关资料

需求规格说明书：https://gpji0d3vts.feishu.cn/docx/LPQKd8IpzoBlQZxgnl2c1IeGn5w  
运维需求文档：https://gpji0d3vts.feishu.cn/docx/Mm24dYRoyo6Kk4xMHJJc6tdLngb

超级管理员：admin / YZmVX39[=(FZ

## 项目运维
### 部署服务
先把文件全部拷贝到/home/<USER>/bnlp

1. 部署前端  
```
cd /hostdir/proxy-nginx/
rm -rf bnlp-v3
unzip /home/<USER>/bnlp/bnlp-v3.zip

kubectl exec -it nginx-proxy-689d548cb9-bzl8w bash
cd /usr/share/nginx/html/
rm -rf bnlp-v3
exit
```

2. 部署后端  
```
rm -rf /hostdir/bnlp-webapps/bnlp-api-v3*
mv /home/<USER>/bnlp/bnlp-api-v3.war /hostdir/bnlp-webapps/

kubectl logs -f tomcat-bnlp-7f7648d8c5-bc6nb

数据库
kubectl exec mysql-0 -it bash

mysql -hlocalhost -uroot -p
数据库：bnlp3
密码： bnlp@2021
```

### 数据库运维
备份MySQL
```
rm -rf /home/<USER>/backup/*
kubectl exec -it mysql-0 -- bash

rm -rf ./backup/*

mysqldump -h127.0.0.1 -P3306 -R -E -u root -p --default-character-set=utf8 bnlp3 >  ./backup/bnlp3.sql
密码：bnlp@2021
exit

kubectl cp mysql-0:backup/bnlp3.sql /home/<USER>/backup/bnlp3.sql

其他命令
mysqldump -h127.0.0.1 -P3306 -R -E -u root -p --default-character-set=utf8 bfms > ./bfms.sql

tar -zcvf bnlp-sql.gz bnlp.sql
tar -zcvf bfms-sql.gz bfms.sql
```

备份MongoDB
```
kubectl exec -it mongo-0 -- bash

cd backup
rm -rf /backup/*

mongodump --host mongo.bnlp --port 27017 -u root -p bnlp@2021 -d bnlp3 --authenticationDatabase admin --out /backup

tar -zcvf bnlp-mongo.gz bnlp3
exit
kubectl cp mongo-0:backup/bnlp-mongo.gz /home/<USER>/backup/bnlp-mongo.gz


旧：
kubectl exec -it mongo-0 -- bash
mongodump --host 127.0.0.1 --port 27017 -u root -p bnlp@2021 -d bnlp --gzip --authenticationDatabase admin --out /backup

mongodump --host 127.0.0.1 --port 27017 -u root -p bnlp@2021 -d bfms --gzip --authenticationDatabase admin --out /backup

tar -zcvf bnlp-mongo.gz bnlp3
tar -zcvf bfms-mongo.gz bfms

kubectl cp mongo-0:backup/bnlp-mongo.gz /home/<USER>/backup/bnlp-mongo.gz

kubectl cp mongo-0:backup/bfms-mongo.gz /home/<USER>/upload/database/back/2022-9-8/bfms-mongo.gz
```

还原到***************阿里云开发测试数据库
```
/data/back-data/bnlp

docker exec -it mysql bash

rm -rf /backup/bnlp3.sql

exit

docker cp /data/back-data/bnlp/bnlp3.sql  mysql:/backup/

docker exec -it mysql bash

mysql -hlocalhost -P3306 --default-character-set=utf8 -uroot -p bnlp3 < /backup/bnlp3.sql

密码：Lfgzs@2021

// 重置数据库密码为123456（使用navicat操作）
UPDATE `bnlp3`.`sys_user` SET `password` = 'dc16ac99e04f8d34feffd4686d5993f22a9366ee980dd7c99946a36eb02d4f8b', `salt` = 'kNWZcgwiHD3dQDAEM08j'

MongoDB
// 删除旧数据
docker exec -it mongo bash
rm -rf /backup/*
exit

// 拷贝新数据
docker cp /data/back-data/bnlp/bnlp-mongo.gz  mongo:/backup/
docker exec -it mongo bash
cd /backup
tar -zxvf bnlp-mongo.gz

清空数据库后执行
mongorestore -h localhost -p 27017 --authenticationDatabase admin -u admin -p admin2021 -d bnlp3 --dir /backup/bnlp3
=还原数据库结束=
```
```
其他命令
登录mogodb  
mongo -p 27017 -u admin -p --authenticationDatabase admin
密码：admin2021

查看数据库
show dbs
切换数据库
use bnlp-prod
查看集合
show collections
删除库
db.dropDatabase()

BFMS数据库
mongorestore -h localhost -p 27017 --authenticationDatabase admin -u admin -p admin2021 -d bfms --gzip --dir /backup/bfms
```
