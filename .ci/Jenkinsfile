pipeline {

  agent any



    environment {
        DOCKER_CREDENTIAL_ID = 'dev-biosino-dockerhub-builder'
        KUBECONFIG_CREDENTIAL_ID = 'kubecfg-bnlp-admin'
        REGISTRY = 'dev.biosino.org'
        DOCKERHUB_NAMESPACE = 'biosino'
        APP_NAME = 'tomcat-bnlp'
        GITLAB_ADDRESS = 'dev.biosino.org/git'
        WAR_PKG = ""
        BUILD_DATE = sh(script: "date +%Y%m%d", returnStdout: true)
        PROJ_ROOTFS = '/zjbdp/nservice/bnlp/bnlp-webapps'
    }


    stages {

        stage ('checkout scm') {
            steps {
                checkout(scm)
            }
        }

        stage('build'){
            steps {
                echo "starting deploy....."
                sh "mvn clean package -Dmaven.test.skip=true -P prod"
            }
        }

        stage('replacs && restart'){

            steps {
                sh "cp target/bnlp-api-v3.war ${PROJ_ROOTFS}/bnlp-api-v3.war.v$BUILD_ID-$BUILD_DATE"
                sh "rm -rf ${PROJ_ROOTFS}/bnlp-api-v3.war"
                sh "cp target/bnlp-api-v3.war ${PROJ_ROOTFS}/bnlp-api-v3.war"

                withKubeConfig( [ credentialsId: "${KUBECONFIG_CREDENTIAL_ID}" ] ) {
                    sh "kubectl rollout restart deployment --selector=type=webapp-bnlp -n bnlp"
                }
            }
        }

    }
}
